<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatREGKAN extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("UTC");
    $this->load->model('IHSModel');
    $this->load->model('SatuSehatModel');
    $this->load->model('SatuSehatREGKANModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ];
    // $postDataArray = [
    //   'client_id' => 'KsRtseuJpe4aCTWIcmAVBDdhgUuXGNCFijmfflaWbCNSvhrA','client_secret' => 'aMAlbyP6JVgn6SWf2j3ZpTNozmPfvhUtLr1PUqOkoK6qqiSVdJ5w4Ub2z1AdDM00'
    // ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';
    // $url = 'https://api-satusehat-stg.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  public function regkan()
  {
    $listPasienRegKan = $this->SatuSehatREGKANModel->listPasienRegKan();
    foreach($listPasienRegKan as $ls){
      $regkanAllIn = $this->SatuSehatREGKANModel->regkanAllIn($ls['NORM']);
      $dariKun1 = 0;
      if(!empty($regkanAllIn)){

        foreach($regkanAllIn as $data){
          if($data['JENISS'] == 'KUNJUNGAN 1'){
            $dariKun1 = 1;
            $dataSimpanJenis1 = array(
              'id_condition'              => isset($data['ID_CONDITION']) ? $data['ID_CONDITION'] : "",
              'id_encounter'              => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'             => $data['ID_IHS_PASIEN'],
              'nm_pasien'                 => $data['NAMA_PASIEN'],
              'kode_diagnosa_masuk'       => $data['DIAGNOSA_MASUK'],
              'deskripsi_diagnosa_masuk'  => $data['DESKRIPSI_DIAGNOSA_MASUK'],
              'tgl_jam'                   => $data['TGL_JAM'],
              'jenis_pengiriman'          => 2,
              'jenis_condition'           => 1,
            );
            
            echo $ls['NORM'] . ' - 1<br>';
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis1);

          }elseif($data['JENISS'] == 'KUNJUNGAN 2'){

            $dataSimpanJenis2 = array(
              'id_encounter'              => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'             => $data['ID_IHS_PASIEN'],
              'nm_pasien'                 => $data['NAMA_PASIEN'],
              'kode_diagnosa_masuk'       => $data['KODE_ICD10'],
              'deskripsi_diagnosa_masuk'  => $data['DESK_ICD10'],
              'kode_basis_diagnosis'      => $data['BASIS_DIAGNOSIS'],
              'deskripsi_basis_diagnosis' => $data['DESKRIPSI_BASIS_DIAGNOSIS'],
              'tgl_jam'                   => $data['TGL_JAM'],
              'jenis_pengiriman'          => 1,
              'jenis_condition'           => 2,
            );
            
            echo $ls['NORM'] . ' - 2<br>';

            $dataSimpanJenis3 = array(
              'kode_topografi'           => $data['KODE_TOPOGRAFI'],
              'desk_topografi'           => $data['TOPOGRAFI_DES'],
              'kode_morfologi'           => $data['KODE_MORFOLOGI'],
              'desk_morfologi'           => $data['MORFOLOGI_DES'],
              'kode_perilaku_tumor'      => $data['KODE_PERILAKU_TUMOR'],
              'desk_perilaku_tumor'      => $data['DESK_PERILAKU_TUMOR'],
              'kode_grade_tumor'         => $data['KODE_GRADE'],
              'desk_grade_tumor'         => $data['DESK_GRADE'],
              'id_ihs_pasien'            => $data['ID_IHS_PASIEN'],
              'nm_pasien'                => $data['NAMA_PASIEN'],
              'id_encounter'             => $data['ID_ENCOUNTER'],
              'tgl_jam'                  => $data['TGL_JAM'],
              'jenis_pengiriman'         => 1,
              'jenis_condition'          => 3,
            );
            echo $ls['NORM'] . ' - 2A<br>';

            $dataSimpanJenis4 = array(
              'tnm_t'               => $data['TNM_T'],
              'tnm_n'               => $data['TNM_N'],
              'tnm_m'               => $data['TNM_M'],
              'kode_stadium'        => $data['STADIUM'],
              'desk_stadium'        => $data['DESKRIPSI_STADIUM'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'nm_pasien'           => $data['NAMA_PASIEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'tgl_jam'             => $data['TGL_JAM'],
              'jenis_pengiriman'    => 1,
              'jenis_condition'     => 4,
            );
            echo $ls['NORM'] . ' - 2B<br>';

            $dataSimpanJenis5 = array(
              'kode_perluasan_tumor'   => $data['KODE_PERLUASAN_TUMOR'],
              'desk_perluasan_tumor'   => $data['DESK_PERLUASAN'],
              'kode_lateralitas_tumor' => $data['KODE_LATERALITAS_TUMOR'],
              'desk_lateralitas_tumor' => $data['DESK_LATERALITAS'],
              'kode_metastasis_jauh_1' => $data['KODE_METASTASIS_JAUH_1'],
              'desk_metastasis_jauh_1' => $data['DESK_METASUS_JAUH_1'],
              'kode_metastasis_jauh_2' => $data['KODE_METASTASIS_JAUH_2'],
              'desk_metastasis_jauh_2' => $data['DESK_METASTASIS_JAUH_2'],
              'kode_metastasis_jauh_3' => $data['KODE_METASTASIS_JAUH_3'],
              'desk_metastasis_jauh_3' => $data['DESK_METASTASIS_JAUH_3'],
              'kode_metastasis_jauh_4' => $data['KODE_METASTASIS_JAUH_4'],
              'desk_metastasis_jauh_4' => $data['DESK_METASTASIS_JAUH_4'],
              'id_ihs_pasien'          => $data['ID_IHS_PASIEN'],
              'nm_pasien'              => $data['NAMA_PASIEN'],
              'id_encounter'           => $data['ID_ENCOUNTER'],
              'tgl_jam'                => $data['TGL_JAM'],
              'jenis_pengiriman'       => 1,
              'jenis_condition'        => 5,
            );
            echo $ls['NORM'] . ' - 2C<br>';

            $dataSimpanPatchEncounter = array(
              'nopen'               => $data['NOPEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'jenis'               => 1,
              'jenis_encounter'     => 1,
              'status'              => 1,
            );
            echo $ls['NORM'] . ' - PATCH<br>';

            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis2);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis3);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis4);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis5);
            $this->db->insert('db_regkan.tb_patch_eoc', $dataSimpanPatchEncounter);

          }elseif($data['JENISS'] == 'KUNJUNGAN 3'){
            $dataSimpanTindakan = array(
              'id_encounter'               => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'              => $data['ID_IHS_PASIEN'],
              'nm_pasien'                  => $data['NAMA_PASIEN'],
              'id_ihs_practitioner'        => $data['ID_IHS_DOKTER'],
              'nm_practitioner'            => $data['NM_DOKTER'],
              'kode_terapi_1'              => $data['terapi_institusi_pelapor_1'],
              'desk_terapi_1'              => $data['desk_institusi_pelapor_1'],
              'kode_terapi_2'              => $data['terapi_institusi_pelapor_2'],
              'desk_terapi_2'              => $data['desk_institusi_pelapor_2'],
              'kode_terapi_3'              => $data['terapi_institusi_pelapor_3'],
              'desk_terapi_3'              => $data['desk_institusi_pelapor_3'],
              'tgl_jam'                    => $data['TGL_JAM_RENCANA_TINDAKAN_TERAPI'],
              'jenis_pengiriman'           => 1,
            );
            echo $ls['NORM'] . ' - 3<br>';

            $dataSimpanPatchEncounter = array(
              'nopen'               => $data['NOPEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'jenis'               => 1,
              'jenis_encounter'     => 2,
              'status'              => 1,
            );
            echo $ls['NORM'] . ' - PATCH<br>';
            $this->db->insert('db_regkan.tb_patch_eoc', $dataSimpanPatchEncounter);
            $this->db->insert('db_regkan.tb_procedure', $dataSimpanTindakan);
          }

          $this->db->set('JENIS_SIAP_KIRIM', '2', FALSE);
          $this->db->where('NORM', $ls['NORM']);
          $this->db->update('db_regkan.tb_pasienbaru');
        }

        if($dariKun1 == 1){
          $dataEpisodeOfCareKun1 = array(
            'nopen'             => $data['NOPEN'],
            'id_ihs_pasien'     => $data['ID_IHS_PASIEN'],
            'nm_pasien'         => $data['NAMA_PASIEN'],
            'tgl_jam'           => $data['TGL_JAM'],
            'jenis_pengiriman'  => 1,
            'put_encounter'     => 1,
            'jenis_pasien'      => 1,
            'status'            => 1,
            );
          print_r($dataEpisodeOfCareKun1);
          echo '<br>';

          $this->db->insert('db_regkan.tb_episodeofcare', $dataEpisodeOfCareKun1);
        }else{
          $dataEpisodeOfCareKun23 = array(
            'nopen'             => $data['NOPEN'],
            'id_ihs_pasien'     => $data['ID_IHS_PASIEN'],
            'nm_pasien'         => $data['NAMA_PASIEN'],
            'tgl_jam'           => $data['TGL_JAM'],
            'jenis_pengiriman'  => 1,
            'put_encounter'     => 1,
            'jenis_pasien'      => 2,
            'status'            => 1,
            );
          print_r($dataEpisodeOfCareKun23);
          echo '<br>';

          $this->db->insert('db_regkan.tb_episodeofcare', $dataEpisodeOfCareKun23);
        }

      }else{
        $this->db->set('JENIS_SIAP_KIRIM', '3', FALSE);
        $this->db->where('NORM', $ls['NORM']);
        $this->db->update('db_regkan.tb_pasienbaru');
      }
    }
  }

  public function regkan_2022()
  {
    $listPasienRegKan = $this->SatuSehatREGKANModel->listPasienRegKan_2022();
    foreach($listPasienRegKan as $ls){
      $regkanAllIn = $this->SatuSehatREGKANModel->regkanAllIn($ls['mr']);
      $dariKun1 = 0;
      if(!empty($regkanAllIn)){

        foreach($regkanAllIn as $data){
          if($data['JENISS'] == 'KUNJUNGAN 1'){
            $dariKun1 = 1;
            $dataSimpanJenis1 = array(
              'id_condition'              => isset($data['ID_CONDITION']) ? $data['ID_CONDITION'] : "",
              'id_encounter'              => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'             => $data['ID_IHS_PASIEN'],
              'nm_pasien'                 => $data['NAMA_PASIEN'],
              'kode_diagnosa_masuk'       => $data['DIAGNOSA_MASUK'],
              'deskripsi_diagnosa_masuk'  => $data['DESKRIPSI_DIAGNOSA_MASUK'],
              'tgl_jam'                   => $data['TGL_JAM'],
              'jenis_pengiriman'          => 2,
              'jenis_condition'           => 1,
            );
            
            echo $ls['mr'] . ' - 1<br>';
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis1);

          }elseif($data['JENISS'] == 'KUNJUNGAN 2'){

            $dataSimpanJenis2 = array(
              'id_encounter'              => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'             => $data['ID_IHS_PASIEN'],
              'nm_pasien'                 => $data['NAMA_PASIEN'],
              'kode_diagnosa_masuk'       => $data['KODE_ICD10'],
              'deskripsi_diagnosa_masuk'  => $data['DESK_ICD10'],
              'kode_basis_diagnosis'      => $data['BASIS_DIAGNOSIS'],
              'deskripsi_basis_diagnosis' => $data['DESKRIPSI_BASIS_DIAGNOSIS'],
              'tgl_jam'                   => $data['TGL_JAM'],
              'jenis_pengiriman'          => 1,
              'jenis_condition'           => 2,
            );
            
            echo $ls['mr'] . ' - 2<br>';

            $dataSimpanJenis3 = array(
              'kode_topografi'           => $data['KODE_TOPOGRAFI'],
              'desk_topografi'           => $data['TOPOGRAFI_DES'],
              'kode_morfologi'           => $data['KODE_MORFOLOGI'],
              'desk_morfologi'           => $data['MORFOLOGI_DES'],
              'kode_perilaku_tumor'      => $data['KODE_PERILAKU_TUMOR'],
              'desk_perilaku_tumor'      => $data['DESK_PERILAKU_TUMOR'],
              'kode_grade_tumor'         => $data['KODE_GRADE'],
              'desk_grade_tumor'         => $data['DESK_GRADE'],
              'id_ihs_pasien'            => $data['ID_IHS_PASIEN'],
              'nm_pasien'                => $data['NAMA_PASIEN'],
              'id_encounter'             => $data['ID_ENCOUNTER'],
              'tgl_jam'                  => $data['TGL_JAM'],
              'jenis_pengiriman'         => 1,
              'jenis_condition'          => 3,
            );
            echo $ls['mr'] . ' - 2A<br>';

            $dataSimpanJenis4 = array(
              'tnm_t'               => $data['TNM_T'],
              'tnm_n'               => $data['TNM_N'],
              'tnm_m'               => $data['TNM_M'],
              'kode_stadium'        => $data['STADIUM'],
              'desk_stadium'        => $data['DESKRIPSI_STADIUM'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'nm_pasien'           => $data['NAMA_PASIEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'tgl_jam'             => $data['TGL_JAM'],
              'jenis_pengiriman'    => 1,
              'jenis_condition'     => 4,
            );
            echo $ls['mr'] . ' - 2B<br>';

            $dataSimpanJenis5 = array(
              'kode_perluasan_tumor'   => $data['KODE_PERLUASAN_TUMOR'],
              'desk_perluasan_tumor'   => $data['DESK_PERLUASAN'],
              'kode_lateralitas_tumor' => $data['KODE_LATERALITAS_TUMOR'],
              'desk_lateralitas_tumor' => $data['DESK_LATERALITAS'],
              'kode_metastasis_jauh_1' => $data['KODE_METASTASIS_JAUH_1'],
              'desk_metastasis_jauh_1' => $data['DESK_METASUS_JAUH_1'],
              'kode_metastasis_jauh_2' => $data['KODE_METASTASIS_JAUH_2'],
              'desk_metastasis_jauh_2' => $data['DESK_METASTASIS_JAUH_2'],
              'kode_metastasis_jauh_3' => $data['KODE_METASTASIS_JAUH_3'],
              'desk_metastasis_jauh_3' => $data['DESK_METASTASIS_JAUH_3'],
              'kode_metastasis_jauh_4' => $data['KODE_METASTASIS_JAUH_4'],
              'desk_metastasis_jauh_4' => $data['DESK_METASTASIS_JAUH_4'],
              'id_ihs_pasien'          => $data['ID_IHS_PASIEN'],
              'nm_pasien'              => $data['NAMA_PASIEN'],
              'id_encounter'           => $data['ID_ENCOUNTER'],
              'tgl_jam'                => $data['TGL_JAM'],
              'jenis_pengiriman'       => 1,
              'jenis_condition'        => 5,
            );
            echo $ls['mr'] . ' - 2C<br>';

            $dataSimpanPatchEncounter = array(
              'nopen'               => $data['NOPEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'jenis'               => 1,
              'jenis_encounter'     => 1,
              'status'              => 1,
            );
            echo $ls['mr'] . ' - PATCH<br>';

            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis2);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis3);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis4);
            $this->db->insert('db_regkan.tb_condition', $dataSimpanJenis5);
            $this->db->insert('db_regkan.tb_patch_eoc', $dataSimpanPatchEncounter);

          }elseif($data['JENISS'] == 'KUNJUNGAN 3'){
            $dataSimpanTindakan = array(
              'id_encounter'               => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'              => $data['ID_IHS_PASIEN'],
              'nm_pasien'                  => $data['NAMA_PASIEN'],
              'id_ihs_practitioner'        => $data['ID_IHS_DOKTER'],
              'nm_practitioner'            => $data['NM_DOKTER'],
              'kode_terapi_1'              => $data['terapi_institusi_pelapor_1'],
              'desk_terapi_1'              => $data['desk_institusi_pelapor_1'],
              'kode_terapi_2'              => $data['terapi_institusi_pelapor_2'],
              'desk_terapi_2'              => $data['desk_institusi_pelapor_2'],
              'kode_terapi_3'              => $data['terapi_institusi_pelapor_3'],
              'desk_terapi_3'              => $data['desk_institusi_pelapor_3'],
              'tgl_jam'                    => $data['TGL_JAM_RENCANA_TINDAKAN_TERAPI'],
              'jenis_pengiriman'           => 1,
            );
            echo $ls['mr'] . ' - 3<br>';

            $dataSimpanPatchEncounter = array(
              'nopen'               => $data['NOPEN'],
              'id_encounter'        => $data['ID_ENCOUNTER'],
              'id_ihs_pasien'       => $data['ID_IHS_PASIEN'],
              'jenis'               => 1,
              'jenis_encounter'     => 2,
              'status'              => 1,
            );
            echo $ls['mr'] . ' - PATCH<br>';
            $this->db->insert('db_regkan.tb_patch_eoc', $dataSimpanPatchEncounter);
            $this->db->insert('db_regkan.tb_procedure', $dataSimpanTindakan);
          }

          $this->db->set('jenis_kirim', '2', FALSE);
          $this->db->where('mr', $ls['mr']);
          $this->db->update('db_regkan.tb_modul_regkan');
        }

        if($dariKun1 == 1){
          $dataEpisodeOfCareKun1 = array(
            'nopen'             => $data['NOPEN'],
            'id_ihs_pasien'     => $data['ID_IHS_PASIEN'],
            'nm_pasien'         => $data['NAMA_PASIEN'],
            'tgl_jam'           => $data['TGL_JAM'],
            'jenis_pengiriman'  => 1,
            'put_encounter'     => 1,
            'jenis_pasien'      => 1,
            'status'            => 1,
            );
          print_r($dataEpisodeOfCareKun1);
          echo '<br>';

          $this->db->insert('db_regkan.tb_episodeofcare', $dataEpisodeOfCareKun1);
        }else{
          $dataEpisodeOfCareKun23 = array(
            'nopen'             => $data['NOPEN'],
            'id_ihs_pasien'     => $data['ID_IHS_PASIEN'],
            'nm_pasien'         => $data['NAMA_PASIEN'],
            'tgl_jam'           => $data['TGL_JAM'],
            'jenis_pengiriman'  => 1,
            'put_encounter'     => 1,
            'jenis_pasien'      => 2,
            'status'            => 1,
            );
          print_r($dataEpisodeOfCareKun23);
          echo '<br>';

          $this->db->insert('db_regkan.tb_episodeofcare', $dataEpisodeOfCareKun23);
        }

      }else{
        $this->db->set('jenis_kirim', '3', FALSE);
        $this->db->where('mr', $ls['mr']);
        $this->db->update('db_regkan.tb_modul_regkan');
      }
    }
  }

  public function kirimCondition()
  {
    $token = $this->getToken();
    $listCondition = $this->SatuSehatREGKANModel->listCondition();
    foreach($listCondition as $lc){

      $namaPasien = trim(preg_replace('/\s+/', ' ', $lc['nm_pasien']));

      $datereg = date_create_from_format('Y-m-d H:i:s', $lc['tgl_jam']);
      $tglReg = $datereg->format(DATE_ATOM);

      if($lc['jenis_condition'] == 2){
        echo 'DIAGNOSA AWAL (KUNJUNGAN 2 / KONFIRMASI) <br>';
        $data = '{
                  "resourceType": "Condition",
                  "clinicalStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                              "code": "active",
                              "display": "Active"
                          }
                      ]
                  },
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                  "code": "encounter-diagnosis",
                                  "display": "Encounter Diagnosis"
                              }
                          ]
                      }
                  ],
                  "verificationStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
                              "code": "confirmed",
                              "display": "Confirmed"
                          }
                      ]
                  },
                  "code": {
                      "coding": [
                          {
                              "system": "http://hl7.org/fhir/sid/icd-10",
                              "code": "'.$lc['kode_diagnosa_masuk'].'",
                              "display": "'.$lc['deskripsi_diagnosa_masuk'].'"
                          }
                      ]
                  },
                  "evidence": [
                      {
                          "code": [
                              {
                                  "coding": [
                                      {
                                          "system": "'.$lc['system_basis_diagnosis'].'",
                                          "code": "'.$lc['kode_basis_diagnosis'].'",
                                          "display": "'.$lc['deskripsi_basis_diagnosis'].'"
                                      }
                                  ]
                              }
                          ]
                      }
                  ],
                  "subject": {
                      "reference": "Patient/'.$lc['id_ihs_pasien'].'",
                      "display": "'.$namaPasien.'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$lc['id_encounter'].'"
                  },
                  "onsetDateTime": "'.$tglReg.'",
                  "recordedDate": "'.$tglReg.'",
                  "note": [
                      {
                          "text": "'.$lc['deskripsi_diagnosa_masuk'].'"
                      }
                  ]
              }';
            echo '<pre>' . $data;
            echo '<br>-------------------------------------------------------------------------------------------------------------------</br>';

            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition';

            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
              "Content-Type: application/json",
              "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            if($httpcode == "201"){
              $dataUbahBasisDiagnosis = array(
                'id_condition'      => $res->id,
                'jenis_pengiriman'  => 2,
              );
            }else{
              $dataUbahBasisDiagnosis = array(
                'jenis_pengiriman'  => 3,
              );
            }

            $this->db->where('tb_condition.id_encounter', $lc['id_encounter']);
            $this->db->where('tb_condition.jenis_condition', 2);
            $this->db->update('db_regkan.tb_condition', $dataUbahBasisDiagnosis);

            $dataSimpanBasisDiagnosis = array(
              'id_encounter'      => $lc['id_encounter'],
              'id_condition'      => $res->id,
              'log'               => $data,
              'response'          => $response,
              'http_code'         => $httpcode,
              'jenis_condition'   => 2,
            );
            $this->db->insert('db_regkan.tb_log_condition', $dataSimpanBasisDiagnosis);

      }elseif($lc['jenis_condition'] == 3){
        echo 'TOPOGRAFI, MORFOLOGI, PERILAKU, GRADE (KUNJUNGAN 2) <br>';
        $data = '{
                  "resourceType": "Condition",
                  "clinicalStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                              "code": "active",
                              "display": "Active"
                          }
                      ]
                  },
                  "verificationStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
                              "code": "confirmed"
                          }
                      ]
                  }, 
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                  "code": "problem-list-item",
                                  "display": "Problem List Item"
                              }
                          ]
                      }
                  ],
                  "code": {
                      "coding": [
                          {
                              "system": "http://terminology.kemkes.go.id/CodeSystem/icd-o-topography",
                              "code": "'.$lc['kode_topografi'].'",
                              "display": "'.$lc['desk_topografi'].'"
                          },
                          {
                              "system": "http://terminology.kemkes.go.id/CodeSystem/icd-o-morphology",
                              "code": "'.$lc['kode_morfologi'].'",
                              "display": "'.$lc['desk_morfologi'].'"
                          },
                          {
                              "system": "'.$lc['system_perilaku'].'",
                              "code": "'.$lc['kode_perilaku_tumor'].'",
                              "display": "'.$lc['desk_perilaku_tumor'].'"
                          }
                      ]
                  },
                  "stage": [
                      {
                          "summary": {
                              "coding": [
                                  {
                                      "system": "'.$lc['system_grade'].'",
                                      "code": "'.$lc['kode_grade_tumor'].'",
                                      "display": "'.$lc['desk_grade_tumor'].'"
                                  }
                              ]
                          },
                          "type": {
                              "coding": [
                                  {
                                      "system": "http://snomed.info/sct",
                                      "code": "370114008",
                                      "display": "Histological grades"
                                  }
                              ]
                          }
                      }
                  ],
                  "subject": {
                      "reference": "Patient/'.$lc['id_ihs_pasien'].'",
                      "display": "'.$namaPasien.'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$lc['id_encounter'].'"
                  },
                  "recordedDate": "'.$tglReg.'"
              }';
            echo '<pre>' . $data;
            echo '<br>-------------------------------------------------------------------------------------------------------------------</br>';

            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition';

            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
              "Content-Type: application/json",
              "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            if($httpcode == "201"){
              $dataUbahTopMorPerGra = array(
                'id_condition'      => $res->id,
                'jenis_pengiriman'  => 2,
              );
            }else{
              $dataUbahTopMorPerGra = array(
                'jenis_pengiriman'  => 3,
              );
            }

            $this->db->where('tb_condition.id_encounter', $lc['id_encounter']);
            $this->db->where('tb_condition.jenis_condition', 3);
            $this->db->update('db_regkan.tb_condition', $dataUbahTopMorPerGra);

            $dataSimpanTopMorPerGra = array(
              'id_encounter'      => $lc['id_encounter'],
              'id_condition'      => $res->id,
              'log'               => $data,
              'response'          => $response,
              'http_code'         => $httpcode,
              'jenis_condition'   => 3,
            );
            $this->db->insert('db_regkan.tb_log_condition', $dataSimpanTopMorPerGra);

      }elseif($lc['jenis_condition'] == 4){
        echo 'TNM, STADIUM (KUNJUNGAN 2) <br>';
        $data = '{
                    "resourceType": "Condition",
                    "clinicalStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                "code": "active",
                                "display": "Active"
                            }
                        ]
                    },
                    "verificationStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
                                "code": "confirmed"
                            }
                        ]
                    },
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                    "code": "problem-list-item",
                                    "display": "Problem List Item"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "'.$lc['system_tnm_t'].'",
                                "code": "'.$lc['tnm_t'].'",
                                "display": "'.$lc['tnm_t'].'"
                            },
                            {
                                "system": "'.$lc['system_tnm_n'].'",
                                "code": "'.$lc['tnm_n'].'",
                                "display": "'.$lc['tnm_n'].'"
                            },
                            {
                                "system": "'.$lc['system_tnm_m'].'",
                                "code": "'.$lc['tnm_m'].'",
                                "display": "'.$lc['tnm_m'].'"
                            }
                        ]
                    },
                    "stage": [
                        {
                            "summary": {
                                "coding": [
                                    {
                                        "system": "'.$lc['system_stadium'].'",
                                        "code": "'.$lc['kode_stadium'].'",
                                        "display": "'.$lc['desk_stadium'].'"
                                    }
                                ]
                            },
                            "type": {
                                "coding": [
                                    {
                                        "system": "http://snomed.info/sct",
                                        "code": "254292007",
                                        "display": "Tumor staging"
                                    }
                                ]
                            }
                        }
                    ],
                    "subject": {
                        "reference": "Patient/'.$lc['id_ihs_pasien'].'",
                        "display": "'.$namaPasien.'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$lc['id_encounter'].'"
                    },
                    "recordedDate": "'.$tglReg.'"
                }';
            echo '<pre>' . $data;
            echo '<br>-------------------------------------------------------------------------------------------------------------------</br>';

            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition';

            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
              "Content-Type: application/json",
              "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            if($httpcode == "201"){
              $dataUbahTnmStadium = array(
                'id_condition'      => $res->id,
                'jenis_pengiriman'  => 2,
              );
            }else{
              $dataUbahTnmStadium = array(
                'jenis_pengiriman'  => 3,
              );
            }

            $this->db->where('tb_condition.id_encounter', $lc['id_encounter']);
            $this->db->where('tb_condition.jenis_condition', 4);
            $this->db->update('db_regkan.tb_condition', $dataUbahTnmStadium);

            $dataSimpanTnmStadium = array(
              'id_encounter'      => $lc['id_encounter'],
              'id_condition'      => $res->id,
              'log'               => $data,
              'response'          => $response,
              'http_code'         => $httpcode,
              'jenis_condition'   => 4,
            );
            $this->db->insert('db_regkan.tb_log_condition', $dataSimpanTnmStadium);

      }elseif($lc['jenis_condition'] == 5){
        echo 'Perluasan, Lateralitas, Metastasis (KUNJUNGAN 2) <br>';
        $data = '{
                  "resourceType": "Condition",
                  "clinicalStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                              "code": "active",
                              "display": "Active"
                          }
                      ]
                  },
                  "verificationStatus": {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
                              "code": "confirmed"
                          }
                      ]
                  },
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                  "code": "problem-list-item",
                                  "display": "Problem List Item"
                              }
                          ]
                      }
                  ],
                  "code": {
                      "coding": [
                          {
                              "system": "'.$lc['system_perluasan'].'",
                              "code": "'.$lc['kode_perluasan_tumor'].'",
                              "display": "'.$lc['desk_perluasan_tumor'].'"
                          }
                      ]
                  },
                  "bodySite": [
                      {
                          "coding": [
                              {
                                  "system": "'.$lc['system_lateralitas'].'",
                                  "code": "'.$lc['kode_lateralitas_tumor'].'",
                                  "display": "'.$lc['desk_lateralitas_tumor'].'"
                              },';

                              if($lc['kode_metastasis_jauh_1'] == 'CB000001' || $lc['kode_metastasis_jauh_1'] == 'CB000002'){
                                $data .='{
                                  "system": "'.$lc['system_metas1'].'",
                                  "code": "'.$lc['kode_metastasis_jauh_1'].'",
                                  "display": "'.$lc['desk_metastasis_jauh_1'].'"
                                }';
                              }else{
                                $data .='{
                                  "system": "'.$lc['system_metas1'].'",
                                  "code": "'.$lc['kode_metastasis_jauh_1'].'",
                                  "display": "'.$lc['desk_metastasis_jauh_1'].'"
                                },';
                                if($lc['kode_metastasis_jauh_2'] == 'CB000001' || $lc['kode_metastasis_jauh_2'] == 'CB000002'){
                                  $data .='{
                                    "system": "'.$lc['system_metas2'].'",
                                    "code": "'.$lc['kode_metastasis_jauh_2'].'",
                                    "display": "'.$lc['desk_metastasis_jauh_2'].'"
                                  }';
                                }else{
                                  $data .='{
                                    "system": "'.$lc['system_metas2'].'",
                                    "code": "'.$lc['kode_metastasis_jauh_2'].'",
                                    "display": "'.$lc['desk_metastasis_jauh_2'].'"
                                  },';
                                  if($lc['kode_metastasis_jauh_3'] == 'CB000001' || $lc['kode_metastasis_jauh_3'] == 'CB000002'){
                                    $data .='{
                                      "system": "'.$lc['system_metas3'].'",
                                      "code": "'.$lc['kode_metastasis_jauh_3'].'",
                                      "display": "'.$lc['desk_metastasis_jauh_3'].'"
                                    }';
                                  }else{
                                    $data .='{
                                      "system": "'.$lc['system_metas3'].'",
                                      "code": "'.$lc['kode_metastasis_jauh_3'].'",
                                      "display": "'.$lc['desk_metastasis_jauh_3'].'"
                                    },{
                                      "system": "'.$lc['system_metas4'].'",
                                      "code": "'.$lc['kode_metastasis_jauh_4'].'",
                                      "display": "'.$lc['desk_metastasis_jauh_4'].'"
                                    }';
                                  }
                                }
                              }
              
                          $data .=']
                      }
                  ],
                  "subject": {
                      "reference": "Patient/'.$lc['id_ihs_pasien'].'",
                      "display": "'.$namaPasien.'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$lc['id_encounter'].'"
                  },
                  "recordedDate": "'.$tglReg.'"
              }';
            echo '<pre>' . $data;
            echo '<br>-------------------------------------------------------------------------------------------------------------------</br>';

            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition';

            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
              "Content-Type: application/json",
              "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            if($httpcode == "201"){
              $dataUbahPerLatMet = array(
                'id_condition'      => $res->id,
                'jenis_pengiriman'  => 2,
              );
            }else{
              $dataUbahPerLatMet = array(
                'jenis_pengiriman'  => 3,
              );
            }

            $this->db->where('tb_condition.id_encounter', $lc['id_encounter']);
            $this->db->where('tb_condition.jenis_condition', 5);
            $this->db->update('db_regkan.tb_condition', $dataUbahPerLatMet);

            $dataSimpanPerLatMet = array(
              'id_encounter'      => $lc['id_encounter'],
              'id_condition'      => $res->id,
              'log'               => $data,
              'response'          => $response,
              'http_code'         => $httpcode,
              'jenis_condition'   => 5,
            );
            $this->db->insert('db_regkan.tb_log_condition', $dataSimpanPerLatMet);
      }
    }

  }

  public function kirimProcedure()
  {
    $token = $this->getToken();
    $listProcedure = $this->SatuSehatREGKANModel->listProcedure();
    foreach($listProcedure as $lp){
      echo 'TINDAKAN TERAPI <br>';

      $namaPasien = trim(preg_replace('/\s+/', ' ', $lp['nm_pasien']));
      $namaPractitioner = trim(preg_replace('/\s+/', ' ', $lp['nm_practitioner']));

      $datereg = date_create_from_format('Y-m-d H:i:s', $lp['tgl_jam']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
                "resourceType": "Procedure",
                "status": "completed",
                "category": {
                    "coding": [
                        {
                            "system": "http://snomed.info/sct",
                            "code": "277132007",
                            "display": "Therapeutic procedure"
                        }
                    ]
                },
                "code": {
                    "coding": [';

                    if($lp['kode_terapi_1'] == 'PC000012'){
                      $data.='{
                            "system": "'.$lp['system_terapi_1'].'",
                            "code": "'.$lp['kode_terapi_1'].'",
                            "display": "'.$lp['desk_terapi_1'].'"
                        }';
                      }else{
                        $data.='{
                            "system": "'.$lp['system_terapi_1'].'",
                            "code": "'.$lp['kode_terapi_1'].'",
                            "display": "'.$lp['desk_terapi_1'].'"
                        },';
                        if($lp['kode_terapi_2'] == 'PC000012'){
                          $data.='{
                            "system": "'.$lp['system_terapi_2'].'",
                            "code": "'.$lp['kode_terapi_2'].'",
                            "display": "'.$lp['desk_terapi_2'].'"
                        }';
                        }else{
                          $data.='{
                            "system": "'.$lp['system_terapi_2'].'",
                            "code": "'.$lp['kode_terapi_2'].'",
                            "display": "'.$lp['desk_terapi_2'].'"
                        },{
                            "system": "'.$lp['system_terapi_3'].'",
                            "code": "'.$lp['kode_terapi_3'].'",
                            "display": "'.$lp['desk_terapi_3'].'"
                        }';
                        }
                      }
                        
                        

                    $data.=']
                },
                "subject": {
                    "reference": "Patient/'.$lp['id_ihs_pasien'].'",
                    "display": "'.$namaPasien.'"
                },
                "encounter": {
                    "reference": "Encounter/'.$lp['id_encounter'].'"
                },
                "performedPeriod": {
                    "start": "'.$tglReg.'",
                    "end": "'.$tglReg.'"
                },
                "performer": [
                    {
                        "actor": {
                            "reference": "Practitioner/'.$lp['id_ihs_practitioner'].'",
                            "display": "'.$namaPractitioner.'"
                        }
                    }
                ]
            }';

            echo '<pre>' . $data;
            echo '<br>-------------------------------------------------------------------------------------------------------------------</br>';

            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Procedure';

            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
              "Content-Type: application/json",
              "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            if($httpcode == "201"){
              $dataUbahProcedure = array(
                'id_procedure'      => $res->id,
                'jenis_pengiriman'  => 2,
              );
            }else{
              $dataUbahProcedure = array(
                'jenis_pengiriman'  => 3,
              );
            }

            $this->db->where('tb_procedure.id_encounter', $lp['id_encounter']);
            $this->db->update('db_regkan.tb_procedure', $dataUbahProcedure);

            $dataSimpanProcedure = array(
              'id_encounter'      => $lp['id_encounter'],
              'id_procedure'      => $res->id,
              'log'               => $data,
              'response'          => $response,
              'http_code'         => $httpcode,
            );
            $this->db->insert('db_regkan.tb_log_procedure', $dataSimpanProcedure);

    }
  }

  public function kirimEOCSur()
  {
    $token = $this->getToken();
    $listEpsKun1 = $this->SatuSehatREGKANModel->listEpsKun(1);
    $jenis = 1;
    foreach($listEpsKun1 as $lekun1){

      $namaPasien = trim(preg_replace('/\s+/', ' ', $lekun1['nm_pasien']));

      $datereg = date_create_from_format('Y-m-d H:i:s', $lekun1['tgl_jam']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
              "resourceType": "EpisodeOfCare",
              "identifier": [
                  {
                      "system": "http://sys-ids.kemkes.go.id/episode-of-care/100025609",
                      "value": "EOC'.$lekun1['nopen'].'"
                  }
              ],
              "status": "waitlist",
              "statusHistory": [
                  {
                      "status": "waitlist",
                      "period": {
                          "start": "'.$tglReg.'"
                      }
                  }
              ],
              "type": [
                  {
                      "coding": [
                          {
                              "system": "http://terminology.kemkes.go.id/CodeSystem/episodeofcare-type",
                              "code": "cancer",
                              "display": "Cancer Management Care "
                          }
                      ]
                  }
              ],
              "patient": {
                  "reference": "Patient/'.$lekun1['id_ihs_pasien'].'",
                  "display": "'.$namaPasien.'"
              },
              "managingOrganization": {
                  "reference": "Organization/100025609"
              },
              "period": {
                  "start": "'.$tglReg.'"
              }
          }';

          echo 'EPISODE OF CARE SURVEILANS <br>';
          echo '<pre>' . $data . '</pre><br>';
          echo '-----------------------------------------------------------------</pre><br>';
      if($lekun1['jenis_pasien'] == 1){

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/EpisodeOfCare';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if($httpcode == "201"){

          $dataSimpanIdSurveilans = array(
            'id_episodeofcare'   => $res->id,
            'jenis_pengiriman'   => 2,
          );

        }else{
          $dataSimpanIdSurveilans = array(
            'jenis_pengiriman'  => 3,
          );
        }

        $this->db->where('tb_episodeofcare.nopen', $lekun1['nopen']);
        $this->db->update('db_regkan.tb_episodeofcare', $dataSimpanIdSurveilans);

      }elseif($lekun1['jenis_pasien'] == 2){

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/EpisodeOfCare?patient='.$lekun1['id_ihs_pasien'].'';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if($res->total == 0){

          $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/EpisodeOfCare';

          $cURL = curl_init();
          curl_setopt($cURL, CURLOPT_URL,$url);
          curl_setopt($cURL, CURLOPT_HEADER,false);
          curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
          ));
          curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
          curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
          $response = curl_exec($cURL);
          $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
          curl_close($cURL);
          $res = json_decode($response);

          if($httpcode == "201"){

            $dataSimpanIdSurveilans = array(
              'id_episodeofcare'   => $res->id,
              'jenis_pengiriman'   => 2,
            );

          }else{
            $dataSimpanIdSurveilans = array(
              'jenis_pengiriman'  => 3,
            );
          }

          $this->db->where('tb_episodeofcare.nopen', $lekun1['nopen']);
          $this->db->update('db_regkan.tb_episodeofcare', $dataSimpanIdSurveilans);

        }else{
          $simpanGetIdSurVeilansKun1 = array(
            'id_surveilans'     => $res->identifier[1]->value,
          );

          $this->db->where('tb_episodeofcare.nopen', $lekun1['nopen']);
          $this->db->update('db_regkan.tb_episodeofcare', $simpanGetIdSurVeilansKun1);
        }
      }

      $dataSimpanLogEOCSur = array(
        'nopen'             => $lekun1['nopen'],
        'id_episodeofcare'  => $res->id,
        'log'               => $data,
        'response'          => $response,
        'httpcode'          => $httpcode,
        'jenis'             => 1,
      );
      $this->db->insert('db_regkan.tb_log_episodeofcare', $dataSimpanLogEOCSur);

      $dataUpdatePatchEoc = array(
        'id_episode_of_care'   => $res->id,
      );

      $this->db->where('tb_patch_eoc.id_ihs_pasien', $lekun1['id_ihs_pasien']);
      $this->db->update('db_regkan.tb_patch_eoc', $dataUpdatePatchEoc);

      $this->getIdSurveilansRegistry($token, $res->id, $jenis, $lekun1['nopen']);
    }
  }

  public function getIdSurveilansRegistry($token, $eoc, $jenis, $nopen)
  {
    $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/EpisodeOfCare/'.$eoc.'';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$token." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);

    $simpanGetIdSurVeilans = array(
      'id_surveilans'     => isset($res->identifier[1]->value) ? $res->identifier[1]->value : "",
      'id_registry'       => isset($res->identifier[2]->value) ? $res->identifier[2]->value : "",
    );

    $this->db->where('tb_episodeofcare.nopen', $nopen);
    $this->db->update('db_regkan.tb_episodeofcare', $simpanGetIdSurVeilans);

    if($jenis == 1){
      return $res->identifier[1]->value;
    }elseif($jenis == 2){
      return $res->identifier[2]->value;
    }
    
  }

  public function kirimEOCReg()
  {
    $token = $this->getToken();
    $listEpsKun2 = $this->SatuSehatREGKANModel->listEpsKun(2);
    $jenis = 2;
    foreach($listEpsKun2 as $lekun2){

      $datereg = date_create_from_format('Y-m-d H:i:s', $lekun2['tgl_jam']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '[
            {
                "op": "replace",
                "path": "/status",
                "value": "active"
            },
            {
                "op": "add",
                "path": "/statusHistory/0/period/end",
                "value": "'.$tglReg.'"
            },
            {
                "op": "add",
                "path": "/statusHistory/1",
                "value": {
                    "status": "active",
                    "period": {
                            "start": "'.$tglReg.'"
                    }
                }
            }
        ]';

        echo 'EPISODE OF CARE REGISTRY <br>';
        echo '<pre>' . $data . '</pre><br>';
        echo '-----------------------------------------------------------------</pre><br>';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/EpisodeOfCare/'.$lekun2['id_episodeofcare'].'';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json-patch+json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if($httpcode == "200"){

          $dataSimpanIdRegistry = array(
            'id_episodeofcare'   => $res->id,
            'jenis_pengiriman'   => 2,
          );

        }else{
          $dataSimpanIdRegistry = array(
            'jenis_pengiriman'  => 3,
          );
        }

        $this->db->where('tb_episodeofcare.nopen', $lekun2['nopen']);
        $this->db->update('db_regkan.tb_episodeofcare', $dataSimpanIdRegistry);

        $dataSimpanLogEOCReg = array(
        'nopen'             => $lekun2['nopen'],
        'id_episodeofcare'  => $res->id,
        'log'               => $data,
        'response'          => $response,
        'httpcode'          => $httpcode,
        'jenis'             => 2,
      );
      $this->db->insert('db_regkan.tb_log_episodeofcare', $dataSimpanLogEOCReg);

      $dataUpdatePatchEoc = array(
        'id_episode_of_care'   => $res->id,
      );

      $this->db->where('tb_patch_eoc.id_ihs_pasien', $lekun2['id_ihs_pasien']);
      $this->db->update('db_regkan.tb_patch_eoc', $dataUpdatePatchEoc);
      $this->getIdSurveilansRegistry($token, $res->id, $jenis, $lekun2['nopen']);
    }
  }

  public function patchEncounter($jns=0)
  {
    $token = $this->getToken();
    $listPatchEoc = $this->SatuSehatREGKANModel->listPatchEoc($jns);
    foreach($listPatchEoc as $leps){

      $data = '[
              {
                  "op": "add",
                  "path": "/episodeOfCare",
                  "value": [
                      {
                          "reference": "EpisodeOfCare/'.$leps['ID_EOC'].'"
                      }
                  ]
              }
          ]';
          echo "<br>Encounter : " . $leps['ID_ENCOUNTER'];
          echo "<pre>" . $data . "</pre><br>----------------------------------------------------------------------------";

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter/'.$leps['ID_ENCOUNTER'].'';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json-patch+json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PATCH');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        echo '<pre>' . $data . '</pre>';
        if($httpcode == "200"){
          $dataUbahPatch = array(
            'jenis'   => 2,
          );

        }else{
          $dataUbahPatch = array(
            'jenis'  => 3,
          );
        }

        if($jns == 2){
          if($httpcode == "200"){
            $dataEocRanap = array(
              'ENCOUNTER' => $leps['ID_ENCOUNTER'],
              'EOC'       => $leps['ID_EOC'],
              'NOPEN'     => $leps['NOPEN'],
              'STATUS'    => 1,
            );
            $this->db->insert('data_ihs.data_eoc_ranap', $dataEocRanap);
          }

          $dataLogEocPatchRanap = array(
            'nopen'     => $leps['NOPEN'],
            'encounter' => $leps['ID_ENCOUNTER'],
            'eoc'       => $leps['ID_EOC'],
            'log'       => $data,
            'response'  => $response,
            'httpcode'  => $httpcode,
          );
          $this->db->insert('data_ihs.tb_log_patch_eoc_ranap', $dataLogEocPatchRanap);
        }

        $this->db->where('tb_patch_eoc.nopen', $leps['NOPEN']);
        $this->db->update('db_regkan.tb_patch_eoc', $dataUbahPatch);

    }
  }

  // public function insertComposition()
  // {
  //   $listPasien = $this->SatuSehatREGKANModel->listPasienRegKan_2022_composition();
  //   foreach($listPasien as $lp){
  //     $com = $this->SatuSehatREGKANModel->composition($lp['NORM']);
  //     echo '<br>' . $lp['NORM'] . '<br>';
  //     if($com->num_rows() > 0){
  //       foreach($com->result_array() as $cm){
  //         $data = array(
  //           'encounter'               => $cm['ID_ENCOUNTER'],
  //           'nopen'                   => $cm['NOPEN'],
  //           'id_ihs_pasien'           => $cm['ID_IHS_PASIEN'],
  //           'id_ihs_practitioner'     => $cm['ID_PRACTITIONER'],
  //           'id_episodeofcare'        => $cm['ID_EPISODEOFCARE'],
  //           'id_condition1'           => $cm['ID_CONDITION1'],
  //           'id_condition2'           => $cm['ID_CONDITION2'],
  //           'id_condition3'           => $cm['ID_CONDITION3'],
  //           'id_condition4'           => $cm['ID_CONDITION4'],
  //           'id_condition5'           => $cm['ID_CONDITION5'],
  //           'id_procedure'            => $cm['ID_PROCEDURE'],
  //           'id_observation_rad'      => $cm['ID_OBSERVATION_RAD'],
  //           'nm_pasien'               => $cm['NAMA_PASIEN'],
  //           'nm_dpjp'                 => $cm['DPJP'],
  //           'tgl_jam'                 => $cm['TANGGAL_JAM'],
  //           'jenis_kirim'             => 1,
  //         );
  //         echo $cm['NOPEN'] . ' - ' . $cm['ID_ENCOUNTER'] . ' - <br>';
  //         // $this->db->insert('db_regkan.tb_composition', $data);
  //       }
  //     }
  //     echo '-----------------------------------------';
  //   }
  // }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4)) . '-TEMP';
    // }
  }

  public function updateComposition()
  {
    $updateComposition = $this->SatuSehatREGKANModel->updateComposition();
    foreach($updateComposition as $dt){
      echo $dt['id'] . '<br>';
      $dataUbahStatusDuplicate = array(
        'status'  => 0,
      );
      $this->db->where('tb_composition.id_composition', $dt['id']);
      $this->db->update('db_regkan.tb_composition', $dataUbahStatusDuplicate);
    }
  }

  public function postComposition()
  {
    $token = $this->getToken();
    $listComposition = $this->SatuSehatREGKANModel->listComposition();
    foreach($listComposition as $lc){
      $datereg = date_create_from_format('Y-m-d H:i:s', $lc['tgl_jam']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
              "resourceType": "Composition",
              "identifier": {
                  "system": "http://sys-ids.kemkes.go.id/composition/100025609",
                  "value": "COM'.$lc['nopen'].'"
              },
              "status": "final",
              "type": {
                  "coding": [
                      {
                          "system": "http://terminology.kemkes.go.id",
                          "code": "TK000012",
                          "display": "Data Registrasi Kanker"
                      }
                  ]
              },
              "subject": {
                  "reference": "Patient/'.$lc['id_ihs_pasien'].'",
                  "display": "'.$lc['nm_pasien'].'"
              },
              "encounter": {
                  "reference": "Encounter/'.$lc['encounter'].'"
              },
              "date": "'.$tglReg.'",
              "author": [
                  {
                      "reference": "Practitioner/'.$lc['id_ihs_practitioner'].'",
                      "display": "'.$lc['nm_dpjp'].'"
                  }
              ],
              "title": "Data Registrasi Kanker",
              "custodian": {
                  "reference": "Organization/100025609"
              }';
              if(isset($lc['id_episodeofcare']) || isset($lc['id_condition1']) || isset($lc['id_condition2']) || isset($lc['id_condition3']) || isset($lc['id_condition4']) || isset($lc['id_condition5']) || isset($lc['id_procedure']) || isset($lc['id_observation_rad'])){
              $data.=',"section": [';
              if(isset($lc['id_episodeofcare']) && $lc['id_episodeofcare'] != ''){
                $data.='{
                      "title": "Episode Perawatan",
                      "code": {
                          "coding": [
                              {
                                  "system": "http://terminology.kemkes.go.id",
                                  "code": "TK000002",
                                  "display": "Episode Perawatan"
                              }
                          ]
                      },
                      "entry": [
                          {
                              "reference": "EpisodeOfCare/'.$lc['id_episodeofcare'].'",
                              "display": "Episode Perawatan Registrasi Kanker"
                          }
                      ]
                  },';
              }

              if(isset($lc['id_condition1']) && $lc['id_condition1'] != ''){
                $data.='{
                      "title": "Anamnesis",
                      "code": {
                          "coding": [
                              {
                                  "system": "http://terminology.kemkes.go.id",
                                  "code": "TK000003",
                                  "display": "Anamnesis"
                              }
                          ]
                      },
                      "entry": [
                          {
                              "reference": "Condition/'.$lc['id_condition1'].'",
                              "display": "Keluhan Utama"
                          }
                      ]
                  },';
              }
              
              if(isset($lc['id_observation_rad']) && $lc['id_observation_rad'] != ''){
                $data.='{
                      "title": "Hasil Pemeriksaan Penunjang",
                      "code": {
                          "coding": [
                              {
                                  "system": "http://terminology.kemkes.go.id",
                                  "code": "TK000009",
                                  "display": "Hasil Pemeriksaan Penunjang"
                              }
                          ]
                      },
                      "entry": [
                          {
                              "reference": "Observation/'.$lc['id_observation_rad'].'",
                              "display": "Pemeriksaan Radiologi"
                          }
                      ]
                  },';
              }
              
              if((isset($lc['id_condition2']) && $lc['id_condition2'] != '') || (isset($lc['id_condition3']) && $lc['id_condition3'] != '') || (isset($lc['id_condition4']) && $lc['id_condition4'] != '') || (isset($lc['id_condition5']) && $lc['id_condition5'] != '')){
                $data.='{
                      "title": "Diagnosis Tumor",
                      "code": {
                          "coding": [
                              {
                                  "system": "http://terminology.kemkes.go.id",
                                  "code": "TK000004",
                                  "display": "Diagnosis"
                              }
                          ]
                      },
                      "entry": [';
                      if(isset($lc['id_condition2']) && $lc['id_condition2'] != ''){
                        $data.='{
                              "reference": "Condition/'.$lc['id_condition2'].'",
                              "display": "ICD-10 dan Basis Diagnosis"
                          },';
                      }
                      if(isset($lc['id_condition3']) && $lc['id_condition3'] != ''){
                        $data.='{
                              "reference": "Condition/'.$lc['id_condition3'].'",
                              "display": "Topografi, Morfologi, Perilaku, Grade"
                          },';
                      }
                      if(isset($lc['id_condition4']) && $lc['id_condition4'] != ''){
                        $data.='{
                              "reference": "Condition/'.$lc['id_condition4'].'",
                              "display": "TNM, Stadium"
                          },';
                      }
                      if(isset($lc['id_condition5']) && $lc['id_condition5'] != ''){
                        $data.='{
                              "reference": "Condition/'.$lc['id_condition5'].'",
                              "display": "Perluasan, Lateralitas, Metastasis"
                          },';
                      }
                
                    $data.='],
                      "text": {
                          "status": "additional",
                          "div": "Pasien terdiagnosis kanker"
                      }
                  },';
              }

              if(isset($lc['id_procedure']) && $lc['id_procedure'] != ''){
                $data.='{
                      "title": "Tindakan/Prosedur Medis",
                      "code": {
                          "coding": [
                              {
                                  "system": "http://terminology.kemkes.go.id",
                                  "code": "TK000005",
                                  "display": "Tindakan/Prosedur Medis"
                              }
                          ]
                      },
                      "entry": [
                          {
                              "reference": "Procedure/'.$lc['id_procedure'].'",
                              "display": "Tindakan Terapi"
                          }
                      ]
                  }';
              }

              $data.=']';
            }
          $data.='}';
          $data = str_replace(',]', ']', $data);

          $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Composition';

          $cURL = curl_init();
          curl_setopt($cURL, CURLOPT_URL,$url);
          curl_setopt($cURL, CURLOPT_HEADER,false);
          curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
          ));
          curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
          curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
          $response = curl_exec($cURL);
          $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
          curl_close($cURL);
          $res = json_decode($response);

          if($httpcode == "201"){
            $dataUbahComposition = array(
              'id_composition'         => $res->id,
              'jenis_kirim'            => 2,
            );
          }else{
            $dataUbahComposition = array(
              'jenis_kirim'  => 3,
            );
          }

          $this->db->where('tb_composition.id_composition', $lc['id_composition']);
          $this->db->update('db_regkan.tb_composition', $dataUbahComposition);

          $dataSimpanCompositionLog = array(
            'nopen'               => $lc['nopen'],
            'id_encounter'        => $lc['encounter'],
            'id_composition'      => $res->id,
            'log'                 => $data,
            'response'            => $response,
            'httpcode'            => $httpcode,
          );

          echo '<pre>' . $data . '</pre><br>';
          $this->db->insert('db_regkan.tb_log_composition', $dataSimpanCompositionLog);

    }
}
}
