<h1>Patient Menu</h1>
<ul class="nav nav-tabs">
  <li class="nav-item">
    <a class="nav-link active" href="#search-nik" data-toggle="tab">Search NIK</a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="#search-patient-ihs" data-toggle="tab">Search Patient ID</a>
  </li>
</ul>

<div class="tab-content">
  <div class="tab-pane active" id="search-nik">
    <form id="formGetPatientByNIK">
      <label><br>NIK</label>
      <div class="row col-md-12">
        <input type="text" name="nik" placeholder="nik" class="form-control col-md-3" required>
        <button type="submit" id="btnSearchPatientByNIK" style="margin-left: 15px;" class="btn btn-warning">Search</button>
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="resultGetPatientByNIK"></pre>
  </div>
  <div class="tab-pane" id="search-patient-ihs">
    <form id="formGetPatientByIHS">
      <label><br>ID IHS</label>
      <div class="row col-md-12">
        <input type="text" name="id_ihs" placeholder="id ihs" class="form-control col-md-3" required>
        <button type="submit" id="btnSearchPatientByIHS" style="margin-left: 15px;" class="btn btn-warning">Search</button>
        <!-- <button type="submit" id="btnSearchPatientByNIK" style="margin-left: 15px;" class="btn btn-warning">Search</button> -->
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="resultGetPatientByIHS"></pre>
  </div>
</div>
<script>
  $('#formGetPatientByNIK').submit(function (event) {
    dataVerifPengkajian = $("#formGetPatientByNIK").serializeArray();
    $.ajax({
      url: '<?= base_url('IHS/getPatientByNIK') ?>',
      data: dataVerifPengkajian,
      method: 'POST',
      success: function(res){
        $("#resultGetPatientByNIK").html(res);
      }
    });
    event.preventDefault();
  });

  $('#formGetPatientByIHS').submit(function (event) {
    form = $("#formGetPatientByIHS").serializeArray();
    $.ajax({
      url: '<?= base_url('IHS/getPatientByIHS') ?>',
      method: 'POST',
      data: form,
      success: function(res){
        $("#resultGetPatientByIHS").html(res);
      }
    });
    event.preventDefault();
  });
</script>