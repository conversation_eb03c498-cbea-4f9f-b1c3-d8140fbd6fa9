<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class IHSModel extends CI_Model {

  public function login()
  {
    $username = $this->input->post('username');
    $password = md5($this->input->post('password'));

    $this->db->select('l.id, l.username, l.password');
    $this->db->from('ihs.tb_login l');
    $this->db->where('l.username',$username);
    $this->db->where('l.password',$password);

    $query = $this->db->get()->row();
    if($query == "")
    {
      return array('status' => 204, 'message' => 'Username dan password tidak sesuai.');
    }else{
      $id              = $query->id;
      $username        = $query->username;

      $data = array('id' => $id , 
                    'username' => $username
                  );
 
      return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );


    }
  }

  public function getDataEncounter()
  {
    $query = $this->db->query("SELECT iden.NOMOR NIK_PASIEN, peg.KTP NIK_DOKTER, res.diagnosis_utama diag_st_icd, res.diagnosis_utama_des diag_st_display, res.diagnosis_sekunder diag_nd_icd, res.diagnosis_sekunder_des diag_nd_display FROM pendaftaran.pendaftaran pen
    LEFT JOIN master.pasien maspas ON pen.`NORM` = maspas.NORM
    LEFT JOIN master.kartu_identitas_pasien iden ON pen.NORM = iden.NORM
    LEFT JOIN resume_medis.resume_medis res ON pen.NOMOR = res.nopen
    LEFT JOIN master.dokter dok ON res.id_dpjp = dok.id
    LEFT JOIN master.pegawai1 peg ON dok.NIP = peg.NIP
    WHERE iden.NOMOR='3173060105640014' AND pen.TANGGAL LIKE '2022-03-23%' AND res.id IS NOT NULL");

    return $query->row_array();
  }

  public function dataKunjungan()
  {
    $query ="
    SELECT pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
    , pg.KTP KTP_DOKTER
    , CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN
    , master.getNamaLengkapPegawai (doks.NIP) DPJP
    , DATE_FORMAT(tk.MASUK,'%Y-%m-%d %H:%i:%s') TGLREG
    , DATE_FORMAT(tk.KELUAR,'%Y-%m-%d %H:%i:%s') TGLKELUAR
    , r.DESKRIPSI UNITPELAYANAN
    , r.ID ID_RUANGAN
    , pd.STATUS

    FROM pendaftaran.pendaftaran pd
    LEFT JOIN master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
    LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
    LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
    LEFT JOIN master.pasien p ON p.NORM=pd.NORM
    LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
    LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
    LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN
    LEFT JOIN ihs.tb_encounter enc ON enc.nopen = pd.NOMOR
    WHERE pd.STATUS IN (1) AND tk.REF IS NULL AND jkr.JENIS_KUNJUNGAN NOT IN(3,4)
    AND tp.RUANGAN LIKE '105%' AND tk.MASUK BETWEEN (CONCAT(DATE(NOW()),' 00:00:00')) AND (CONCAT(DATE(NOW()),' 23:59:59')) 
    AND enc.id IS NULL
    GROUP BY pd.NOMOR
    limit 5
    ";
    $bind = $this->db->query($query);
    return $bind;
  }

  public function dataTindakan($nopen)
  {
    $query ="
    SELECT pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
    , DATE_FORMAT(tk.MASUK,'%d-%m-%Y %H:%i:%s') TGLREG
    , md.KODE DIAGNOSA
    , (
      SELECT mr.STR
      FROM master.mrconso mr
      WHERE mr.CODE=md.KODE AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN=pd.NOMOR
      LIMIT 1) DIAGNOSA_DESKRIPSI
    , md.UTAMA JENIS
    FROM pendaftaran.pendaftaran pd
    LEFT JOIN master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
    LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
    LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
    LEFT JOIN master.pasien p ON p.NORM=pd.NORM
    LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
    LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
    LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN
    LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = pd.NOMOR AND md.`STATUS`=1
    WHERE pd.STATUS IN (1,2) AND tk.REF IS NULL AND tk.STATUS=2 AND jkr.JENIS_KUNJUNGAN NOT IN(3,4) 
    AND tp.RUANGAN LIKE '105%' AND tk.MASUK BETWEEN (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -7 DAY),' 00:00:00')) 
    AND (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -7 DAY),' 23:59:59')) 
    AND pd.NOMOR='$nopen'
    AND md.ID IS NOT NULL
    ";
    $bind = $this->db->query($query);
    return $bind;
  }

  public function dataPasien($nik)
  {
    $query ="SELECT ps.NORM, ps.GELAR_DEPAN, ps.NAMA, ps.GELAR_BELAKANG, ps.PANGGILAN NAMA_PANGGILAN,
            CASE
            WHEN ps.TEMPAT_LAHIR = w.ID THEN w.DESKRIPSI
            ELSE ps.TEMPAT_LAHIR
            END TEMPAT_LAHIR,

            DATE_FORMAT(ps.TANGGAL_LAHIR, '%Y-%m-%d') TANGGAL_LAHIR,
            #master.getCariUmur(df.TANGGAL, ps.TANGGAL_LAHIR) UMUR,
            jekel.DESKRIPSI JENIS_KELAMIN,
            IF(jekel.DESKRIPSI='Perempuan', 'female', 'male') JENIS_KELAMIN_ING,
            agm.DESKRIPSI AGAMA,
            sp.DESKRIPSI STATUS_PERKAWINAN,
            IF(sp.DESKRIPSI='Kawin', 'M', IF(sp.DESKRIPSI='Belum Kawin', 'U', 'L')) STATUS_KAWIN_KODE,
            IF(sp.DESKRIPSI='Kawin', 'Married', IF(sp.DESKRIPSI='Belum Kawin', 'Unmarried', 'Legally Separated')) STATUS_KAWIN_VAR,
            pen.DESKRIPSI PENDIDIKAN,
            gd.DESKRIPSI GOLONGAN_DARAH,
            neg.DESKRIPSI KEWARGANEGARAAN,
            sb.DESKRIPSI SUKUBANGSA,
            bhs.DESKRIPSI BAHASA_SEHARIHARI,
            krj.DESKRIPSI PEKERJAAN,
            lkrj.DESKRIPSI LINGKUNGAN_KERJA,
            tj.DESKRIPSI TUJUAN_PERIKSA,
            ps.ALAMAT,
            ps.RT,
            ps.RW,
            ps.KODEPOS,
            wil.DESKRIPSI WILAYAH,
            wil.ID KODE_WILAYAH,
            wilkot.ID KODE_KOTA,
            wilkec.ID KODE_KECAMATAN,
            wilkel.ID KODE_DESA,
            wilkot.DESKRIPSI KOTA,
            wilkec.DESKRIPSI KECAMATAN,
            wilkel.DESKRIPSI KELURAHAN,
            rmh.NOMOR TELEPON_RUMAH,
            ktr.NOMOR TELEPON_KANTOR,
            sel.NOMOR TELEPON_SELULER,
            frmh.NOMOR FAKS_RUMAH,
            fktr.NOMOR FAKS_KANTOR,
            em.NOMOR EMAIL,
            web.NOMOR SITUS_WEB,
            sm.NOMOR SOCIAL_MEDIA,
            kidp.NOMOR NOMOR_IDENTITAS,
            kidp.ALAMAT ALAMAT_IDENTITAS,
            kidp.RT RT_IDENTITAS,
            kidp.RW RW_IDENTITAS,
            kidp.KODEPOS KODEPOS_IDENTITAS,
            wil.DESKRIPSI WILAYAH_IDENTITAS,
            wilkot.DESKRIPSI KOTA_IDENTITAS,
            wilkec.DESKRIPSI KECAMATAN_IDENTITAS,
            wilkel.DESKRIPSI KELURAHAN_IDENTITAS,
            hubps.DESKRIPSI SHDK,
            kps.NAMA NAMA_KELUARGA,
            kkps.NOMOR NO_HP_KELUARGA,
            jkl.DESKRIPSI JENIS_KELAMIN_KEL,
            pend.DESKRIPSI PENDIDIKAN_KEL,
            krjk.DESKRIPSI PEKERJAAN_KEL,
            kps.ALAMAT ALAMAT_KEL

            FROM master.pasien ps

            LEFT JOIN master.wilayah w ON w.ID = ps.TEMPAT_LAHIR
            LEFT JOIN master.referensi jekel ON jekel.ID = ps.JENIS_KELAMIN AND jekel.JENIS = 2
            LEFT JOIN master.referensi agm ON agm.ID = ps.AGAMA AND agm.JENIS = 1
            LEFT JOIN master.referensi sp ON sp.ID = ps.STATUS_PERKAWINAN AND sp.JENIS = 5
            LEFT JOIN master.referensi pen ON pen.ID = ps.PENDIDIKAN AND pen.JENIS = 3
            LEFT JOIN master.referensi gd ON gd.ID = ps.GOLONGAN_DARAH AND gd.JENIS = 6
            LEFT JOIN master.negara neg ON neg.ID = ps.KEWARGANEGARAAN
            LEFT JOIN master.referensi sb ON sb.ID = ps.SUKUBANGSA AND sb.JENIS = 96
            LEFT JOIN master.referensi bhs ON bhs.ID = ps.BAHASA AND bhs.JENIS = 95
            LEFT JOIN master.referensi krj ON krj.ID = ps.PEKERJAAN AND krj.JENIS = 4
            LEFT JOIN master.referensi lkrj ON lkrj.ID = ps.LINGKUNGANKERJA AND lkrj.JENIS = 97
            LEFT JOIN master.referensi tj ON tj.ID = ps.TUJUANPERIKSA AND tj.JENIS = 98
            LEFT JOIN master.wilayah wil ON SUBSTR(ps.WILAYAH,1,2) = wil.ID AND wil.JENIS = 1
            LEFT JOIN master.wilayah wilkot ON SUBSTR(ps.WILAYAH,1,4) = wilkot.ID AND wilkot.JENIS = 2
            LEFT JOIN master.wilayah wilkec ON SUBSTR(ps.WILAYAH,1,6) = wilkec.ID AND wilkec.JENIS = 3
            LEFT JOIN master.wilayah wilkel ON SUBSTR(ps.WILAYAH,1,10) = wilkel.ID AND wilkel.JENIS = 4
            LEFT JOIN master.kontak_pasien rmh ON rmh.NORM = ps.NORM AND rmh.JENIS = 1
            LEFT JOIN master.kontak_pasien ktr ON ktr.NORM = ps.NORM AND ktr.JENIS = 2
            LEFT JOIN master.kontak_pasien sel ON sel.NORM = ps.NORM AND sel.JENIS = 3
            LEFT JOIN master.kontak_pasien frmh ON frmh.NORM = ps.NORM AND frmh.JENIS = 4
            LEFT JOIN master.kontak_pasien fktr ON fktr.NORM = ps.NORM AND fktr.JENIS = 5
            LEFT JOIN master.kontak_pasien em ON em.NORM = ps.NORM AND em.JENIS = 6
            LEFT JOIN master.kontak_pasien web ON web.NORM = ps.NORM AND web.JENIS = 7
            LEFT JOIN master.kontak_pasien sm ON sm.NORM = ps.NORM AND sm.JENIS = 8
            LEFT JOIN master.kartu_identitas_pasien kidp ON kidp.NORM = ps.NORM
            LEFT JOIN master.wilayah kwil ON SUBSTR(kidp.WILAYAH,1,2) = kwil.ID AND kwil.JENIS = 1
            LEFT JOIN master.wilayah kwilkot ON SUBSTR(kidp.WILAYAH,1,4) = kwilkot.ID AND kwilkot.JENIS = 2
            LEFT JOIN master.wilayah kwilkec ON SUBSTR(kidp.WILAYAH,1,6) = kwilkec.ID AND kwilkec.JENIS = 3
            LEFT JOIN master.wilayah kwilkel ON SUBSTR(kidp.WILAYAH,1,10) = kwilkel.ID AND kwilkel.JENIS = 4
            LEFT JOIN master.keluarga_pasien kps ON kps.NORM = ps.NORM
            LEFT JOIN master.referensi hubps ON hubps.ID = kps.SHDK AND hubps.JENIS = 7
            LEFT JOIN master.referensi jkl ON jkl.ID = kps.JENIS_KELAMIN AND jkl.JENIS = 2
            LEFT JOIN master.referensi pend ON pend.ID = kps.PENDIDIKAN AND pend.JENIS = 3
            LEFT JOIN master.referensi krjk ON krjk.ID = kps.PEKERJAAN AND krjk.JENIS = 4
            LEFT JOIN master.kontak_keluarga_pasien kkps ON kkps.NORM = ps.NORM

            WHERE #DATE(ps.TANGGAL) = '2021-08-18' AND
              kidp.NOMOR='$nik'

            GROUP BY ps.NORM";

    $bind = $this->db->query($query);
    return $bind->row_array();
  }

  public function dataPasienAlternatif($name,$birthdate,$birthplace)
  {
    $query ="SELECT ps.NORM, ps.GELAR_DEPAN, ps.NAMA, ps.GELAR_BELAKANG, ps.PANGGILAN NAMA_PANGGILAN,
            CASE
            WHEN ps.TEMPAT_LAHIR = w.ID THEN w.DESKRIPSI
            ELSE ps.TEMPAT_LAHIR
            END TEMPAT_LAHIR,

            DATE_FORMAT(ps.TANGGAL_LAHIR, '%Y-%m-%d') TANGGAL_LAHIR,
            #master.getCariUmur(df.TANGGAL, ps.TANGGAL_LAHIR) UMUR,
            jekel.DESKRIPSI JENIS_KELAMIN,
            IF(jekel.DESKRIPSI='Perempuan', 'female', 'male') JENIS_KELAMIN_ING,
            agm.DESKRIPSI AGAMA,
            sp.DESKRIPSI STATUS_PERKAWINAN,
            IF(sp.DESKRIPSI='Kawin', 'M', IF(sp.DESKRIPSI='Belum Kawin', 'U', 'L')) STATUS_KAWIN_KODE,
            IF(sp.DESKRIPSI='Kawin', 'Married', IF(sp.DESKRIPSI='Belum Kawin', 'Unmarried', 'Legally Separated')) STATUS_KAWIN_VAR,
            pen.DESKRIPSI PENDIDIKAN,
            gd.DESKRIPSI GOLONGAN_DARAH,
            neg.DESKRIPSI KEWARGANEGARAAN,
            sb.DESKRIPSI SUKUBANGSA,
            bhs.DESKRIPSI BAHASA_SEHARIHARI,
            krj.DESKRIPSI PEKERJAAN,
            lkrj.DESKRIPSI LINGKUNGAN_KERJA,
            tj.DESKRIPSI TUJUAN_PERIKSA,
            ps.ALAMAT,
            ps.RT,
            ps.RW,
            ps.KODEPOS,
            wil.DESKRIPSI WILAYAH,
            wil.ID KODE_WILAYAH,
            wilkot.ID KODE_KOTA,
            wilkec.ID KODE_KECAMATAN,
            wilkel.ID KODE_DESA,
            wilkot.DESKRIPSI KOTA,
            wilkec.DESKRIPSI KECAMATAN,
            wilkel.DESKRIPSI KELURAHAN,
            rmh.NOMOR TELEPON_RUMAH,
            ktr.NOMOR TELEPON_KANTOR,
            sel.NOMOR TELEPON_SELULER,
            frmh.NOMOR FAKS_RUMAH,
            fktr.NOMOR FAKS_KANTOR,
            em.NOMOR EMAIL,
            web.NOMOR SITUS_WEB,
            sm.NOMOR SOCIAL_MEDIA,
            kidp.NOMOR NOMOR_IDENTITAS,
            kidp.ALAMAT ALAMAT_IDENTITAS,
            kidp.RT RT_IDENTITAS,
            kidp.RW RW_IDENTITAS,
            kidp.KODEPOS KODEPOS_IDENTITAS,
            wil.DESKRIPSI WILAYAH_IDENTITAS,
            wilkot.DESKRIPSI KOTA_IDENTITAS,
            wilkec.DESKRIPSI KECAMATAN_IDENTITAS,
            wilkel.DESKRIPSI KELURAHAN_IDENTITAS,
            hubps.DESKRIPSI SHDK,
            kps.NAMA NAMA_KELUARGA,
            kkps.NOMOR NO_HP_KELUARGA,
            jkl.DESKRIPSI JENIS_KELAMIN_KEL,
            pend.DESKRIPSI PENDIDIKAN_KEL,
            krjk.DESKRIPSI PEKERJAAN_KEL,
            kps.ALAMAT ALAMAT_KEL

            FROM master.pasien ps

            LEFT JOIN master.wilayah w ON w.ID = ps.TEMPAT_LAHIR
            LEFT JOIN master.referensi jekel ON jekel.ID = ps.JENIS_KELAMIN AND jekel.JENIS = 2
            LEFT JOIN master.referensi agm ON agm.ID = ps.AGAMA AND agm.JENIS = 1
            LEFT JOIN master.referensi sp ON sp.ID = ps.STATUS_PERKAWINAN AND sp.JENIS = 5
            LEFT JOIN master.referensi pen ON pen.ID = ps.PENDIDIKAN AND pen.JENIS = 3
            LEFT JOIN master.referensi gd ON gd.ID = ps.GOLONGAN_DARAH AND gd.JENIS = 6
            LEFT JOIN master.negara neg ON neg.ID = ps.KEWARGANEGARAAN
            LEFT JOIN master.referensi sb ON sb.ID = ps.SUKUBANGSA AND sb.JENIS = 96
            LEFT JOIN master.referensi bhs ON bhs.ID = ps.BAHASA AND bhs.JENIS = 95
            LEFT JOIN master.referensi krj ON krj.ID = ps.PEKERJAAN AND krj.JENIS = 4
            LEFT JOIN master.referensi lkrj ON lkrj.ID = ps.LINGKUNGANKERJA AND lkrj.JENIS = 97
            LEFT JOIN master.referensi tj ON tj.ID = ps.TUJUANPERIKSA AND tj.JENIS = 98
            LEFT JOIN master.wilayah wil ON SUBSTR(ps.WILAYAH,1,2) = wil.ID AND wil.JENIS = 1
            LEFT JOIN master.wilayah wilkot ON SUBSTR(ps.WILAYAH,1,4) = wilkot.ID AND wilkot.JENIS = 2
            LEFT JOIN master.wilayah wilkec ON SUBSTR(ps.WILAYAH,1,6) = wilkec.ID AND wilkec.JENIS = 3
            LEFT JOIN master.wilayah wilkel ON SUBSTR(ps.WILAYAH,1,10) = wilkel.ID AND wilkel.JENIS = 4
            LEFT JOIN master.kontak_pasien rmh ON rmh.NORM = ps.NORM AND rmh.JENIS = 1
            LEFT JOIN master.kontak_pasien ktr ON ktr.NORM = ps.NORM AND ktr.JENIS = 2
            LEFT JOIN master.kontak_pasien sel ON sel.NORM = ps.NORM AND sel.JENIS = 3
            LEFT JOIN master.kontak_pasien frmh ON frmh.NORM = ps.NORM AND frmh.JENIS = 4
            LEFT JOIN master.kontak_pasien fktr ON fktr.NORM = ps.NORM AND fktr.JENIS = 5
            LEFT JOIN master.kontak_pasien em ON em.NORM = ps.NORM AND em.JENIS = 6
            LEFT JOIN master.kontak_pasien web ON web.NORM = ps.NORM AND web.JENIS = 7
            LEFT JOIN master.kontak_pasien sm ON sm.NORM = ps.NORM AND sm.JENIS = 8
            LEFT JOIN master.kartu_identitas_pasien kidp ON kidp.NORM = ps.NORM
            LEFT JOIN master.wilayah kwil ON SUBSTR(kidp.WILAYAH,1,2) = kwil.ID AND kwil.JENIS = 1
            LEFT JOIN master.wilayah kwilkot ON SUBSTR(kidp.WILAYAH,1,4) = kwilkot.ID AND kwilkot.JENIS = 2
            LEFT JOIN master.wilayah kwilkec ON SUBSTR(kidp.WILAYAH,1,6) = kwilkec.ID AND kwilkec.JENIS = 3
            LEFT JOIN master.wilayah kwilkel ON SUBSTR(kidp.WILAYAH,1,10) = kwilkel.ID AND kwilkel.JENIS = 4
            LEFT JOIN master.keluarga_pasien kps ON kps.NORM = ps.NORM
            LEFT JOIN master.referensi hubps ON hubps.ID = kps.SHDK AND hubps.JENIS = 7
            LEFT JOIN master.referensi jkl ON jkl.ID = kps.JENIS_KELAMIN AND jkl.JENIS = 2
            LEFT JOIN master.referensi pend ON pend.ID = kps.PENDIDIKAN AND pend.JENIS = 3
            LEFT JOIN master.referensi krjk ON krjk.ID = kps.PEKERJAAN AND krjk.JENIS = 4
            LEFT JOIN master.kontak_keluarga_pasien kkps ON kkps.NORM = ps.NORM

            WHERE #DATE(ps.TANGGAL) = '2021-08-18' AND kidp.NOMOR='8271021209490001'
            ps.NAMA LIKE '%$name%' 
            # AND YEAR(ps.TANGGAL_LAHIR) = '$birthdate' 
            AND ps.TANGGAL_LAHIR = '$birthdate' 
            AND w.DESKRIPSI LIKE '%$birthplace%'

            GROUP BY ps.TANGGAL_LAHIR";
    $bind = $this->db->query($query);
    return $bind->row_array();
  }

  public function getIdLocation($idruangan)
  {
    $query ="SELECT loc.id_ihs ID_IHS_LOCATION
    , loc.description KET_IHS_LOCATION
    , r.ID ID_RUANGAN
    FROM ihs.tb_location loc
    LEFT JOIN master.ruangan r ON r.ID = loc.id_ruangan AND r.JENIS=5
    WHERE loc.status=?
    AND loc.id_ruangan IS NOT NULL
    AND loc.id_ruangan!=''
    AND loc.id_ruangan=?
    ";
    $bind = $this->db->query($query, array(1, $idruangan));
    return $bind->row_array();
  }

  public function getPartOf()
  {
    $query = $this->db->query("SELECT * FROM ihs.tb_location l WHERE l.status = 1");

    return $query->result_array();
  }

  public function getRuangan()
  {
    $this->db->select('ID ID_RUANGAN, JENIS, JENIS_KUNJUNGAN, DESKRIPSI, STATUS');
        $this->db->from('master.ruangan r');
        $this->db->where('r.JENIS', '5');
        $this->db->where('r.STATUS', '1');
        $this->db->where_in('r.JENIS_KUNJUNGAN', array('1', '2', '4', '14', '13'));
        $this->db->order_by('DESKRIPSI ASC');

        $query = $this->db->get();
        return $query->result_array();
  }

  public function getDiagnosa()
  {
    $query = $this->db->query("SELECT a.encounter, pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
    , DATE_FORMAT(tk.MASUK,'%d-%m-%Y %H:%i:%s') TGLREG
    , md.KODE DIAGNOSA
    , (SELECT mr.STR FROM master.mrconso mr WHERE mr.CODE=md.KODE AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN=pd.NOMOR LIMIT 1) DIAGNOSA_DESKRIPSI
    , md.UTAMA JENIS 

    FROM (SELECT enc.nopen, enc.encounter encounter FROM ihs.tb_encounter enc
      LEFT JOIN ihs.tb_condition con ON con.nopen = enc.nopen
    WHERE con.id IS NULL 
    GROUP BY enc.nopen) a

    LEFT JOIN  pendaftaran.pendaftaran pd ON pd.NOMOR = a.nopen
    LEFT JOIN  master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
    LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
    LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
    
    LEFT JOIN master.pasien p ON p.NORM=pd.NORM
    LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
    LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
    LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN 
    LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = pd.NOMOR AND md.`STATUS`=1 
    LEFT JOIN ihs.tb_encounter ihs_enc ON ihs_enc.nopen = pd.NOMOR

    WHERE pd.STATUS IN (1,2) AND tk.REF IS NULL
    AND tk.STATUS=2
    AND jkr.JENIS_KUNJUNGAN NOT IN(3,4) AND tp.RUANGAN LIKE '105%'");

    return $query;
  }

  public function getEncounter($nomr)
  {
    $query = $this->db->query("SELECT c.nopen, pen.NORM
            , (SELECT cs.id FROM ihs.tb_encounter cs
              WHERE cs.nopen = c.nopen AND cs.`status`=1
              ORDER BY cs.id DESC 
              LIMIT 1
              ) id
            , (SELECT cs.uuid FROM ihs.tb_encounter cs
              WHERE cs.nopen = c.nopen AND cs.`status`=1
              ORDER BY cs.id DESC 
              LIMIT 1
              ) uuid
            , (SELECT cs.encounter FROM ihs.tb_encounter cs
              WHERE cs.nopen = c.nopen AND cs.`status`=1
              ORDER BY cs.id DESC 
              LIMIT 1
              ) encounter
            , (SELECT cs.`status` FROM ihs.tb_encounter cs
              WHERE cs.nopen = c.nopen AND cs.`status`=1
              ORDER BY cs.id DESC 
              LIMIT 1
              ) status
            , (SELECT cs.created_at FROM ihs.tb_encounter cs
              WHERE cs.nopen = c.nopen AND cs.`status`=1
              ORDER BY cs.id DESC 
              LIMIT 1
              ) created_at
              
            FROM ihs.tb_encounter c 
            LEFT JOIN pendaftaran.pendaftaran pen ON c.nopen = pen.NOMOR
            WHERE pen.NORM = '$nomr' AND c.status = 1
            GROUP BY c.nopen
          ");

    return $query->result_array();
  }

  public function dataEncounter($nik)
  {
    $query = $this->db->query("SELECT pen.NOMOR, pen.TANGGAL, pen.NORM, master.getNamaLengkap(ps.NORM) NAMA_PASIEN FROM pendaftaran.pendaftaran pen
    LEFT JOIN master.pasien ps ON pen.NORM = ps.NORM
    LEFT JOIN master.kartu_identitas_pasien kidp ON kidp.NORM = ps.NORM
    WHERE kidp.NOMOR = '$nik' AND pen.STATUS != 0");

    return $query->result_array();
  }

  public function dataKunjunganV2($nik)
  {
    $query ="
    SELECT pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
    , pg.KTP KTP_DOKTER
    , CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN
    , master.getNamaLengkapPegawai (doks.NIP) DPJP
    , DATE_FORMAT(tk.MASUK,'%Y-%m-%d %H:%i:%s') TGLREG
    , DATE_FORMAT(tk.KELUAR,'%Y-%m-%d %H:%i:%s') TGLKELUAR
    , r.DESKRIPSI UNITPELAYANAN
    , r.ID ID_RUANGAN
    FROM pendaftaran.pendaftaran pd
    LEFT JOIN master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
    LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
    LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
    LEFT JOIN master.pasien p ON p.NORM=pd.NORM
    LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
    LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
    LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN
    WHERE pd.STATUS IN (1,2) AND tk.REF IS NULL AND tk.STATUS=2 AND jkr.JENIS_KUNJUNGAN NOT IN(3,4) 
    AND tp.RUANGAN LIKE '105%'
    AND kip.NOMOR='$nik'
    GROUP BY tk.NOMOR
    #LIMIT 2
    ";
    $bind = $this->db->query($query);
    return $bind;
  }

  public function dataTindakanV2($nopen)
  {
    $query ="
    SELECT pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
    , DATE_FORMAT(tk.MASUK,'%d-%m-%Y %H:%i:%s') TGLREG
    , md.KODE DIAGNOSA
    , (
    SELECT mr.STR
    FROM master.mrconso mr
    WHERE mr.CODE=md.KODE AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN=pd.NOMOR
    LIMIT 1) DIAGNOSA_DESKRIPSI
    , md.UTAMA JENIS
    FROM pendaftaran.pendaftaran pd
    LEFT JOIN master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
    LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
    LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
    LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
    LEFT JOIN master.pasien p ON p.NORM=pd.NORM
    LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
    LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
    LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
    LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN
    LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = pd.NOMOR AND md.`STATUS`=1
    WHERE pd.STATUS IN (1,2) AND tk.REF IS NULL AND tk.STATUS=2 AND jkr.JENIS_KUNJUNGAN NOT IN(3,4) 
    AND tp.RUANGAN LIKE '105%' 
    AND pd.NOMOR='$nopen' AND md.ID IS NOT NULL
    ";
    $bind = $this->db->query($query);
    return $bind;
  }

  public function dataLab($nopen)
  {
    $query = "SELECT p.NORM,master.getNamaLengkap(p.NORM) NAMA
    , p.NOMOR NOPEN,pendK.NOMOR NOKUN,p.TANGGAL TGL_PENDAFTARAN ,pendK.MASUK TGL_KUNJUNGAN, lhl.LIS_TANGGAL TGL_HASIL,r.DESKRIPSI RUANGAN
    , mt.NAMA TINDAKAN_SIMPEL, lhl.LIS_NAMA_TEST PARAMETER, lhl.LIS_HASIL,lhl.LIS_NILAI_NORMAL, lhl.LIS_CATATAN,lhl.LIS_SATUAN
    

    FROM pendaftaran.pendaftaran p

    LEFT JOIN pendaftaran.kunjungan pendK ON pendK.NOPEN=p.NOMOR
    LEFT JOIN lis.hasil_log lhl ON lhl.HIS_NO_LAB=pendK.NOMOR
    LEFT JOIN master.pasien mp ON mp.NORM=p.NORM
    LEFT JOIN master.ruangan r ON pendK.RUANGAN=r.ID
    LEFT JOIN master.tindakan mt ON mt.ID= lhl.HIS_KODE_TEST





    WHERE p.NOMOR='$nopen'
    AND pendK.`STATUS` !=0
    AND r.ID=105070101";

    $bind = $this->db->query($query);
    return $bind;
  }

  public function parameterLab()
  {
    $query = "SELECT mptl.TINDAKAN ID_TINDAKAN_SIMPEL
            , mt.NAMA TINDAKAN_SIMPEL, mptl.ID ID_PARAMETER, mptl.PARAMETER, mptl.INDEKS
            , lhl.NILAI, lhl.SATUAN, ihs.id_param
            
          FROM pendaftaran.kunjungan pendK
            LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pendK.NOMOR
            LEFT JOIN master.tindakan mt ON mt.ID = ltm.TINDAKAN
            LEFT JOIN master.parameter_tindakan_lab mptl ON mptl.TINDAKAN = mt.ID AND mptl.STATUS!=0
            LEFT JOIN layanan.hasil_lab lhl ON lhl.TINDAKAN_MEDIS = ltm.ID AND lhl.PARAMETER_TINDAKAN = mptl.ID
            LEFT JOIN ihs.tb_master_lab ihs ON mptl.ID = ihs.id_param
          WHERE ltm.STATUS=1
            AND ihs.id_param IS NULL
            AND lhl.TANGGAL BETWEEN '2022-07-01 00:00:00' AND '2022-07-20 23:00:00'
            #AND lhl.PARAMETER_TINDAKAN=1476001
          GROUP BY lhl.PARAMETER_TINDAKAN
          
          ORDER BY mptl.TINDAKAN ASC, mptl.INDEKS asc";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function parameterLabDone()
  {
    $query = "SELECT * FROM ihs.tb_master_lab";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function dataSnomed()
  {
    $query = "SELECT tin.ID, tin.NAMA, rtpa.deskripsi PARENT, sno.CODE, sc.DISPLAY, sc.SYSTEM

    FROM master.tindakan_ruangan tru
    
    LEFT JOIN master.tindakan tin ON tin.ID = tru.TINDAKAN
    LEFT JOIN master.referensi_tindakan_penunjang rtp ON rtp.tindakan = tin.ID AND rtp.jenis_penunjang='105070101'
    LEFT JOIN master.referensi_tindakan_penunjang rtpa ON rtpa.id = rtp.parent
    LEFT JOIN ihs.tb_mapping_snomed_specimen sno ON sno.ID_TINDAKAN = tin.ID
    LEFT JOIN ihs.tb_snomed_specimen_type sc ON sc.CODE = sno.CODE
    
    WHERE tru.`STATUS`!=0 AND tru.RUANGAN='105070101' AND tin.`STATUS`!=0

    ORDER BY -sno.CREATED_AT ASC, rtpa.deskripsi DESC, tin.NAMA ASC";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function dataTindakanLoinc()
  {
    $query = "SELECT tin.ID, tin.NAMA, rtpa.deskripsi PARENT, sno.CODE, sno.DISPLAY

    FROM master.tindakan_ruangan tru
    
    LEFT JOIN master.tindakan tin ON tin.ID = tru.TINDAKAN
    LEFT JOIN master.referensi_tindakan_penunjang rtp ON rtp.tindakan = tin.ID AND rtp.jenis_penunjang='105070101'
    LEFT JOIN master.referensi_tindakan_penunjang rtpa ON rtpa.id = rtp.parent
    LEFT JOIN ihs.tb_mapping_loinc_specimen sno ON sno.ID_TINDAKAN = tin.ID
    
    WHERE tru.`STATUS`!=0 AND tru.RUANGAN='105070101' AND tin.`STATUS`!=0

    ORDER BY -sno.CREATED_AT ASC, rtpa.deskripsi DESC, tin.NAMA ASC";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function tindakanPA()
  {
    $query = "SELECT t.ID ID_TINDAKAN, t.NAMA NAMA_TINDAKAN
    FROM master.tindakan_ruangan tr
    LEFT JOIN master.tindakan t ON t.ID = tr.TINDAKAN
    WHERE tr.RUANGAN='105080101' AND tr.STATUS='1'";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function dataTindakanPa()
  {
    $query = "SELECT tmt.ID, tmt.ID_TINDAKAN, t.NAMA NAMA_TINDAKAN
    , IF(tmt.JENIS=1,'Histologi', IF(tmt.JENIS=2, 'Sitologi', IF(tmt.JENIS=3, 'Patmol', 'IHK'))) JENIS
    , tmt.status STATUS
    FROM remun_medis.tb_mapping_tindakan_pa tmt
    LEFT JOIN master.tindakan t ON t.ID = tmt.ID_TINDAKAN";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function dataLoinc()
  {
    $query = "SELECT ml.*, md.display DISPLAY
    FROM ihs.tb_mapping_lab_parameter ml
    LEFT JOIN ihs.tb_mapping_dto md ON md.code = ml.LOINC
    GROUP BY ml.ID";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  function listLoinc()
    {
        $this->db->select("*");
        $this->db->from('ihs.tb_mapping_dto ml');

        $this->db->group_start();
        $this->db->like('ml.nama_pemeriksaan', $_POST['search']['value']);
        $this->db->or_like("ml.kategori", $_POST['search']['value']);
        $this->db->or_like("ml.permintaan", $_POST['search']['value']);
        $this->db->or_like("ml.spesimen", $_POST['search']['value']);
        $this->db->or_like("ml.tipe_hasil_pemeriksaan", $_POST['search']['value']);
        $this->db->or_like("ml.satuan", $_POST['search']['value']);
        $this->db->or_like("ml.metode_analisis", $_POST['search']['value']);
        $this->db->or_like("ml.code", $_POST['search']['value']);
        $this->db->or_like("ml.display", $_POST['search']['value']);
        $this->db->or_like("ml.component", $_POST['search']['value']);
        $this->db->or_like("ml.property", $_POST['search']['value']);
        $this->db->or_like("ml.timing", $_POST['search']['value']);
        $this->db->or_like("ml.system", $_POST['search']['value']);
        $this->db->or_like("ml.scale", $_POST['search']['value']);
        $this->db->or_like("ml.method", $_POST['search']['value']);
        $this->db->or_like("ml.unit_of_measure", $_POST['search']['value']);
        $this->db->group_end();
    }

  function datatableLoinc(){
        $this->listLoinc();
        if($_POST["length"] != -1){
        $this->db->limit($_POST["length"], $_POST["start"]);
        }
        $query = $this->db->get();
        return $query->result();
    }

    function filter_count(){
        $this->listLoinc();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function total_count(){
        $this->listLoinc();
        return $this->db->count_all_results();
    }

  public function dataLoincMaster()
  {
    $query = "SELECT *
    FROM ihs.tb_mapping_dto ml";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function dataSnomedSpecimen()
  {
    $query = "SELECT *
    FROM ihs.tb_snomed_specimen_type tss";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function getMappingLoinc()
  {
    $query = "SELECT * FROM ihs.tb_master_lab";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

  public function parameterLabTanpa()
  {
    $query = "SELECT mptl.TINDAKAN ID_TINDAKAN_SIMPEL
                , mt.NAMA TINDAKAN_SIMPEL, mptl.ID ID_PARAMETER, mptl.PARAMETER, mptl.INDEKS
              
              FROM master.parameter_tindakan_lab mptl
                LEFT JOIN master.tindakan mt ON mt.ID = mptl.TINDAKAN
                LEFT JOIN ihs.tb_master_lab ihs ON mptl.ID = ihs.id_param
              WHERE mptl.`STATUS`!=0 AND ihs.id_param IS NULL";
    $bind = $this->db->query($query);
    return $bind->result_array();
  }

}