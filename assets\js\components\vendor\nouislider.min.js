//
// Form control
//
"use strict";var noUiSlider=function(){if($(".input-slider-container")[0]&&$(".input-slider-container").each(function(){var e=$(this).find(".input-slider"),t=e.attr("id"),n=e.data("range-value-min"),a=e.data("range-value-max"),r=$(this).find(".range-slider-value"),i=r.attr("id"),d=r.data("range-value-low"),u=document.getElementById(t),l=document.getElementById(i);noUiSlider.create(u,{start:[parseInt(d)],connect:[!0,!1],range:{min:[parseInt(n)],max:[parseInt(a)]}}),u.noUiSlider.on("update",function(e,t){l.textContent=e[t]})}),$("#input-slider-range")[0]){var e=document.getElementById("input-slider-range"),t=document.getElementById("input-slider-range-value-low"),n=document.getElementById("input-slider-range-value-high"),a=[t,n];noUiSlider.create(e,{start:[parseInt(t.getAttribute("data-range-value-low")),parseInt(n.getAttribute("data-range-value-high"))],connect:!0,range:{min:parseInt(e.getAttribute("data-range-value-min")),max:parseInt(e.getAttribute("data-range-value-max"))}}),e.noUiSlider.on("update",function(e,t){a[t].textContent=e[t]})}}();