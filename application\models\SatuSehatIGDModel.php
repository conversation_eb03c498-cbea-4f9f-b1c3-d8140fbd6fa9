<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatIGDModel extends CI_Model {

	function sendBed()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_bed_kosong_igd bki
				WHERE bki.STATUS = 1 
				AND (bki.HTTPCODE IS NULL OR bki.HTTPCODE != 201)";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendEncounter()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_encounter_igd eis
				WHERE eis.NOPEN NOT IN (select es.nopen from ihs.tb_encounter es where es.status = 1 
				AND es.jenis = 2) AND eis.STATUS = 1 
				AND (eis.HTTPCODE IS NULL)
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function updateRuangTriase()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_masuk_triase_igd dti
				WHERE dti.HTTPCODE IS NULL AND dti.STATUS = 1
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendTriaseTransport()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_triasetransport_igd dti
				WHERE dti.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendTriaseRujukan()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_triaserujukan_igd dti
				WHERE dti.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function TriaseKondisi($id)
	{
		$query = "SELECT df.NOMOR NOPEN
				, en.encounter ID_ENCOUNTER
				, ps.ID_IHS ID_IHS_PASIEN
				, master.getNamaLengkap(ps.NORM) NAMAPASIEN
				, pss.NAMA NAMAPASIEN_2
				, dok.ID_IHS ID_IHS_DOKTER
				, master.getNamaLengkapPegawai(dok.NIP_SIMPEL) NAMADOKTER
				, tri.tanggal_masuk CREATED_AT_TRIASE
				, df.TANGGAL TGL_DAFTAR
				, CASE
						WHEN ref.id_referensi = 479 THEN '1'
						WHEN ref.id_referensi = 480 THEN '2'
						WHEN ref.id_referensi = 481 THEN '3'
						WHEN ref.id_referensi = 482 THEN '4'
						WHEN ref.id_referensi = 483 THEN '5'
						ELSE ref.referensi
					END ATS
				, CASE
						WHEN ref.id_referensi = 479 THEN 'LA6112-2'
						WHEN ref.id_referensi = 480 THEN 'LA6113-0'
						WHEN ref.id_referensi = 481 THEN 'LA6114-8'
						WHEN ref.id_referensi = 482 THEN 'LA6115-5'
						WHEN ref.id_referensi = 483 THEN 'LA10137-0'
						ELSE ref.referensi
					END ATS_LOINC
				, en.id_ihs_pasien ID_IHS_PASIEN_DUMMY
				, en.id_ihs_practitioner ID_IHS_PRACTITIONER_DUMMY
				
				FROM pendaftaran.pendaftaran df
				
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
				LEFT JOIN keperawatan.tb_triase tri ON tri.nokun = kun.NOMOR
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = kun.NOPEN
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pss ON pss.NORM = df.NORM
				LEFT JOIN ihs.tb_dokter dok ON dok.ID_DOKTER = tp.DOKTER
				LEFT JOIN master.dokter dokk ON dokk.ID = tp.DOKTER
				LEFT JOIN pendaftaran.surat_rujukan_pasien srp ON srp.NOPEN = df.NOMOR
				LEFT JOIN keperawatan.tb_triase_ats ats ON ats.id_triase = tri.id
				LEFT JOIN db_master.variabel v ON v.id_variabel = ats.ats_detail
				LEFT JOIN db_master.referensi ref ON ref.id_referensi = v.id_referensi
				
				WHERE df.NOMOR = ?
				AND ps.ID_IHS IS NOT NULL
				AND dok.ID_IHS IS NOT NULL
				AND en.nopen IS NOT NULL
				AND tri.id IS NOT NULL
				AND ats.id IS NOT NULL";
		$bind = $this->db->query($query, array(2,$id));
		return $bind;
	}

	function cekConditionIGD($jenisEnc,$jenisCondition)
	{
		$query = "SELECT enc.encounter, enc.nopen 

					FROM ihs.tb_encounter_ enc
					
						LEFT JOIN ihs.tb_condition_igd con ON enc.encounter = con.id_encounter AND con.kategori='$jenisCondition'
						LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN=enc.nopen
						
					WHERE
				enc.jenis = '$jenisEnc'
				AND enc.nopen NOT IN (
					SELECT cn.nopen
					FROM ihs.tb_condition_igd cn WHERE cn.kategori = '$jenisCondition')
					GROUP BY enc.nopen
					LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function AnamnesisKeluhan($id)
	{
		$query = "SELECT df.NOMOR NOPEN
		, DATE_FORMAT(tm.created_at, '%d-%m-%Y') TGL_ANAMNESIS_MEDIS
		, an.keluhan_utama KELUHAN_UTAMA
		, dm.ID ID_DIAGNOSA
		, dm.ICD KODE_ICD
		, (SELECT mr.STR FROM master.mrconso mr
			WHERE mr.CODE=dm.ICD GROUP BY mr.CODE) DIAGNOSA
		, en.id_ihs_pasien ID_IHS_PASIEN_DUMMY
		, master.getNamaLengkap(ps.NORM) NAMAPASIEN
		, pss.NAMA NAMAPASIEN_2
		, en.encounter ID_ENCOUNTER
		, DATE_FORMAT(df.TANGGAL, '%Y-%m-%d') TGL_DAFTAR
		
		FROM pendaftaran.pendaftaran df
		
		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
		LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = kun.NOPEN
		LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
		LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
		LEFT JOIN master.pasien pss ON pss.NORM = df.NORM
		LEFT JOIN ihs.tb_dokter dok ON dok.ID_DOKTER = tp.DOKTER
		LEFT JOIN master.dokter dokk ON dokk.ID = tp.DOKTER
		LEFT JOIN master.diagnosa_masuk dm ON dm.ID = df.DIAGNOSA_MASUK
		LEFT JOIN medis.tb_medis tm ON tm.nopen = en.nopen
		LEFT JOIN medis.tb_anamnesa an ON an.id_emr = tm.id_emr
		
		WHERE df.NOMOR = ?
		AND	ps.ID_IHS IS NOT NULL
		AND dok.ID_IHS IS NOT NULL
		AND en.nopen IS NOT NULL
		AND dm.ICD != '.'
		
		GROUP BY df.NOMOR";
		$bind = $this->db->query($query, array(2,$id));
		return $bind;
	}

	function AnamnesisRiwayatPenyakit($id)
	{
		// $query = "SELECT df.NOMOR NOPEN
		// , TRIM(UPPER(md.KODE)) DIAGNOSA
		// , (SELECT mr.STR
		// 	FROM master.mrconso mr
		// 	WHERE mr.CODE = TRIM(md.KODE) AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = df.NOMOR
		// LIMIT 1) DIAGNOSA_MRCONSO
		// , en.id_ihs_pasien ID_IHS_PASIEN_DUMMY
		// , master.getNamaLengkap(ps.NORM) NAMAPASIEN
		// , pss.NAMA NAMAPASIEN_2
		// , en.encounter ID_ENCOUNTER
		// , DATE_FORMAT(df.TANGGAL, '%Y-%m-%d') TANGGAL_DAFTAR
		
		// FROM pendaftaran.pendaftaran df
		
		// LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
		// LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = kun.NOPEN
		// LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
		// LEFT JOIN master.diagnosa_masuk mdm ON df.DIAGNOSA_MASUK = mdm.ID
		// LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = df.NOMOR AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
		// LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
		// LEFT JOIN master.pasien pss ON pss.NORM = df.NORM
		// LEFT JOIN ihs.tb_dokter dok ON dok.ID_DOKTER = tp.DOKTER
		// LEFT JOIN master.dokter dokk ON dokk.ID = tp.DOKTER
		
		// WHERE df.NOMOR = ?
		// AND ps.ID_IHS IS NOT NULL
		// AND dok.ID_IHS IS NOT NULL
		// AND en.nopen IS NOT NULL
		// AND md.ID NOT IN (SELECT c.id_diagnosa FROM ihs.tb_condition c)
		
		
		// GROUP BY df.NOMOR";
		$query = "SELECT a.NOMOR NOPEN_DIAGNOSA, en.encounter, en.id_ihs_pasien, en.id_ihs_practitioner
				, DATE_FORMAT(tk.MASUK, '%Y-%m-%d') TANGGAL_DAFTAR
				, TRIM(UPPER(md.KODE)) DIAGNOSA
				, (SELECT mr.STR
					FROM master.mrconso mr
					WHERE mr.CODE = TRIM(md.KODE) AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = a.NOMOR
				LIMIT 1) DIAGNOSA_MRCONSO
				, ps.NAMA NAMAPASIEN
				, md.ID ID_DIAGNOSA
				
			
			FROM
				
				(SELECT p.NOMOR
				FROM pendaftaran.pendaftaran px 
					LEFT JOIN pendaftaran.pendaftaran p ON p.NORM = px.NORM AND p.TANGGAL < px.TANGGAL
					LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = p.NOMOR 
						AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
				WHERE px.NOMOR = ? AND md.ID IS NOT NULL 
				ORDER BY p.TANGGAL DESC 
				LIMIT 1) a
				
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = ?
			LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = p.NOMOR
			LEFT JOIN ihs.tb_encounter_ en ON en.nopen = p.NOMOR AND en.jenis = ?
			LEFT JOIN master.pasien ps ON ps.NORM = p.NORM
			LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = a.NOMOR AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
			, pendaftaran.kunjungan tk
			
			WHERE p.NOMOR=tk.NOPEN AND tp.RUANGAN=tk.RUANGAN AND tk.REF IS NULL AND tk.STATUS IN (1,2) AND md.UTAMA = 2";
		$bind = $this->db->query($query, array($id,$id,2));
		return $bind;
	}

	function cekAsesmenNyeri($jenisEnc,$jenisAsesmen)
	{
		$query = "SELECT enc.encounter, enc.nopen 

					FROM ihs.tb_encounter_ enc
					
						LEFT JOIN ihs.tb_observation_igd asm ON enc.encounter = asm.id_encounter AND asm.kategori='$jenisAsesmen'
						LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN=enc.nopen
						
					WHERE
				enc.jenis = '$jenisEnc'
				AND enc.nopen NOT IN (
					SELECT ase.nopen
					FROM ihs.tb_observation_igd ase WHERE ase.kategori = '$jenisAsesmen')
					GROUP BY enc.nopen
					LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function AsesemenNyeriIGD($id)
	{
		$query = "SELECT
				df.NOMOR NOPEN
				, master.getNamaLengkap(df.NORM) NAMAPASIEN
				, pas.NAMA NAMAPASIEN2
				, ps.ID_IHS ID_IHS_PASIEN
				, ps.NORM NOMR
				, idok.ID_IHS ID_IHS_DOKTER
				, master.getNamaLengkapPegawai(idok.NIP_SIMPEL) NAMADOKTER
				, DATE_FORMAT(pkx.MASUK,'%Y-%m-%d %H:%i:%s') TANGGALMASUK
				, DATE_FORMAT(pkx.KELUAR,'%Y-%m-%d %H:%i:%s') TANGGALKELUAR
				, dg.ICD ICDDIAGNOSA
				, (SELECT mr.STR FROM master.mrconso mr
					WHERE mr.CODE=dg.ICD GROUP BY mr.CODE) DESKRIPSIDIAGNOSA
				, CASE
						WHEN sn.metode = 17 THEN 'false'
						WHEN sn.metode != 17 THEN 'true'
					END NYERI
					
				FROM keperawatan.tb_skrining_nyeri sn
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = sn.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ?	AND kun.STATUS != 0
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN master.diagnosa_masuk dg ON dg.ID = df.DIAGNOSA_MASUK
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
					
				WHERE
				df.NOMOR = ?
				AND kun.NOMOR IS NOT NULL
				AND	en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND idok.ID_IHS IS NOT NULL
				AND ps.ID_IHS IS NOT NULL
				AND sn.data_source = 10 AND sn.id IS NOT NULL
				
				GROUP BY df.NOMOR
				
				
				#ON tri.nokun = pk.NOMOR";
		$bind = $this->db->query($query, array(105011201,2,$id));
		return $bind;
	}

	function SkalaNyeriIGD($id)
	{
		$query = "SELECT
					df.NOMOR NOPEN
					, master.getNamaLengkap(df.NORM) NAMAPASIEN
					, pas.NAMA NAMAPASIEN2
					, ps.ID_IHS ID_IHS_PASIEN
					, ps.NORM NOMR
					, idok.ID_IHS ID_IHS_DOKTER
					, master.getNamaLengkapPegawai(idok.NIP_SIMPEL) NAMADOKTER
					, DATE_FORMAT(pkx.MASUK,'%Y-%m-%d %H:%i:%s') TANGGALMASUK
					, DATE_FORMAT(pkx.KELUAR,'%Y-%m-%d %H:%i:%s') TANGGALKELUAR
					, dg.ICD ICDDIAGNOSA
					, (SELECT mr.STR FROM master.mrconso mr
						WHERE mr.CODE=dg.ICD GROUP BY mr.CODE) DESKRIPSIDIAGNOSA
					, CASE
							WHEN sn.metode = 17 THEN 'false'
							WHEN sn.metode != 17 THEN 'true'
						END NYERII
					, CASE
							WHEN sn.skor IN (321,322,323,324,325,326,327,328,329,330,423) THEN skor.variabel
							ELSE '0'
						END SKOR_SKALA_NYERI
						
					FROM keperawatan.tb_skrining_nyeri sn
					
					LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = sn.nokun
					LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
					LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
					LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
					LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
					LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
					LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
					LEFT JOIN master.diagnosa_masuk dg ON dg.ID = df.DIAGNOSA_MASUK
					LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
					LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
					LEFT JOIN db_master.variabel skor ON skor.id_variabel = sn.skor AND skor.id_referensi = 114
						
					WHERE
					df.NOMOR = ?
					AND kun.NOMOR IS NOT NULL
					AND	en.nopen IS NOT NULL
					AND rkt.RUANG_KAMAR != 234
					AND idok.ID_IHS IS NOT NULL
					AND ps.ID_IHS IS NOT NULL
					AND sn.metode = 18
					AND	sn.data_source = 10 AND sn.id IS NOT NULL
					
					GROUP BY df.NOMOR";
		$bind = $this->db->query($query, array(105011201,2,$id));
		return $bind;
	}

	function LokasiNyeriIGD($id)
	{
		$query = "SELECT df.NOMOR NOPEN
				, pas.NAMA NAMAPASIEN2
				, ps.ID_IHS ID_IHS_PASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, DATE_FORMAT(sn.created_at, '%Y-%m-%d %H:%i:%s') TANGGAL_SKRININGNYERI
				, sn.regio LOKASI_NYERI
				, en.encounter ID_ENCOUNTER
				
				FROM keperawatan.tb_skrining_nyeri sn
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = sn.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				
				WHERE
				df.NOMOR = ? 
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND	rkt.RUANG_KAMAR != 234
				AND idok.ID_IHS IS NOT NULL
				AND ps.ID_IHS IS NOT NULL
				AND	sn.data_source = 10 AND sn.regio != ''
				
				GROUP BY df.NOMOR";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function PenyebabNyeriIGD($id)
	{
		$query = "SELECT pkx.NOMOR KUNNJUNGAN1
				, kun.NOMOR KUNJUNGAN2
				, df.NOMOR NOPEN
				, pas.NAMA NAMAPASIEN2
				, ps.ID_IHS ID_IHS_PASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, DATE_FORMAT(sn.created_at, '%Y-%m-%d %H:%i:%s') TANGGAL_SKRININGNYERI
				, p.variabel PENYEBAB
				, en.encounter ID_ENCOUNTER
				
				FROM keperawatan.tb_skrining_nyeri sn
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = sn.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN db_master.variabel p ON p.id_variabel = sn.provokative AND p.id_referensi = 8
				
				WHERE
				df.NOMOR = ? 
				AND kun.NOMOR IS NOT NULL
				AND	en.nopen IS NOT NULL
				AND	rkt.RUANG_KAMAR != 234
				AND	idok.ID_IHS IS NOT NULL
				AND	ps.ID_IHS IS NOT NULL
				AND	sn.data_source = 10
				
				GROUP BY df.NOMOR";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function RisikoJatuhIGD($id)
	{
		$query = "SELECT en.encounter
				, df.NOMOR NOPEN
				, pas.NAMA NAMAPASIEN2
				, ps.ID_IHS ID_IHS_PASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, DATE_FORMAT(rj.created_at, '%Y-%m-%d %H:%i:%s') TANGGAL_RISIKOJATUH
				, (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) TOTAL_SKOR
				, CASE
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 0 AND 24 THEN 'OI000026'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 25 AND 44 THEN 'OI000027'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) > 45 THEN 'OI000028'
					END KODE_TERMINOLOGI
				, CASE
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 0 AND 24 THEN '0 - 24 (Risiko Rendah)'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 25 AND 44 THEN '25 - 44 (Risiko Sedang)'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) > 45 THEN '> 45 (Risiko Tinggi)'
					END DESKRIPSI_TERMINOLOGI
				, CASE
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 0 AND 24 THEN 'Risiko Rendah'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) BETWEEN 25 AND 44 THEN 'Risiko Sedang'
					WHEN (v1.nilai + v2.nilai + v3.nilai + v4.nilai + v5.nilai + v6.nilai) > 45 THEN 'Risiko Tinggi'
					END STATUS_MFS
				, en.encounter ID_ENCOUNTER
				
				FROM keperawatan.tb_pengkajian_risiko_jatuh_pasien_dewasa rj
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = rj.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN db_master.variabel v1 ON v1.id_variabel = rj.riwayat_jatuh
				LEFT JOIN db_master.variabel v2 ON v2.id_variabel = rj.diagnosis_sekunder
				LEFT JOIN db_master.variabel v3 ON v3.id_variabel = rj.alat_bantu
				LEFT JOIN db_master.variabel v4 ON v4.id_variabel = rj.infus
				LEFT JOIN db_master.variabel v5 ON v5.id_variabel = rj.cara_berjalan
				LEFT JOIN db_master.variabel v6 ON v6.id_variabel = rj.status_mental
				
				WHERE
				df.NOMOR = ?
				AND kun.NOMOR IS NOT NULL
				AND	en.nopen IS NOT NULL
				AND	rkt.RUANG_KAMAR != 234
				AND	idok.ID_IHS IS NOT NULL
				AND	ps.ID_IHS IS NOT NULL
				
				GROUP BY df.NOMOR";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function TingkatKesadaranIGD($id)
	{
		$query = "SELECT df.NOMOR NOPEN
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, en.encounter ID_ENCOUNTER
				, DATE_FORMAT(sd.created_at, '%Y-%m-%d %H:%i:%s') CREATEDAT
				, CASE
					WHEN sd.kesadaran = 9 THEN '248234008'
					WHEN sd.kesadaran = 10 THEN '130987000'
					WHEN sd.kesadaran = 11 THEN '300202002'
					WHEN sd.kesadaran = 12 THEN '450847001'
					WHEN sd.kesadaran = 105 THEN '422768004'
					END KESADARAN
				, CASE
					WHEN sd.kesadaran = 9 THEN 'Mentally alert'
					WHEN sd.kesadaran = 10 THEN 'Acute confusion'
					WHEN sd.kesadaran = 11 THEN 'Response voice'
					WHEN sd.kesadaran = 12 THEN 'Response pain'
					WHEN sd.kesadaran = 105 THEN 'Unresponsive'
					END DESKRIPSI_KESADARAN
				
				
				FROM db_pasien.tb_kesadaran sd
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = sd.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN master.diagnosa_masuk dg ON dg.ID = df.DIAGNOSA_MASUK
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN db_master.variabel ksd ON ksd.id_variabel = sd.kesadaran AND ksd.id_referensi = 5
				
				WHERE
				df.NOMOR = ?
				AND sd.data_source = 10
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND idok.ID_IHS IS NOT NULL
				AND ps.ID_IHS IS NOT NULL";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	public function cekObservationIGD()
	{
		$query = "SELECT enc.nopen NOPEN, enc.encounter IDENCOUNTER
		FROM ihs.tb_encounter_ enc
		WHERE enc.nopen NOT IN (SELECT obs.nopen FROM ihs.tb_observation_igd obs)
		LIMIT 5";

		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function cekProcedureIGD()
	{
		$query = "SELECT DISTINCT enc.nopen NOPEN, md.ID ID_PROSEDUR, enc.encounter IDENCOUNTER
		, p.NORM, CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN, enc.created_at
		, p.TANGGAL TGL_PENDAFTARAN
		, TRIM(md.KODE) KODE, mr.STR DESKRIPSI, md.TANGGAL
		, enc.id_ihs_pasien IDPASIEN
		, enc.id_ihs_practitioner IDPRACTITIONER
		FROM ihs.tb_encounter_ enc
		LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = enc.nopen
		LEFT JOIN medicalrecord.prosedur md ON md.NOPEN = enc.nopen
		LEFT JOIN master.mrconso mr ON mr.CODE=TRIM(md.KODE) AND md.`STATUS`=1 AND mr.SAB='ICD9CM_2005' AND TTY IN ('PX', 'PT')
		WHERE md.ID NOT IN (
			SELECT pro.prosedur
			FROM ihs.tb_procedure_igd pro) AND md.ID IS NOT NULL AND enc.jenis = 2
		LIMIT 10";

		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	function VitalSignIGD($id)
	{
				$query = "SELECT df.NOMOR NOPEN
				, en.encounter ID_ENCOUNTER
				, idok.ID_IHS ID_IHS_DOKTER
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, DATE_FORMAT(tv.created_at, '%d-%m-%Y %H:%i:%s') CREATEDAT
				, tv.nadi NADI
				, tv.pernapasan PERNAPASAN
				, tv.td_sistolik SISTOLE
				, tv.td_diastolik DIASTOLE
				, tv.suhu SUHU
				
				FROM db_pasien.tb_tanda_vital tv
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = tv.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				
				WHERE
				df.NOMOR = ? 
				AND tv.data_source = 10
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND idok.ID_IHS IS NOT NULL
				AND ps.ID_IHS IS NOT NULL";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function PsikologisIGD($id)
	{
				$query = "SELECT df.NOMOR NOPEN
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, en.encounter ID_ENCOUNTER
				, DATE_FORMAT(k.created_at, '%d-%m-%Y %H:%i:%s') CREATEDAT
				, CASE
					WHEN pf.psikologis = 1655 THEN '17326005'
					WHEN pf.psikologis = 34 THEN '48694002'
					WHEN pf.psikologis = 36 THEN '1402001'
					WHEN pf.psikologis = 37 THEN '75408008'
					WHEN pf.psikologis = 33 THEN '420038007'
					WHEN pf.psikologis = 38 THEN '74964007'
					END PSIKOLOGIS
				, CASE
					WHEN pf.psikologis = 1655 THEN 'Well in self'
					WHEN pf.psikologis = 34 THEN 'Feeling anxious'
					WHEN pf.psikologis = 36 THEN 'Afraid'
					WHEN pf.psikologis = 37 THEN 'Feeling angry'
					WHEN pf.psikologis = 33 THEN 'Feeling unhappy'
					WHEN pf.psikologis = 38 THEN 'Other'
					END DESKRIPSI_PSIKOLOGIS
				
				
				FROM keperawatan.tb_pemeriksaan_fisik pf
				
				LEFT JOIN keperawatan.tb_keperawatan k ON k.id_emr = pf.id_emr
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOMOR = k.nokun
				LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = pkx.NOPEN
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN master.diagnosa_masuk dg ON dg.ID = df.DIAGNOSA_MASUK
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN db_master.variabel psi ON psi.id_variabel = pf.psikologis AND psi.id_referensi = 13
				
				WHERE
				df.NOMOR = ? 
				AND k.jenis = 9
				AND	kun.NOMOR IS NOT NULL
				AND	en.nopen IS NOT NULL
				AND	rkt.RUANG_KAMAR != 234
				AND	idok.ID_IHS IS NOT NULL
				AND	ps.ID_IHS IS NOT NULL
				AND	pf.psikologis IN (1655,34,36,37,33,38)";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function DiagnosisAwalIGD($id)
	{
				$query = "SELECT df.NOMOR
				, df.NORM
				, md.KODE ICD10
				, (SELECT mr.STR
						FROM master.mrconso mr
						WHERE mr.CODE = TRIM(md.KODE) AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = df.NOMOR
					LIMIT 1) DESKRIPSI_ICD10
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, en.encounter ID_ENCOUNTER
				, DATE_FORMAT(md.TANGGAL, '%Y-%m-%d %H:%i:%s') CREATED_AT
				, CASE
					WHEN mm.desk_diagnosa_medis IS NULL THEN '-'
					ELSE mm.desk_diagnosa_medis
					END DESKRIPSI_NOTES
				
				FROM pendaftaran.pendaftaran df
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOPEN = df.NOMOR
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = pkx.NOPEN AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = df.NOMOR AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
				LEFT JOIN medis.tb_medis med ON med.nokun = pkx.NOMOR
				LEFT JOIN medis.tb_masalah_medis_kep mm ON mm.id_emr =  med.id_emr
				
				WHERE
				df.NOMOR = ? 
				AND md.ID IS NOT NULL
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND ps.ID_IHS IS NOT NULL
				GROUP BY df.NOMOR
				ORDER BY df.TANGGAL DESC";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function DiagnosisKerjaIGD($id)
	{
				$query = "SELECT df.NOMOR
				, df.NORM
				, md.KODE ICD10
				, (SELECT mr.STR
						FROM master.mrconso mr
						WHERE mr.CODE = TRIM(md.KODE) AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = df.NOMOR
					LIMIT 1) DESKRIPSI_ICD10
				, md.UTAMA JENIS
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, en.encounter ID_ENCOUNTER
				, DATE_FORMAT(md.TANGGAL, '%Y-%m-%d %H:%i:%s') CREATED_AT
				, CASE
					WHEN mm.desk_diagnosa_medis IS NULL THEN ''
					ELSE mm.desk_diagnosa_medis
					END DESKRIPSI_NOTES
				
				FROM pendaftaran.pendaftaran df
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOPEN = df.NOMOR
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = pkx.NOPEN AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = df.NOMOR AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
				LEFT JOIN medis.tb_medis med ON med.nokun = pkx.NOMOR
				LEFT JOIN medis.tb_masalah_medis_kep mm ON mm.id_emr =  med.id_emr
				
				WHERE
				df.NOMOR = ?
				AND md.ID IS NOT NULL
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND ps.ID_IHS IS NOT NULL
				GROUP BY md.KODE
				ORDER BY df.NORM ASC";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function TindakanIGD($id)
	{
				$query = "SELECT df.NOMOR
				, df.NORM
				, '373110003' KATEGORI
				, 'Emergency procedure' DESKRIPSI_KATEGORI
				, mp.ID ID_PROSEDUR
				, mp.KODE ICD9
				, (SELECT mr.STR
						FROM master.mrconso mr
						WHERE mr.CODE = mp.KODE AND mr.SAB='ICD9CM_2005' AND TTY IN ('PX', 'PT') AND mp.NOPEN = df.NOMOR
					LIMIT 1) DESKRIPSI_ICD9
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, idok.ID_IHS ID_IHS_DOKTER
				, master.getNamaLengkapPegawai(idok.NIP_SIMPEL) NAMA_DOKTER
				, en.encounter ID_ENCOUNTER
				, DATE_FORMAT(kun.MASUK, '%Y-%m-%d %H:%i:%s') TGL_MULAI
				, DATE_FORMAT(kun.KELUAR, '%Y-%m-%d %H:%i:%s') TGL_SELESAI
				, md.KODE ICD10
				, (SELECT mr.STR
						FROM master.mrconso mr
						WHERE mr.CODE = md.KODE AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = df.NOMOR
					LIMIT 1) DESKRIPSI_ICD10
				
				FROM pendaftaran.pendaftaran df
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOPEN = df.NOMOR
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = pkx.NOPEN AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = df.NOMOR
				LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = df.NOMOR AND md.STATUS = 1 AND md.VERSI = 5 AND md.ID IS NOT NULL
				LEFT JOIN medicalrecord.prosedur mp ON mp.NOPEN = df.NOMOR AND mp.STATUS = 1 AND mp.VERSI = 5 AND mp.ID IS NOT NULL
				
				WHERE
				df.NOMOR = ?
				AND mp.KODE IS NOT NULL
				AND	md.KODE IS NOT NULL
				AND kun.NOMOR IS NOT NULL
				AND	en.nopen IS NOT NULL
				AND	rkt.RUANG_KAMAR != 234
				AND	ps.ID_IHS IS NOT NULL
				GROUP BY mp.KODE
				ORDER BY df.NOMOR ASC";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function KondisiPulangIGD($id)
	{
				$query = "SELECT df.NOMOR
				, df.NORM
				, pp.KEADAAN
				, CASE
					WHEN pp.KEADAAN = 1 THEN '359746009'
					WHEN pp.KEADAAN = 2 THEN '268910001'
					WHEN pp.KEADAAN = 3 THEN '162668006'
					WHEN pp.KEADAAN = 4 THEN 'exp-lt48h'
					WHEN pp.KEADAAN = 5 THEN 'exp-gt48h'
					ELSE 'oth'
					END KODE_KEADAAN
				, k.DESKRIPSI PP_DESKRIPSI
				, CASE
					WHEN pp.KEADAAN = 1 THEN 'Patient`s condition stable'
					WHEN pp.KEADAAN = 2 THEN 'Patient`s condition improved'
					WHEN pp.KEADAAN = 3 THEN 'Patient`s condition unstable'
					WHEN pp.KEADAAN = 4 THEN 'Meninggal < 48 jam'
					WHEN pp.KEADAAN = 5 THEN 'Meninggal > 48 jam'
					WHEN pp.KEADAAN = 6 THEN 'Death on Arrival'
					WHEN pp.KEADAAN = 7 THEN 'Pindah Ruang Kelas'
					WHEN pp.KEADAAN = 8 THEN 'Bluecode'
					WHEN pp.KEADAAN = 9 THEN 'Meninggal < 8 Jam'
					WHEN pp.KEADAAN = 10 THEN 'Meninggal > 8 Jam'
					END DESKRIPSI_KEADAAN
				, ps.ID_IHS ID_IHS_PASIEN
				, pas.NAMA NAMAPASIEN
				, en.encounter ID_ENCOUNTER
				
				FROM pendaftaran.pendaftaran df
				
				LEFT JOIN pendaftaran.kunjungan pkx ON pkx.NOPEN = df.NOMOR
				LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = pkx.NOPEN AND kun.RUANGAN = ? AND kun.STATUS != 0
				LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
				LEFT JOIN layanan.pasien_pulang pp ON pp.KUNJUNGAN = kun.NOMOR
				LEFT JOIN master.referensi k ON k.ID = pp.KEADAAN AND k.JENIS = 46
				LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
				LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
				LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
				
				WHERE
				df.NOMOR = ?
				AND kun.NOMOR IS NOT NULL
				AND en.nopen IS NOT NULL
				AND rkt.RUANG_KAMAR != 234
				AND ps.ID_IHS IS NOT NULL
				AND pp.KEADAAN IS NOT NULL
				GROUP BY df.NOMOR
				ORDER BY df.NOMOR ASC";
				$bind = $this->db->query($query, array(105011201,2,$id));
				return $bind;
	}

	function PasienPulang()
	{
		$query = "SELECT df.NOMOR NOPEN
		, en.encounter ID_ENCOUNTER
		, dds.ID_IHS_PASIEN_DUMMY
		, master.getNamaLengkap(df.NORM) NAMAPASIEN
		, pas.NAMA NAMAPASIEN2
		, ps.NORM NOMR
		, dds.ID_IHS_DOKTER_DUMMY
		, master.getNamaLengkapPegawai(idok.NIP_SIMPEL) NAMADOKTER
		, DATE_FORMAT(kun.MASUK,'%Y-%m-%d %H:%i:%s') TANGGALMASUK
		, DATE_FORMAT(kun.KELUAR,'%Y-%m-%d %H:%i:%s') TANGGALKELUAR
		, rkt.RUANG_KAMAR
		, rkt.TEMPAT_TIDUR BED
		, ld.id_ihs ID_IHS_BED
		, ld.description NAMA_BED
		
		FROM pendaftaran.pendaftaran df
		
		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
		LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = kun.NOPEN
		LEFT JOIN master.ruangan ru ON ru.ID = kun.RUANGAN
		LEFT JOIN ihs.tb_dokter idok ON idok.ID_DOKTER = tp.DOKTER
		LEFT JOIN ihs.tb_pasien ps ON ps.NORM = df.NORM
		LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
		LEFT JOIN master.diagnosa_masuk dg ON dg.ID = df.DIAGNOSA_MASUK
		LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR
		LEFT JOIN master.ruang_kamar_tidur rkt ON rkt.ID = kun.RUANG_KAMAR_TIDUR
		LEFT JOIN ihs.tb_location_dummy ld ON ld.id_ruangan = rkt.ID
		LEFT JOIN ihs.data_dummy_satset dds ON dds.NOMR = ps.NORM
		
		WHERE df.STATUS != 0
		AND ru.ID = ?
		AND ru.JENIS_KUNJUNGAN = 3
		AND ps.ID_IHS IS NOT NULL
		AND idok.ID_IHS IS NOT NULL
		AND en.nopen IS NOT NULL
		AND rkt.RUANG_KAMAR != ?
		AND dds.ID_IHS_PASIEN_DUMMY IS NOT NULL
		
		ORDER BY rkt.TEMPAT_TIDUR ASC";
		$bind = $this->db->query($query, array(105011201,234));
		return $bind;
	}

	function ConditionPulang($id)
	{
		$query = "SELECT ai.id ID_IHS_ANAMNESIS_KELUHAN
		, dm.ICD KODE_ICD
		, (SELECT mr.STR FROM master.mrconso mr
			WHERE mr.CODE=dm.ICD GROUP BY mr.CODE) DIAGNOSA
		
		FROM ihs.tb_anamnesis_igd ai
		
		LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = ai.nopen
		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
		LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = kun.NOPEN
		LEFT JOIN ihs.tb_encounter_ en ON en.nopen = df.NOMOR AND en.jenis = ?
		LEFT JOIN master.diagnosa_masuk dm ON dm.ID = df.DIAGNOSA_MASUK
		
		WHERE
		df.NOMOR = ?
		AND kun.REF IS NULL
		AND	en.nopen IS NOT NULL
		AND	ai.id IS NOT NULL";
		$bind = $this->db->query($query, array(2,$id));
		return $bind;
	}
}
