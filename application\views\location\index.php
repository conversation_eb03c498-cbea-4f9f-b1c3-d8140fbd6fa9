<h1>Location Menu</h1>
<ul class="nav nav-tabs">
  <li class="nav-item">
    <a class="nav-link active" href="#send-location" data-toggle="tab">Send Location</a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="#search-location" data-toggle="tab">Search Location</a>
  </li>
</ul>

<div class="tab-content">
  <div class="tab-pane active" id="send-location">
    <form id="formPostLocation">
      </br>
      <div class="row col-md-12">
        <label class="col-md-2">Category</label>
        <label class="offset-md-1 col-md-2">PartOf</label>
      </div>
      <div class="row col-md-12">
        <select name="category" class="form-control col-md-2 category-location">
          <option value="si" data-id="site">Site</option>
          <option value="bu" data-id="building">Building</option>
          <option value="ro" data-id="room" selected>Room</option>
        </select>
        <select name="partof" class="form-control col-md-6 offset-md-1 partof-location">
          <option value="">Pilih</option>
          <?php 
            foreach($getPartOf as $list):
          ?>
            <option value="<?= $list['id_ihs'] ?>" data-id="<?= $list['description'] ?>" 
            <?php if($list['id'] == 5){
              echo "selected";
            } else {
              echo "";
            } ?>
            ><?= $list['description'] ?></option>
          <?php
            endforeach;
          ?>
        </select>
      </div>
      <div class="row col-md-12">
        <label class="col-md-6"><br>Name</label>
        <label class="col-md-6"><br>Description</label>
      </div>
      <div class="row col-md-12">
        <div style="display: none;" class="col-md-5">
          <input type="text" name="name" placeholder="name" class="form-control">
        </div>
        <div class="col-md-5">
          <select name="ruangan" id="ruangan" placeholder="name" class="form-control ruangan-location">
            <?php foreach($getRuangan as $list): ?>
              <option value="<?= $list['DESKRIPSI'] ?>" data-id="<?= $list['ID_RUANGAN'] ?>"><?= $list['DESKRIPSI'] ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div style="display: none;" class="offset-md-1 col-md-6">
          <input type="text" name="description" placeholder="description" class="form-control">
        </div>
      </div>
      <br>
      <button type="submit" id="btnPostLocation" style="margin-left: 15px;" class="btn btn-warning">Save</button>
      <br>
    </form>
    <label>Result :</label>
    <pre id="resultPostLocation"></pre>
  </div>
  <div class="tab-pane" id="search-location">
    <form id="formSearchLocation">
      <label><br>ID IHS</label>
      <div class="row col-md-12">
        <input type="text" name="id_ihs" placeholder="id ihs" class="form-control col-md-3" required>
        <button type="submit" id="btnSearchLocation" style="margin-left: 15px;" class="btn btn-warning">Search</button>
        <!-- <button type="submit" id="btnSearchPatientByNIK" style="margin-left: 15px;" class="btn btn-warning">Search</button> -->
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="resultSearchLocation"></pre>
  </div>
</div>
<script>
  $(document).ready(function() {
    $('#ruangan').select2();

    $('#formPostLocation').submit(function (event) {
      var name = $("input[name=name]").val();
      var description = $("input[name=description]").val();
      var category = $("select[name=category]").val();
      var partof = $("select[name=partof]").val();
      var ruangan = $("select[name=ruangan]").val();
      var id_ruangan = $('.ruangan-location').find(':selected').attr('data-id');

      var labelcat = $('.category-location').find(':selected').attr('data-id');
      var labelpartof = $('.partof-location').find(':selected').attr('data-id');

      $.ajax({
        url: '<?= base_url('IHS/postLocation') ?>',
        data: {name, description, category, labelcat, partof, labelpartof, ruangan, id_ruangan},
        method: 'POST',
        success: function(res){
          $("#resultPostLocation").html(res);
        }
      });
      event.preventDefault();
    });

    $('#formSearchLocation').submit(function(event) {
      form = $("#formSearchLocation").serializeArray();
      $.ajax({
        url: '<?= base_url('IHS/getLocation') ?>',
        method: 'POST',
        data: form,
        success: function(res){
          $("#resultSearchLocation").html(res);
        }
      });
      event.preventDefault();
    });
  });
</script>