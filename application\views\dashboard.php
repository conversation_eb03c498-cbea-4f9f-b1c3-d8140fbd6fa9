<?php
  require_once('layout/head.php');
?>

<style>
  table, th, td {
    border:1px solid black;
  }
</style>
<div class="container p-3 my-3 border">
<div class="row">
  <div class="offset-md-11 col-md">
    <a href="<?=base_url('Logout');?>">Keluar</a>
  </div>
</div>
  <ul class="nav nav-tabs">
    <!-- <li class="nav-item">
      <a class="nav-link" href="#mapping-loinc" data-toggle="tab">Mapping Loinc</a>
    </li> -->
    <li class="nav-item">
      <a class="nav-link mappingSnomed" href="#mappingSnomed" data-toggle="tab">Mapping Specimen ke SNOMED</a>
    </li>
    <li class="nav-item">
      <a class="nav-link mappingLoinc" href="#mappingLoinc" data-toggle="tab">Mapping Parameter ke LOINC</a>
    </li>
    <li class="nav-item">
      <a class="nav-link mappingTindakanLoinc" href="#mappingTindakanLoinc" data-toggle="tab">Mapping Tindakan/Buku tarif ke LOINC</a>
    </li>
    <li class="nav-item">
      <a class="nav-link mappingTindakanPa" href="#mappingTindakanPa" data-toggle="tab">Mapping Tindakan PA</a>
    </li>
   <!--  <li class="nav-item">
      <a class="nav-link" href="#patient" data-toggle="tab">Patient</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#organization" data-toggle="tab">Organization</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#practitioner" data-toggle="tab">Practitioner</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#location" data-toggle="tab">Location</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#bundle" data-toggle="tab">Bundle Encounter</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#search-data-encounter" data-toggle="tab">Search Data Encounter</a>
    </li> -->
    <!-- <li class="nav-item">
      <a class="nav-link" href="#buatEncounter" data-toggle="tab">Encounter</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatCondition" data-toggle="tab">Condition</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatProcedure" data-toggle="tab">Procedure</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatObservation" data-toggle="tab">Observation</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatComposition" data-toggle="tab">Composition</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatMedication1" data-toggle="tab">Medication Dokter (Request)</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#buatMedication2" data-toggle="tab">Medication Farmasi (Dispense)</a>
    </li>
    <li class="nav-item">
      <a class="nav-link" href="#serviceRequest" data-toggle="tab">Service Request</a>
    </li> -->
  </ul>

  <div class="tab-content">
    <div class="tab-pane" id="mapping-loinc">
      <?php //$this->load->view('mappingloinc/index', $parameterLab, $parameterLabDone, $parameterLabTanpa);?>
    </div>
    <div class="tab-pane" id="mappingSnomed">
      <div id="view_mappingSnomed"></div>
    </div>
    <div class="tab-pane" id="mappingLoinc">
      <div id="view_mappingLoinc"></div>
    </div>
    <div class="tab-pane" id="mappingTindakanLoinc">
      <div id="view_mappingTindakanLoinc"></div>
    </div>
    <div class="tab-pane" id="mappingTindakanPa">
      <div id="view_mappingTindakanPa"></div>
    </div>
    <div class="tab-pane" id="patient">
      <?php $this->load->view('patient/index');?>
    </div>
    <div class="tab-pane" id="organization">
      <p>Howdy, I'm in Section 2.</p>
    </div>
    <div class="tab-pane" id="practitioner">
      <?php $this->load->view('practitioner/index');?>
    </div>
    <div class="tab-pane" id="location">
      <?php $this->load->view('location/index');?>
    </div>
    <div class="tab-pane" id="encounter">
      <?php $this->load->view('encounter/index');?>
    </div>
    <div class="tab-pane" id="bundle">
      <?php $this->load->view('bundle/index');?>
    </div>
    <div class="tab-pane" id="bundle-condition">
      <?php $this->load->view('bundle/condition');?>
    </div>
    <div class="tab-pane" id="search-data-encounter">
      <?php $this->load->view('search/index');?>
    </div>
    <div class="tab-pane" id="buatEncounter">
      <?php $this->load->view('satuSehat/encounter/index');?>
    </div>
    <div class="tab-pane" id="buatCondition">
      <?php $this->load->view('satuSehat/condition/index');?>
    </div>
    <div class="tab-pane" id="buatProcedure">
      <?php $this->load->view('satuSehat/procedure/index');?>
    </div>
    <div class="tab-pane" id="buatObservation">
      <?php $this->load->view('satuSehat/observation/index');?>
    </div>
    <div class="tab-pane" id="buatComposition">
      <?php $this->load->view('satuSehat/composition/index');?>
    </div>
    <div class="tab-pane" id="buatUpdateEncounter">
      <?php $this->load->view('satuSehat/encounter/update');?>
    </div>
    <div class="tab-pane" id="buatMedication1">
      <?php $this->load->view('satuSehat/medicationRequest/index');?>
    </div>
    <div class="tab-pane" id="buatMedication2">
      <?php $this->load->view('satuSehat/medicationDispense/index');?>
    </div>
    <div class="tab-pane" id="serviceRequest">
      <?php $this->load->view('satuSehat/serviceRequest/index');?>
    </div>
  </div>

  

</div>

<script>
  $(document).ready(function(){
    $('.mappingSnomed').on('click', function() {
      $.ajax({
        url: "<?= base_url('IHS/mappingSnomed') ?>",
        method: "POST",
        success: function(data) {
          $('#view_mappingSnomed').html(data);
        }
      });
    });

    $('.mappingLoinc').on('click', function() {
      $.ajax({
        url: "<?= base_url('IHS/mappingLoinc') ?>",
        method: "POST",
        success: function(data) {
          $('#view_mappingLoinc').html(data);
        }
      });
    });

    $('.mappingTindakanLoinc').on('click', function() {
      $.ajax({
        url: "<?= base_url('IHS/mappingTindakanLoinc') ?>",
        method: "POST",
        success: function(data) {
          $('#view_mappingTindakanLoinc').html(data);
        }
      });
    });

    $('.mappingTindakanPa').on('click', function() {
      $.ajax({
        url: "<?= base_url('IHS/mappingTindakanPa') ?>",
        method: "POST",
        success: function(data) {
          $('#view_mappingTindakanPa').html(data);
        }
      });
    });
  });
</script>

<?php
  require_once('layout/footer.php');
?>