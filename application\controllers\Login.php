<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {
    public function __construct()
    {
        parent::__construct();
        $this->load->model('IHSModel');
        if($this->session->userdata('logged_in') == TRUE ){
            redirect($this->session->userdata('link'));
        }
        date_default_timezone_set("Asia/Bangkok");
    }

	public function index()
	{
		$this->load->view('login/index');
  }
    
    public function signin(){
        $result = $this->IHSModel->login();
    
        if(isset($result['data'])){
            $data = $result['data'];

            $session = array(
                'id'        => $data['id'],
                'username'  => $data['username'],
                'link'      => "IHS",
                'logged_in' => TRUE
            );
            $this->session->set_userdata($session);
        }
            echo json_encode($result);
    }
}
