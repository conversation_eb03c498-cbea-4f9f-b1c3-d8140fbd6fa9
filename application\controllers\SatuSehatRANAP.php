<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatRANAP extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("UTC");
    $this->load->model('IHSModel');
    $this->load->model('SatuSehatModel');
    $this->load->model('SatuSehatRANAPModel');
      //10000187 DEV
      //100025609 PROD
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ]; //PROD

    // $postDataArray = [
    //     'client_id' => 'fs2BJRXk1OgoRCIkZA6UPsAGdvv2lwPS0TI76WCXWxtyAHz7','client_secret' => '2VeU4CziJdKZxnfHLn7aw6kktOC4EAnVHKVlUIy3ZQetvjAyCfq6czKDjklfdNFk'
    // ]; //DEV

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials'; //Prod
    // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials'; //Dev

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
  }

  function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomString;
  }

  function getNakesIHS(){
    $data = $this->SatuSehatRANAPModel->getNakes()->result_array();
    foreach($data as $d){
        $token = $this->getToken();
        // $token = '';
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Practitioner?identifier=https://fhir.kemkes.go.id/id/nik|'.$d["KTP"].''; //Prod

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // var_dump($res);

        if($httpcode == "200"){
            $dataNakes = array(
            'ID_SIMPEL'   => $d['ID_SIMPEL'],
            'ID_IHS'      => $res->entry[0]->resource->id,
            'NIK'         => $d['KTP'],
            'NAMA'        => $res->entry[0]->resource->name[0]->text,
            'FLAG'        => 1,
            );
            $this->db->insert('ihs.tb_nakes', $dataNakes);      
        }
    }
  }

  function postRuanganRanap(){
    $ranap = $this->SatuSehatRANAPModel->dataKamarRanap();
    if($ranap->num_rows() > 0){
        foreach($ranap->result_array() as $r){
            //   $token = $this->getToken();
              $data = '
                    {
                        "resourceType": "Location",
                        "identifier": [
                            {
                                "system": "http://sys-ids.kemkes.go.id/organization/100025609",
                                "value": "Kamar '.$r['NAMA_KAMAR'].'"
                            }
                        ],
                        "status": "active",
                        "name": "Kamar '.$r['NAMA_KAMAR'].', Ruang '.$r['NAMA_RUANGAN'].'",
                        "mode": "instance",
                        "telecom": [
                            {
                                "system": "phone",
                                "value": "+6221-5681570",
                                "use": "work"
                            },
                            {
                                "system": "email",
                                "value": "<EMAIL>",
                                "use": "work"
                            },
                            {
                                "system": "url",
                                "value": "www.dharmais.com",
                                "use": "work"
                            }
                        ],
                        "type": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.kemkes.go.id/CodeSystem/location-type",
                                        "code": "RT0016",
                                        "display": "Ruang Rawat Inap"
                                    }
                                ]
                            }
                        ],
                        "physicalType": {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/location-physical-type",
                                    "code": "ro",
                                    "display": "Room"
                                }
                            ]
                        },
                        "position": {
                            "longitude": -6.23115426275766,
                            "latitude": 106.83239885393944,
                            "altitude": 0
                        },
                        "managingOrganization": {
                            "reference": "Organization/100025609"
                        },
                        "extension": [
                            {
                                "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/LocationServiceClass",
                                "valueCodeableConcept": {
                                    "coding": [
                                        {
                                            "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Inpatient",
                                            "code": "'.$r['ID_KELAS_IHS'].'",
                                            "display": "'.$r['NAMA_KELAS_IHS'].'"
                                        }
                                    ]
                                }
                            }
                        ],
                        "partOf": {
                            "reference": "Location/'.$r['IHS_PARTOF_RUANGAN'].'",
                            "display": "'.$r['NAMA_RUANGAN'].'"
                        }
                    }
                ';
                // echo $data;
                // $json = json_decode($data, true);
                // print_r($json);
                // echo '<pre>' . $data . '</pre>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location'; //DEV
            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Location'; //PROD
            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //     "Content-Type: application/json",
            //     "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            // if($httpcode == "201"){
            //     $simpanLocation = array(
            //         'id_ihs'         => $res->id,
            //         'id_ruangan'     => $r['ID_RUANGAN'],
            //         'id_kamar'       => $r['ID_RUANG_KAMAR'],
            //         'description'    => $r['NAMA_KAMAR'],
            //         'physicaltype'   => 'ro',
            //         'partof'         => $r['IHS_PARTOF_RUANGAN']
            //     );

            //     $this->db->insert('ihs.tb_location', $simpanLocation);  
            // //     // echo '<pre>'.print_r($simpanLocation)."</pre>";
            // }

            // $simpanLog = array(
            //     'id_ruangan'       => $r['ID_RUANG_KAMAR'],
            //     'id_ihs'           => $res->id,
            //     'log'              => $data,
            //     'response'         => $response,
            //     'http_code'        => $httpcode,
            //     'jenis'            => 'KAMAR RANAP'
            //   );
            //   $this->db->insert('ihs.tb_log_location', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    }   
  }

  function postBedRanap(){
    $ranap = $this->SatuSehatRANAPModel->dataBedRanap();
    if($ranap->num_rows() > 0){
        foreach($ranap->result_array() as $r){
            //   $token = $this->getToken();
              $data = '
                        {
                            "resourceType": "Location",
                            "identifier": [
                                {
                                    "system": "http://sys-ids.kemkes.go.id/organization/100025609",
                                    "value": "Bed '.$r['NAMA_BED'].'"
                                }
                            ],
                            "status": "active",
                            "name": "Bed '.$r['NAMA_BED'].', Kamar '.$r['NAMA_KAMAR'].', Ruang '.$r['NAMA_RUANGAN'].'",
                            "mode": "instance",
                            "telecom": [
                                {
                                    "system": "phone",
                                    "value": "+6221-5681570",
                                    "use": "work"
                                },
                                {
                                    "system": "email",
                                    "value": "<EMAIL>",
                                    "use": "work"
                                },
                                {
                                    "system": "url",
                                    "value": "www.dharmais.com",
                                    "use": "work"
                                }
                            ],
                            "type": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.kemkes.go.id/CodeSystem/location-type",
                                            "code": "RT0004",
                                            "display": "Tempat Tidur"
                                        }
                                    ]
                                }
                            ],
                            "physicalType": {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/location-physical-type",
                                        "code": "bd",
                                        "display": "Bed"
                                    }
                                ]
                            },
                            "operationalStatus" : 
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/v2-0116",
                                        "code": "U",
                                        "display": "Unoccupied"
                                    },
                            "position": {
                                "longitude": -6.23115426275766,
                                "latitude": 106.83239885393944,
                                "altitude": 0
                            },
                            "managingOrganization": {
                                "reference": "Organization/100025609"
                            },
                            "extension": [
                                {
                                    "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/LocationServiceClass",
                                    "valueCodeableConcept": {
                                        "coding": [
                                            {
                                                "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Inpatient",
                                                "code": "'.$r['ID_KELAS_IHS'].'",
                                                "display": "'.$r['NAMA_KELAS_IHS'].'"
                                            }
                                        ]
                                    }
                                }
                            ],
                            "partOf": {
                                "reference": "Location/'.$r['ID_PARTOF'].'",
                                "display": "Kamar '.$r['NAMA_KAMAR'].'"
                            }
                        }
             
                ';
                // echo $data;
                // $json = json_decode($data, true);
                // print_r($json);
                // echo '<pre>' . $data . '</pre>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location'; //DEV
            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Location'; //PROD
            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //     "Content-Type: application/json",
            //     "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            // if($httpcode == "201"){
            //     $simpanLocation = array(
            //         'id_ihs'         => $res->id,
            //         'id_ruangan'     => $r['ID_RUANGAN'],
            //         'id_kamar'       => $r['ID_KAMAR'],
            //         'id_bed'         => $r['ID_BED'],
            //         'description'    => 'Bed '.$r['NAMA_BED'],
            //         'physicaltype'   => 'bd',
            //         'partof'         => $r['ID_PARTOF']
            //     );

            //     $this->db->insert('ihs.tb_location', $simpanLocation);  
            // // //     // echo '<pre>'.print_r($simpanLocation)."</pre>";
            // }

            // $simpanLog = array(
            //     'id_ruangan'       => $r['ID_BED'],
            //     'id_ihs'           => $res->id,
            //     'log'              => $data,
            //     'response'         => $response,
            //     'http_code'        => $httpcode,
            //     'jenis'            => 'BED RANAP'
            //   );
            //   $this->db->insert('ihs.tb_log_location', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    }   
  }

  function postServiceRequest(){
    $service = $this->SatuSehatRANAPModel->sendServiceRequest();
    if($service->num_rows() > 0){
        foreach($service->result_array() as $s){
          $token = $this->getToken();
            // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
            $dataReg = date_create_from_format('Y-m-d H:i:s', $s['TGL_BUAT']);
            $tglReg = $dataReg->format(DATE_ATOM);
            $data = '
                {
                    "resourceType": "ServiceRequest",
                    "identifier": [
                        {
                            "system": "http://sys-ids.kemkes.go.id/servicerequest/100025609",
                            "value": "'.$s['NOPEN'].'"
                        }
                    ],
                    "status": "active",
                    "intent": "order",
                    "priority": "routine",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://snomed.info/sct",
                                    "code": "3457005",
                                    "display": "Patient referral"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                    "system": "http://snomed.info/sct",
                                    "code": "737481003",
                                    "display": "Inpatient care management"
                            }
                        ],
                        "text": "Rawat inap pasca '.$s['JENIS_MASUK_RANAP'].'"
                    },
                    "subject": {
                        "reference": "Patient/'.$s['ID_IHS_PASIEN'].'",
                        "display": "'.$s['NAMA_PASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$s['ENCOUNTER_SEBELUM'].'"
                    },
                    "occurrenceDateTime": "'.$tglReg.'",
                    "requester": {
                        "reference": "Practitioner/'.$s['ID_IHS_DOKTER'].'",
                        "display": "'.$s['NAMA_DOKTER'].'"
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$s['ID_IHS_PERAWAT'].'",
                            "display": "'.$s['NAMA_PERAWAT'].'"
                        }
                    ],
                    "reasonCode": [
                        {
                            "coding": [
                                {
                                    "system": "http://hl7.org/fhir/sid/icd-10",
                                    "code": "'.$s['KODE_ICD10'].'",
                                    "display": "'.$s['NAMA_ICD10'].'"
                                }
                            ],
                            "text": "Pasien mengalami '.$s['NAMA_ICD10'].'"
                        }
                    ],
                    "patientInstruction": "Pasien mendapatkan perawatan rawat inap setelah dari '.$s['JENIS_MASUK_RANAP'].'"
                }';
                // echo '<pre>' . $data . '</pre><br>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //DEV
            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //PROD
            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);

            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            if($httpcode == "201"){
            $simpanServiceRequest = array(
                'nopen'                => $s['NOPEN'],
                'encounter_sebelum'    => $s['ENCOUNTER_SEBELUM'],
                'id_ihs'               => $res->id,
                'status'               => 1,
                'jenis'                => 'MASUK RANAP'
              );

              $this->db->insert('ihs.tb_service_request_ranap', $simpanServiceRequest);  
                // echo '<pre>'.print_r($simpanProcedure)."</pre>";
            }

            date_default_timezone_set('Asia/Jakarta');
            $dataSerReqRANAP = array (
                'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
                'HTTPCODE'                     => $httpcode,
                'CREATED_AT'                   => date('Y-m-d H:i:s'),
                'STATUS'                       => ($httpcode == "201") ? '2' : '1',
            );
    
            $this->db->where('NOPEN', $s['NOPEN']);
            $this->db->update('data_ihs.data_service_request_ranap', $dataSerReqRANAP);

            $simpanLog = array(
                'nopen'                => $s['NOPEN'],
                'id_encounter'         => $s['ENCOUNTER_SEBELUM'],
                'id_ihs'               => $res->id,
                'log'                  => $data,
                'response'             => $response,
                'http_code'            => $httpcode,
                'jenis'                => 'MASUK RANAP'
              );
              $this->db->insert('ihs.tb_log_service_request_ranap', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    }else{
        // '<pre>'.print_r('Data Kosong')."</pre>";
    }   
  }

  function postEncounter(){
    $encounter = $this->SatuSehatRANAPModel->sendEncounter();
    if($encounter->num_rows() > 0){
        foreach($encounter->result_array() as $en){
          $token = $this->getToken();
            // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
              $date = date_create_from_format('Y-m-d H:i:s', $en['TGL_MASUK']);
              $tgl = $date->format(DATE_ATOM);
            //   $eoc = $this->guidv4($en['NOPEN']);
              $data = '
                        {
                            "resourceType": "Encounter",
                            "identifier": [
                                {
                                    "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
                                    "value": "'.$en['NOPEN'].'"
                                }
                            ],
                            "status": "in-progress",
                            "class": {
                                "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                                "code": "IMP",
                                "display": "inpatient encounter"
                            },
                            "subject": {
                                "reference": "Patient/'.$en['ID_IHS_PASIEN'].'",
                                "display": "'.$en['NAMA_PASIEN'].'"
                            },
                            "participant": [
                                {
                                    "type": [
                                        {
                                            "coding": [
                                                {
                                                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                                    "code": "ATND",
                                                    "display": "attender"
                                                }
                                            ]
                                        }
                                    ],
                                    "individual": {
                                        "reference": "Practitioner/'.$en['ID_IHS_DOKTER'].'",
                                        "display": "'.$en['NAMA_DOKTER'].'"
                                    }
                                }
                            ],
                            "period": {
                                "start": "'.$tgl.'"
                            },
                            "location": [
                                {
                                    "location": {
                                        "reference": "Location/'.$en['ID_IHS_BED'].'",
                                        "display": "'.$en['NAMA_BED'].', Kamar '.$en['NAMA_KAMAR'].'"
                                    },
                                    "extension": [
                                        {
                                            "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/ServiceClass",
                                            "extension": [
                                                {
                                                    "url": "value",
                                                    "valueCodeableConcept": {
                                                        "coding": [
                                                            {
                                                                "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Inpatient",
                                                                "code": "'.$en['ID_IHS_KELAS'].'",
                                                                "display": "'.$en['NAMA_KELAS'].'"
                                                            }
                                                        ]
                                                    }
                                                },
                                                {
                                                    "url": "upgradeClassIndicator",
                                                    "valueCodeableConcept": {
                                                        "coding": [
                                                            {
                                                                "system": "http://terminology.kemkes.go.id/CodeSystem/locationUpgradeClass",
                                                                "code": "kelas-tetap",
                                                                "display": "Kelas Tetap Perawatan"
                                                            }
                                                        ]
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            "statusHistory": [
                                {
                                    "status": "in-progress",
                                    "period": {
                                        "start": "'.$tgl.'"
                                    }
                                }
                            ],
                            "serviceProvider": {
                                "reference": "Organization/100025609"
                            },
                            "basedOn": [
                                {
                                    "reference": "ServiceRequest/'.$en['SERVICE_REQUEST'].'"
                                }
                            ]
                        }';
                // echo '<pre>' . $data . '</pre><br>';
            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter'; //Prod
            // $url = 'https://api-satusehat-stg.dto.kemkes.go.id/fhir-r4/v1/Encounter'; //Staging
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter'; //Dev
            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            if($httpcode == "201"){
                $simpanEncounter = array(
                  'id_ihs_pasien' => $en['ID_IHS_PASIEN'],
                  'id_ihs_practitioner' => $en['ID_IHS_DOKTER'],
                  'encounter' => $res->id,
                  'nopen' => $en['NOPEN'],
                  'jenis' => 3
                );

                $this->db->insert('ihs.tb_encounter', $simpanEncounter);   
                // echo '<pre>'.print_r($simpanEncounter)."</pre>";
            }

            date_default_timezone_set('Asia/Jakarta');
            $dataEncRANAP = array (
                'ENCOUNTER'                    => ($httpcode == "201") ? $res->id : NULL,
                'HTTPCODE'                     => $httpcode,
                'CREATED_AT'                   => date('Y-m-d H:i:s'),
                'STATUS'                       => ($httpcode == "201") ? '2' : '1',
            );

            $this->db->where('NOPEN', $en['NOPEN']);
            $this->db->update('data_ihs.data_encounter_ranap', $dataEncRANAP);

                $simpanLogEncounter = array(
                    'nopen'     => $en['NOPEN'],
                    'log'       => $data,
                    'httpcode'       => $httpcode,
                    'id_encounter' => $res->id,
                    'response'       => $response,
                    'jenis'       => 'POST',
                );
                $this->db->insert('ihs.tb_log_encounter_ranap', $simpanLogEncounter);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    }
  }

  function postTingkatKesadaranRANAP(){
    $kesadaran = $this->SatuSehatRANAPModel->sendTingkatKesadaran();
    if($kesadaran->num_rows() > 0){
        foreach($kesadaran->result_array() as $tk){
        //   $token = $this->getToken();
            // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
              $date = date_create_from_format('Y-m-d H:i:s', $tk['CREATED_AT']);
              $tgl = $date->format(DATE_ATOM);
              $data = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "exam",
                                        "display": "Exam"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "67775-7",
                                    "display": "Level of responsiveness"
                                }
                            ]
                        },
                        "subject": {
                            "reference": "Patient/'.$tk['ID_IHS_PASIEN'].'",
                            "display": "'.$tk['NAMAPASIEN'].'"
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$tk['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "encounter": {
                            "reference": "Encounter/'.$tk['ID_ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tgl.'",
                        "issued": "'.$tgl.'",
                        "valueCodeableConcept": {
                            "coding": [
                                {
                                    "system": "http://snomed.info/sct",
                                    "code": "'.$tk['KESADARAN'].'",
                                    "display": "'.$tk['DESKRIPSI_KESADARAN'].'"
                                }
                            ]
                        }
                    }';
                // echo '<pre>' . $data . '</pre><br>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //     "Content-Type: application/json",
            //     "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            // if($httpcode == "201"){
            // $simpanTingkatKesadaran = array(
            //     'observation'        => $res->id,
            //     'nopen'              => $tk['NOPEN'],
            //     'id_encounter'       => $tk['ID_ENCOUNTER'],
            //     'kategori'           => 'TINGKAT KESADARAN',
            //   );

            //   $this->db->insert('ihs.tb_observation_ranap', $simpanTingkatKesadaran);  
            //     // echo '<pre>'.print_r($simpanTingkatKesadaran)."</pre>";
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $tk['ID_ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'TINGKAT KESADARAN',
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    } 
  }

  public function postVitalSignRANAP()
  {
    $vitalsign = $this->SatuSehatRANAPModel->sendVitalSign();
    if($vitalsign->num_rows() > 0){
        foreach($vitalsign->result_array() as $vs){
          $date = date_create_from_format('Y-m-d H:i:s', $vs['CREATEDAT']);
          $tgl = $date->format(DATE_ATOM);
            //   $token = $this->getToken();
            // START NADI
            if(isset($vs['NADI'])) {
            $datanadi = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "vital-signs",
                                        "display": "Vital Signs"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "8867-4",
                                    "display": "Heart rate"
                                }
                            ]
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$vs['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "subject": {
                            "reference": "Patient/'.$vs['ID_IHS_PASIEN'].'",
                            "display": "'.$vs['NAMA_PASIEN'].'"
                        },
                        "encounter": {
                            "reference": "Encounter/'.$vs['ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tgl.'",
                        "issued": "'.$tgl.'",
                        "valueQuantity": {
                            "value": '.$vs['NADI'].',
                            "unit": "beats/minute",
                            "system": "http://unitsofmeasure.org",
                            "code": "/min"
                        }
                    }';
                //  echo '<pre>' . $datanadi . '</pre><br>';

            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //   "Content-Type: application/json",
            //   "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datanadi);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);

            // if($httpcode == "201"){
            //   echo 'Status Code : ' . $httpcode . '<br>';
            //   echo '<pre>'.$response."</pre>";

            //  $simpanProcedure = array(
            //     'observation'   => $res->id,
            //     'nopen'              => $vs['NOPEN'],
            //     'id_encounter'       => $vs['ENCOUNTER'],
            //     'kategori'           => 'VITAL SIGN',
            //     'tipe'               => 'NADI'
            //     );
            //     $this->db->insert('ihs.tb_observation_ranap', $simpanProcedure);      
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $vs['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'VITAL SIGN',
            //     'tipe'           => 'NADI'
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);
        }
            // END NADI

            // START PERNAPASAN
            if(isset($vs['PERNAPASAN'])) {
            // $token = $this->getToken();
            $datapernapasan = '{
                            "resourceType": "Observation",
                            "status": "final",
                            "category": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                            "code": "vital-signs",
                                            "display": "Vital Signs"
                                        }
                                    ]
                                }
                            ],
                            "code": {
                                "coding": [
                                    {
                                        "system": "http://loinc.org",
                                        "code": "9279-1",
                                        "display": "Respiratory rate"
                                    }
                                ]
                            },
                            "subject": {
                                "reference": "Patient/'.$vs['ID_IHS_PASIEN'].'",
                                "display": "'.$vs['NAMA_PASIEN'].'"
                            },
                            "performer": [
                                {
                                    "reference": "Practitioner/'.$vs['ID_IHS_DOKTER'].'"
                                }
                            ],
                            "encounter": {
                                "reference": "Encounter'.$vs['ENCOUNTER'].'"
                            },
                            "effectiveDateTime": "'.$tgl.'",
                            "issued": "'.$tgl.'",
                            "valueQuantity": {
                                "value": '.$vs['PERNAPASAN'].',
                                "unit": "breaths/minute",
                                "system": "http://unitsofmeasure.org",
                                "code": "/min"
                            }
                        }';

            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //   "Content-Type: application/json",
            //   "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datapernapasan);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);

            // if($httpcode == "201"){
            // //   echo 'Status Code : ' . $httpcode . '<br>';
            // //   echo '<pre>'.$response."</pre>";

            //  $simpanProcedure = array(
            //     'observation'   => $res->id,
            //     'nopen'              => $vs['NOPEN'],
            //     'id_encounter'       => $vs['ENCOUNTER'],
            //     'kategori'           => 'VITAL SIGN',
            //     'tipe'               => 'PERNAPASAN'
            //     );
            //     $this->db->insert('ihs.tb_observation_ranap', $simpanProcedure);      
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $vs['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'VITAL SIGN',
            //     'tipe'           => 'PERNAPASAN'
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);
        } 
            // END PERNAPASAN 

        // START TEKANAN DARAH (Sistolik)
        if(isset($vs['SISTOLE'])) {
            // $token = $this->getToken();
            $datasistole = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "vital-signs",
                                        "display": "Vital Signs"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "8480-6",
                                    "display": "Systolic blood pressure"
                                }
                            ]
                        },
                        "subject": {
                            "reference": "Patient/'.$vs['ID_IHS_PASIEN'].'",
                            "display": "'.$vs['NAMA_PASIEN'].'"
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$vs['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "encounter": {
                            "reference": "Encounter/'.$vs['ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tgl.'",
                        "issued": "'.$tgl.'",
                        "valueQuantity": {
                            "value": '.$vs['SISTOLE'].',
                            "unit": "mm[Hg]",
                            "system": "http://unitsofmeasure.org",
                            "code": "mm[Hg]"
                        }
                    }';

            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //   "Content-Type: application/json",
            //   "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasistole);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);

            // if($httpcode == "201"){
            // //   echo 'Status Code : ' . $httpcode . '<br>';
            // //   echo '<pre>'.$response."</pre>";

            //  $simpanProcedure = array(
            //     'observation'   => $res->id,
            //     'nopen'              => $vs['NOPEN'],
            //     'id_encounter'       => $vs['ENCOUNTER'],
            //     'kategori'           => 'VITAL SIGN',
            //     'tipe'               => 'SISTOLIK'
            //     );
            //     $this->db->insert('ihs.tb_observation_ranap', $simpanProcedure);     
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $vs['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'VITAL SIGN',
            //     'tipe'           => 'SISTOLIK'
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);

        }
        // END TEKANAN DARAH (Sistolik)

        // START TEKANAN DARAH (Diastolik)
        if(isset($vs['DIASTOLE'])) {
            // $token = $this->getToken();
            $datadiastol = '{
                            "resourceType": "Observation",
                            "status": "final",
                            "category": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                            "code": "vital-signs",
                                            "display": "Vital Signs"
                                        }
                                    ]
                                }
                            ],
                            "code": {
                                "coding": [
                                    {
                                        "system": "http://loinc.org",
                                        "code": "8462-4",
                                        "display": "Diastolic blood pressure"
                                    }
                                ]
                            },
                            "subject": {
                                "reference": "Patient/'.$vs['ID_IHS_PASIEN'].'",
                                "display": "'.$vs['NAMA_PASIEN'].'"
                            },
                            "performer": [
                                {
                                    "reference": "Practitioner/'.$vs['ID_IHS_DOKTER'].'"
                                }
                            ],
                            "encounter": {
                                "reference": "Encounter/'.$vs['ENCOUNTER'].'"
                            },
                            "effectiveDateTime": "'.$tgl.'",
                            "issued": "'.$tgl.'",
                            "valueQuantity": {
                                "value": '.$vs['DIASTOLE'].',
                                "unit": "mm[Hg]",
                                "system": "http://unitsofmeasure.org",
                                "code": "mm[Hg]"
                            }
                        }';

            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //   "Content-Type: application/json",
            //   "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datadiastol);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);

            // if($httpcode == "201"){
            // //   echo 'Status Code : ' . $httpcode . '<br>';
            // //   echo '<pre>'.$response."</pre>";

            //  $simpanProcedure = array(
            //     'observation'   => $res->id,
            //     'nopen'              => $vs['NOPEN'],
            //     'id_encounter'       => $vs['ENCOUNTER'],
            //     'kategori'           => 'VITAL SIGN',
            //     'tipe'               => 'DIASTOLIK'
            //     );
            //     $this->db->insert('ihs.tb_observation_ranap', $simpanProcedure);    
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $vs['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'VITAL SIGN',
            //     'tipe'           => 'DIASTOLIK'
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);
        }
        // END TEKANAN DARAH (Diastolik)

        // START SUHU
        if(isset($vs['SUHU'])) {
            // $token = $this->getToken();
            $datasuhu = '{
                            "resourceType": "Observation",
                            "status": "final",
                            "category": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                            "code": "vital-signs",
                                            "display": "Vital Signs"
                                        }
                                    ]
                                }
                            ],
                            "code": {
                                "coding": [
                                    {
                                        "system": "http://loinc.org",
                                        "code": "8310-5",
                                        "display": "Body temperature"
                                    }
                                ]
                            },
                            "subject": {
                                "reference": "Patient/'.$vs['ID_IHS_PASIEN'].'",
                                "display": "'.$vs['NAMA_PASIEN'].'"
                            },
                            "performer": [
                                {
                                    "reference": "Practitioner/'.$vs['ID_IHS_DOKTER'].'"
                                }
                            ],
                            "encounter": {
                                "reference": "Encounter/'.$vs['ENCOUNTER'].'"
                            },
                            "effectiveDateTime": "'.$tgl.'",
                            "issued": "'.$tgl.'",
                            "valueQuantity": {
                                "value": '.$vs['SUHU'].',
                                "unit": "C",
                                "system": "http://unitsofmeasure.org",
                                "code": "Cel"
                            }
                        }';

            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //   "Content-Type: application/json",
            //   "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasuhu);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);

            // if($httpcode == "201"){
            // //   echo 'Status Code : ' . $httpcode . '<br>';
            // //   echo '<pre>'.$response."</pre>";

            //  $simpanProcedure = array(
            //     'observation'   => $res->id,
            //     'nopen'              => $vs['NOPEN'],
            //     'id_encounter'       => $vs['ENCOUNTER'],
            //     'kategori'           => 'VITAL SIGN',
            //     'tipe'               => 'SUHU'
            //     );
            //     $this->db->insert('ihs.tb_observation_ranap', $simpanProcedure);    
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $vs['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'VITAL SIGN',
            //     'tipe'           => 'SUHU'
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);

        }
      }
    }else{
        // END SUHU
        // $simpanKosong = array(
        //   'nopen' => $cekObservation['NOPEN'],
        //   'observation' => 'tidak ada data - ' . $cekObservation['NOPEN'],
        //   'id_encounter' => $cekObservation['IDENCOUNTER'],
        //   'kategori' => "tidak ada data",
        //   'status' => "0",
        // );
        // $this->db->insert('ihs.tb_observation', $simpanKosong);
    }
  }

  function postStatusPsikologisRANAP(){
    $psikologis = $this->SatuSehatRANAPModel->sendStatusPsikologis();
    if($psikologis->num_rows() > 0){
        foreach($psikologis->result_array() as $ps){
        //   $token = $this->getToken();
            // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
              $date = date_create_from_format('Y-m-d H:i:s', $ps['TGL_MASUK']);
              $tgl = $date->format(DATE_ATOM);
              $data = '{
                            "resourceType": "Observation",
                            "status": "final",
                            "category": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                            "code": "survey",
                                            "display": "Survey"
                                        }
                                    ]
                                }
                            ],
                            "code": {
                                "coding": [
                                    {
                                        "system": "http://loinc.org",
                                        "code": "8693-4",
                                        "display": "Mental Status"
                                    }
                                ]
                            },
                            "subject": {
                                "reference": "Patient/'.$ps['ID_IHS_PASIEN'].'"
                            },
                            "performer": [
                                {
                                    "reference": "Practitioner/'.$ps['ID_IHS_PRACTITIONER'].'"
                                }
                            ],
                            "encounter": {
                                "reference": "Encounter/'.$ps['ENCOUNTER'].'"
                            },
                            "effectiveDateTime": "'.$tgl.'",
                            "issued": "'.$tgl.'",
                            "valueCodeableConcept": {
                                "coding": [
                                    {
                                        "system": "http://snomed.info/sct",
                                        "code": "'.$ps['KODE_SNOMED'].'",
                                        "display": "'.$ps['NAMA_SNOMED'].'"
                                    }
                                ]
                            }
                        }';
                // echo '<pre>' . $data . '</pre><br>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
            // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            // $cURL = curl_init();
            // curl_setopt($cURL, CURLOPT_URL,$url);
            // curl_setopt($cURL, CURLOPT_HEADER,false);
            // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            //     "Content-Type: application/json",
            //     "Authorization: Bearer ".$token." "
            // ));
            // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            // $response = curl_exec($cURL);
            // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            // curl_close($cURL);
            // $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            // if($httpcode == "201"){
            // $simpanStatusPsikologis = array(
            //     'observation'        => $res->id,
            //     'nopen'              => $ps['NOPEN'],
            //     'id_encounter'       => $ps['ENCOUNTER'],
            //     'kategori'           => 'STATUS PSIKOLOGIS',
            //   );

            //   $this->db->insert('ihs.tb_observation_ranap', $simpanStatusPsikologis);  
            //     // echo '<pre>'.print_r($simpanStatusPsikologis)."</pre>";
            // }

            // $simpanLog = array(
            //     'id_encounter'   => $ps['ENCOUNTER'],
            //     'id_observation' => $res->id,
            //     'log'            => $data,
            //     'response'       => $response,
            //     'http_code'      => $httpcode,
            //     'kategori'       => 'STATUS PSIKOLOGIS',
            // );
            // $this->db->insert('ihs.tb_log_observation_ranap', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
    } 
  }
//----------------------------------------------------------------------FASE 2--------------------------------------------------------------------------------------------------------
  function postCarepPlanRANAP(){
    $careplan = $this->SatuSehatRANAPModel->sendCarePlan();
    if($careplan->num_rows() > 0){
        foreach($careplan->result_array() as $cp){
          $token = $this->getToken();
            // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
              $date = date_create_from_format('Y-m-d H:i:s', $cp['TGL_CAREPLAN']);
              $tgl = $date->format(DATE_ATOM);
              $data = '
                        {
                            "resourceType": "CarePlan",
                            "title": "Rencana Rawat Pasien",
                            "status": "active",
                            "category": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://snomed.info/sct",
                                            "code": "736353004",
                                            "display": " Inpatient care plan"
                                        }
                                    ]
                                }
                            ],
                            "intent": "plan",
                            "description": "'.trim(preg_replace('/\s+/', ' ', $cp['POC'])).'",
                            "subject": {
                                "reference": "Patient/'.$cp['ID_IHS_PASIEN'].'",
                                "display": "'.$cp['NAMA_PASIEN'].'"
                            },
                            "encounter": {
                                "reference": "Encounter/'.$cp['ENCOUNTER'].'"
                            },
                            "created": "'.$tgl.'",
                            "author": {
                                "reference": "Practitioner/'.$cp['ID_IHS_PENGISI'].'"
                            }
                        }';
                // echo '<pre>' . $data . '</pre><br>';
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/CarePlan'; //DEV
            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/CarePlan'; //PROD
            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            if($httpcode == "201"){
                $simpanCarePlan = array(
                    'id_careplan'         => $res->id,
                    'nopen'               => $cp['NOPEN'],
                    'id_encounter'        => $cp['ENCOUNTER'],
                    'id_ihs_pasien'       => $cp['ID_IHS_PASIEN'],
                    'jenis'               => 'MASUK RANAP'
                );

                $this->db->insert('ihs.tb_careplan_ranap', $simpanCarePlan);  
                // echo '<pre>'.print_r($simpanCarePlan)."</pre>";
            }

            date_default_timezone_set('Asia/Jakarta');
            $dataCarePlanRANAP = array (
                'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
                'HTTPCODE'                     => $httpcode,
                'CREATED_AT'                   => date('Y-m-d H:i:s'),
                'STATUS'                       => ($httpcode == "201") ? '2' : '1',
            );

            $this->db->where('NOPEN', $cp['NOPEN']);
            $this->db->update('data_ihs.data_careplan_ranap', $dataCarePlanRANAP);

            $simpanLogCareplan = array(
                'id_encounter'         => $cp['ENCOUNTER'],
                'id_careplan'          => $res->id,
                'log'                  => $data,
                'response'             => $response,
                'http_code'            => $httpcode,
                'jenis'                => 'MASUK RANAP'
            );
            $this->db->insert('ihs.tb_log_careplan_ranap', $simpanLogCareplan);
            // echo '<pre>'.print_r($simpanLogCareplan)."</pre>";
                // }
        }
    } 
  }

}
