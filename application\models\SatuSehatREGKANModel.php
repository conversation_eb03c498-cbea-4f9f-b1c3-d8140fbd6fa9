<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatREGKANModel extends CI_Model {

       public function listPasienRegKan()
       {
              $query="SELECT *
              FROM db_regkan.tb_pasienbaru pas
              WHERE pas.JENIS_SIAP_KIRIM=1
              -- AND pas.NORM!=298064
              -- AND pas.NORM!=298231
              -- AND pas.NORM!=298378
              -- AND pas.NORM!=298398
              -- AND pas.NORM!=298499
              -- AND pas.NORM!=298516
              -- AND pas.NORM!=298807
              LIMIT 500";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       public function listPasienRegKan_2022()
       {
              $query="SELECT *
              FROM db_regkan.tb_modul_regkan fr
              WHERE fr.jenis_kirim=1 AND fr.mr!=0 
              -- AND fr.mr!=293129
              -- AND fr.mr!=293528
              -- AND fr.mr!=293582
              -- AND fr.mr!=293612
              LIMIT 500";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       // public function listPasienRegKan_2022_composition()
       // {
       //        $query="SELECT *
       //        FROM db_regkan.tb_pasienbaru pas
       //        #WHERE pas.JENIS_SIAP_KIRIM=2 
       //        -- LIMIT 500
       //        -- LIMIT 500, 500
       //        -- LIMIT 1000, 500
       //        -- LIMIT 1500, 500
       //        -- LIMIT 2000, 500
       //        -- LIMIT 2500, 500
       //        -- LIMIT 3000, 500
       //        -- LIMIT 3500, 500
       //        -- LIMIT 4000, 500
       //        -- LIMIT 4500, 500
       //        -- LIMIT 5000, 500
       //        -- LIMIT 5500, 1000
       //        -- LIMIT 6500, 1000
       //        -- LIMIT 7500, 1000
       //        -- LIMIT 8500, 1000
       //        -- LIMIT 9500, 1500
       //        -- LIMIT 11000, 2000
       //        -- LIMIT 13000, 2000
       //        -- LIMIT 15000, 2000
       //        LIMIT 19000, 2000
       //        ";

       //        $bind = $this->db->query($query);
       //        return $bind->result_array();
       // }

       // public function composition($mr)
       // {
       //        $query="SELECT pen.NOMOR NOPEN, enc.encounter ID_ENCOUNTER, enc.id_ihs_pasien ID_IHS_PASIEN
       //        , master.getNamaLengkap(pen.NORM) NAMA_PASIEN, enc.id_ihs_practitioner ID_PRACTITIONER
       //        , master.getNamaLengkapPegawai (doks.NIP) DPJP, con1.id_condition ID_CONDITION1, con2.id_condition ID_CONDITION2, con3.id_condition ID_CONDITION3, con4.id_condition ID_CONDITION4, con5.id_condition ID_CONDITION5, pro.id_procedure ID_PROCEDURE, eoc.id_episodeofcare ID_EPISODEOFCARE
       //        , rad.ID_IHS ID_OBSERVATION_RAD, pen.TANGGAL TANGGAL_JAM
       //        FROM pendaftaran.pendaftaran pen
       //        LEFT JOIN ihs.tb_encounter enc ON enc.nopen = pen.NOMOR
       //        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pen.NOMOR
       //        LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
       //        LEFT JOIN db_regkan.tb_condition con1 ON con1.id_encounter = enc.encounter AND con1.jenis_condition=1
       //        LEFT JOIN db_regkan.tb_condition con2 ON con2.id_encounter = enc.encounter AND con2.jenis_condition=2
       //        LEFT JOIN db_regkan.tb_condition con3 ON con3.id_encounter = enc.encounter AND con3.jenis_condition=3
       //        LEFT JOIN db_regkan.tb_condition con4 ON con4.id_encounter = enc.encounter AND con4.jenis_condition=4
       //        LEFT JOIN db_regkan.tb_condition con5 ON con5.id_encounter = enc.encounter AND con5.jenis_condition=5
       //        LEFT JOIN db_regkan.tb_procedure pro ON pro.id_encounter = enc.encounter
       //        LEFT JOIN db_regkan.tb_episodeofcare eoc ON eoc.nopen=pen.NOMOR
       //        LEFT JOIN data_ihs.data_observation_radiologi rad ON rad.ENCOUNTER = enc.encounter
       //        WHERE pen.NORM=?
       //        AND enc.encounter IS NOT NULL";

       //        $bind = $this->db->query($query, array($mr));
       //        return $bind;
       // }

       public function listComposition()
       {
              $query="
              SELECT *
              FROM db_regkan.tb_composition com
              WHERE com.jenis_kirim=1
              LIMIT 1";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       public function updateComposition()
       {
              $query="
              SELECT MIN(com.id_composition) AS id
              FROM db_regkan.tb_composition com
              JOIN (
                 SELECT encounter, tgl_jam
                 FROM db_regkan.tb_composition
                 WHERE status = 1
                 GROUP BY encounter, tgl_jam
                 HAVING COUNT(*) > 1
          ) AS duplicated_compositions
              ON com.encounter = duplicated_compositions.encounter
              AND com.tgl_jam = duplicated_compositions.tgl_jam
              WHERE com.status = 1
              GROUP BY com.encounter, com.tgl_jam
              LIMIT 5000";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       public function listCondition()
       {
              $query="SELECT con.*, varper.URL system_perilaku, vargra.URL system_grade
              , varsta.URL system_stadium, varlua.URL system_perluasan
              , varlat.URL system_lateralitas, varmt1.URL system_metas1
              , varmt2.URL system_metas2, varmt3.URL system_metas3
              , varmt4.URL system_metas4, varttt.URL system_tnm_t
              , varnnn.URL system_tnm_n, varmmm.URL system_tnm_m
              , varbsd.URL system_basis_diagnosis
              FROM db_regkan.tb_condition con
              LEFT JOIN db_regkan.tb_variabel varper ON varper.KODE = con.kode_perilaku_tumor
              LEFT JOIN db_regkan.tb_variabel vargra ON vargra.KODE = con.kode_grade_tumor
              LEFT JOIN db_regkan.tb_variabel varsta ON varsta.KODE = con.kode_stadium
              LEFT JOIN db_regkan.tb_variabel varlua ON varlua.KODE = con.kode_perluasan_tumor
              LEFT JOIN db_regkan.tb_variabel varlat ON varlat.KODE = con.kode_lateralitas_tumor
              LEFT JOIN db_regkan.tb_variabel varmt1 ON varmt1.KODE = con.kode_metastasis_jauh_1
              LEFT JOIN db_regkan.tb_variabel varmt2 ON varmt2.KODE = con.kode_metastasis_jauh_2
              LEFT JOIN db_regkan.tb_variabel varmt3 ON varmt3.KODE = con.kode_metastasis_jauh_3
              LEFT JOIN db_regkan.tb_variabel varmt4 ON varmt4.KODE = con.kode_metastasis_jauh_4
              LEFT JOIN db_regkan.tb_variabel varttt ON varttt.KODE = con.tnm_t
              LEFT JOIN db_regkan.tb_variabel varnnn ON varnnn.KODE = con.tnm_n
              LEFT JOIN db_regkan.tb_variabel varmmm ON varmmm.KODE = con.tnm_m
              LEFT JOIN db_regkan.tb_variabel varbsd ON varbsd.KODE = con.kode_basis_diagnosis
              WHERE con.jenis_pengiriman=1
              ORDER BY con.nm_pasien, con.jenis_condition ASC
              LIMIT 100";

              $bind = $this->db->query($query);
              return $bind->result_array();
       } 

       public function listProcedure()
       {
              $query="SELECT pro.*, vartp1.URL system_terapi_1, vartp2.URL system_terapi_2
              , vartp3.URL system_terapi_3
              FROM db_regkan.tb_procedure pro
              LEFT JOIN db_regkan.tb_variabel vartp1 ON vartp1.KODE = pro.kode_terapi_1
              LEFT JOIN db_regkan.tb_variabel vartp2 ON vartp2.KODE = pro.kode_terapi_2
              LEFT JOIN db_regkan.tb_variabel vartp3 ON vartp3.KODE = pro.kode_terapi_3
              WHERE pro.jenis_pengiriman=1 AND pro.id_ihs_practitioner IS NOT NULL
              ORDER BY pro.nm_pasien ASC
              LIMIT 100";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       public function listPatchEoc($jns)
       {
              $query="SELECT pe.nopen NOPEN, pe.id_encounter ID_ENCOUNTER, pe.id_episode_of_care ID_EOC, pe.id_ihs_pasien ID_IHS_PASIEN, pe.jenis JENIS_KIRIM, pe.jenis_encounter JENIS_KUNJUNGAN, enc.jenis JENIS_JALNAP
              FROM db_regkan.tb_patch_eoc pe
              LEFT JOIN ihs.tb_encounter enc ON enc.encounter = pe.id_encounter
              WHERE pe.jenis=1";

              if($jns == 1){
                     $query .=" AND enc.jenis IN (1,2)";
              }elseif($jns == 2){
                     $query .=" AND enc.jenis IN (3)";
              }

              $query.="LIMIT 100";

              $bind = $this->db->query($query);
              return $bind->result_array();
       }

       public function listEpsKun($jnsKirim)
       {
              $query="
              SELECT *
              FROM db_regkan.tb_episodeofcare eps
              WHERE eps.jenis_pengiriman=? ";

              if($jnsKirim == 1){
                     $query .="AND eps.id_surveilans=''";
              }elseif($jnsKirim == 2){
                     $query .="AND eps.id_registry=''";
              }

              $query .="LIMIT 100";

              $bind = $this->db->query($query, array($jnsKirim));
              return $bind->result_array();
       }

       public function regkanAllIn($mr)
       {
              $query = $this->db->query("CALL db_regkan.RegistrasiKankerAllin4($mr)");
              $res = $query->result_array();
              $query->next_result(); 
              $query->free_result(); 
              return $res;
       }
}
