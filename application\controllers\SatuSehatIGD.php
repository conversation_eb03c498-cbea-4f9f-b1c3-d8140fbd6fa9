<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatIGD extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("UTC");
    $this->load->model('IHSModel');
    $this->load->model('SatuSehatModel');
    $this->load->model('SatuSehatIGDModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ]; //PROD

    // $postDataArray = [
    //   'client_id' => 'KsRtseuJpe4aCTWIcmAVBDdhgUuXGNCFijmfflaWbCNSvhrA','client_secret' => 'aMAlbyP6JVgn6SWf2j3ZpTNozmPfvhUtLr1PUqOkoK6qqiSVdJ5w4Ub2z1AdDM00'
    // ]; //STAGING

    // $postDataArray = [
    //     'client_id' => 'fs2BJRXk1OgoRCIkZA6UPsAGdvv2lwPS0TI76WCXWxtyAHz7','client_secret' => '2VeU4CziJdKZxnfHLn7aw6kktOC4EAnVHKVlUIy3ZQetvjAyCfq6czKDjklfdNFk'
    // ]; //DEV

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials'; //Prod
    // $url = 'https://api-satusehat-stg.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials'; //Staging
    // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials'; //Dev

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
  }

  function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomString;
  }

  function postBedIGDKosong(){

    $dataBedIGD = $this->SatuSehatIGDModel->sendBed()->result_array();
    foreach($dataBedIGD as $dbi){
        // $store = array(
        //     'NAMA_RUANGAN'      => "Ruang IGD",
        //     'NAMA_KAMAR'        => "Kamar IGD",
        //     'NAMA_BED'          => "Bed IGD - ".$x,
        //     'ID_KELAS_IHS'      => 3,
        //     'NAMA_KELAS_IHS'    => "Kelas 3",
        //     'ID_PARTOF'         => "630f6868-5218-4db4-9340-1fd4ed0654fe",
        //     'NAMA_PARTOF'       => "Kamar IGD",
        // );

        // $this->db->insert('data_ihs.data_bed_kosong_igd', $store);  
        //   $token = $this->getToken();
            $data = '
                    {
                        "resourceType": "Location",
                        "identifier": [
                            {
                                "system": "http://sys-ids.kemkes.go.id/organization/100025609",
                                "value": "'.$dbi['NAMA_BED'].'"
                            }
                        ],
                        "status": "active",
                        "name": "'.$dbi['NAMA_BED'].', Kamar IGD, Ruang IGD",
                        "mode": "instance",
                        "telecom": [
                            {
                                "system": "phone",
                                "value": "+6221-5681570",
                                "use": "work"
                            },
                            {
                                "system": "email",
                                "value": "<EMAIL>",
                                "use": "work"
                            },
                            {
                                "system": "url",
                                "value": "www.dharmais.com",
                                "use": "work"
                            }
                        ],
                        "type": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.kemkes.go.id/CodeSystem/location-type",
                                        "code": "RT0004",
                                        "display": "Tempat Tidur"
                                    }
                                ]
                            }
                        ],
                        "physicalType": {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/location-physical-type",
                                    "code": "bd",
                                    "display": "Bed"
                                }
                            ]
                        },
                        "operationalStatus" : 
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/v2-0116",
                                    "code": "U",
                                    "display": "Unoccupied"
                                },
                        "position": {
                            "longitude": -6.23115426275766,
                            "latitude": 106.83239885393944,
                            "altitude": 0
                        },
                        "managingOrganization": {
                            "reference": "Organization/100025609"
                        },
                        "extension": [
                            {
                                "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/LocationServiceClass",
                                "valueCodeableConcept": {
                                    "coding": [
                                        {
                                            "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Inpatient",
                                            "code": "3",
                                            "display": "Kelas 3"
                                        }
                                    ]
                                }
                            }
                        ],
                        "partOf": {
                            "reference": "Location/'.$dbi['ID_PARTOF'].'",
                            "display": "'.$dbi['NAMA_PARTOF'].'"
                        }
                    }
            
            ';
            // echo $data;
            // $json = json_decode($data, true);
            // print_r($json);
            // echo '<pre>' . $data . '</pre>';
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location'; //DEV
        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Location'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanLocation = array(
        //         'id_ihs'         => $res->id,
        //         'id_ruangan'     => '105140101',
        //         'id_kamar'       => '105140101',
        //         'id_bed'         => '105140101',
        //         'description'    => 'Bed '.$dbi['NAMA_BED'],
        //         'physicaltype'   => 'bd',
        //         'partof'         => $dbi['ID_PARTOF']
        //     );

        //     $this->db->insert('ihs.tb_location', $simpanLocation);  
        //    // echo '<pre>'.print_r($simpanLocation)."</pre>";
        // }

        // $dataBedIGD = array (
        //     'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
        //     'HTTPCODE'                     => $httpcode,
        //     'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        // );

        // $this->db->where('ID', $dbi['ID']);
        // $this->db->update('data_ihs.data_bed_kosong_igd', $dataBedIGD);

        // $simpanLog = array(
        //     'id_ruangan'       => '105140101',
        //     'id_ihs'           => $res->id,
        //     'log'              => $data,
        //     'response'         => $response,
        //     'http_code'        => $httpcode,
        //     'jenis'            => 'BED IGD'
        //   );
        //   $this->db->insert('ihs.tb_log_location', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
    }
  }

  function postEncounter(){

    $dataEnc = $this->SatuSehatIGDModel->sendEncounter()->result_array();
    foreach($dataEnc as $data1){
      $token = $this->getToken();
      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $data1['TGL_MASUK']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '
        {
            "resourceType": "Encounter",
            "identifier": [
                {
                    "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
                    "value": "'.$data1['NOPEN'].'"
                }
            ],
            "status": "arrived",
            "class": {
                "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                "code": "EMER",
                "display": "emergency"
            },
            "subject": {
                "reference": "Patient/'.$data1['ID_IHS_PASIEN'].'",
                "display": "'.$data1['NAMA_PASIEN'].'"
            },
            "participant": [
                {
                    "type": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                    "code": "ATND",
                                    "display": "attender"
                                }
                            ]
                        }
                    ],
                    "individual": {
                        "reference": "Practitioner/'.$data1['ID_IHS_DOKTER'].'",
                        "display": "'.$data1['NAMA_DOKTER'].'"
                    }
                }
            ],
            "period": {
                "start": "'.$tglReg.'"
            },
            "location": [
                {
                    "location": {
                        "reference": "Location/b652a770-1c8b-4546-a386-ed42406d46d5",
                        "display": "Ruangan IGD, Instalasi Gawat Darurat"
                    },
                    "period": {
                        "start": "'.$tglReg.'"
                    },
                    "extension": [
                        {
                          "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/ServiceClass",
                          "extension": [
                            {
                              "url": "value",
                              "valueCodeableConcept": {
                                "coding": [
                                  {
                                    "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Outpatient",
                                    "code": "reguler",
                                    "display": "Kelas Reguler"
                                  }
                                ]
                              }
                            },
                            {
                              "url": "upgradeClassIndicator",
                              "valueCodeableConcept": {
                                "coding": [
                                  {
                                    "system": "http://terminology.kemkes.go.id/CodeSystem/locationUpgradeClass",
                                    "code": "kelas-tetap",
                                    "display": "Kelas Tetap Perawatan"
                                  }
                                ]
                              }
                            }
                          ]
                        }
                    ]
                }
            ],
            "statusHistory": [
                {
                    "status": "arrived",
                    "period": {
                        "start": "'.$tglReg.'"
                    }
                }
            ],
            "serviceProvider": {
                "reference": "Organization/100025609"
            }
        }';
    //   echo '<pre>' . $data . '</pre>';
    //   echo "<pre>";print_r($data);echo "</pre>";
    $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter'; //Prod
    // $url = 'https://api-satusehat-stg.dto.kemkes.go.id/fhir-r4/v1/Encounter'; //Staging
    // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter'; //Dev

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if($httpcode == "201"){
    // //     // echo 'Status Code : ' . $httpcode . '<br>';
    // //     // echo '<pre>'.$response."</pre>";

        $simpanEncounter = array(
          'id_ihs_pasien' => $data1['ID_IHS_PASIEN'],
          'id_ihs_practitioner' => $data1['ID_IHS_DOKTER'],
          'encounter' => $res->id,
          'nopen' => $data1['NOPEN'],
          'jenis' => 2
        );

        $this->db->insert('ihs.tb_encounter', $simpanEncounter); 

      }

        date_default_timezone_set('Asia/Jakarta');
        $dataEncIGD = array (
            'ENCOUNTER'                    => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        );

        $this->db->where('NOPEN', $data1['NOPEN']);
        $this->db->update('data_ihs.data_encounter_igd', $dataEncIGD);

        $simpanLogEncounter = array(
          'nopen'     => $data1['NOPEN'],
          'log'       => $data,
          'httpcode'       => $httpcode,
          'id_encounter' => $res->id,
          'response'       => $response,
          'jenis'       => 'POST',
        );
        $this->db->insert('ihs.tb_log_encounter_igd', $simpanLogEncounter);

    }

  }

    public function postTriaseTransport(){
        $dataTriase = $this->SatuSehatIGDModel->sendTriaseTransport()->result_array();
        foreach($dataTriase as $tri){
            $token = $this->getToken();
            $datetriase = date_create_from_format('Y-m-d H:i:s', $tri['TGL_MASUK']);
            $tglTriase = $datetriase->format(DATE_ATOM);
            $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "74286-6",
                                "display": "Transport mode to hospital"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$tri['ID_IHS_PASIEN'].'",
                        "display": "'.$tri['NAMA_PASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$tri['ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglTriase.'",
                    "issued": "'.$tglTriase.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$tri['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "valueCodeableConcept": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "LA46-8",
                                "display": "Other"
                            }
                        ],
                        "text": "Tidak diketahui"
                    }
                }';
                // echo '<pre>' . $data . '</pre><br>';

            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
            $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            if($httpcode == "201"){
                $simpanObservation = array(
                'observation'        => $res->id,
                'nopen'              => $tri['NOPEN'],
                'id_encounter'       => $tri['ENCOUNTER'],
                'kategori'           => 'TRIASE',
                'tipe'               => 'TRANSPORT',
                );
                $this->db->insert('ihs.tb_observation_igd', $simpanObservation);
                // echo '<pre>'.print_r($simpanObservation)."</pre>";
            }

            date_default_timezone_set('Asia/Jakarta');
            $dataTriaseTransportIGD = array (
                'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
                'HTTPCODE'                     => $httpcode,
                'CREATED_AT'                   => date('Y-m-d H:i:s'),
                'STATUS'                       => ($httpcode == "201") ? '2' : '1',
            );

            $this->db->where('NOPEN', $tri['NOPEN']);
            $this->db->update('data_ihs.data_triasetransport_igd', $dataTriaseTransportIGD);

            $simpanLog = array(
                'nopen'          => $tri['NOPEN'],
                'id_encounter'   => $tri['ENCOUNTER'],
                'id_observation' => $res->id,
                'log'            => $data,
                'response'       => $response,
                'http_code'      => $httpcode,
                'kategori'       => 'TRIASE',
                'tipe'           => 'TRANSPORT',
            );
            $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
                // }
        }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postTriaseRujukan(){
        $dataTriase = $this->SatuSehatIGDModel->sendTriaseRujukan()->result_array();
        foreach($dataTriase as $tri){
            $token = $this->getToken();
            $dateTriase = date_create_from_format('Y-m-d H:i:s', $tri['TGL_MASUK']);
            $tglTriase = $dateTriase->format(DATE_ATOM);
            // $status = $tri['STATUS_RUJUKAN'] == 1 ? 'true' : 'false';
            $data = '{
                "resourceType": "Observation",
                "status": "final",
                "category": [
                    {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                "code": "survey",
                                "display": "Survey"
                            }
                        ]
                    }
                ],
                "code": {
                    "coding": [
                        {
                            "system": "http://terminology.kemkes.go.id/CodeSystem/clinical-term",
                            "code": "OC000034",
                            "display": "Surat Pengantar Rujukan"
                        }
                    ]
                },
                "performer": [
                    {
                        "reference": "Practitioner/'.$tri['ID_IHS_DOKTER'].'"
                    }
                ],
                "subject": {
                    "reference": "Patient/'.$tri['ID_IHS_PASIEN'].'",
                    "display": "'.$tri['NAMA_PASIEN'].'"
                },
                "encounter": {
                    "reference": "Encounter/'.$tri['ENCOUNTER'].'"
                },
                "effectiveDateTime": "'.$tglTriase.'",
                "issued": "'.$tglTriase.'",
                "valueBoolean": '.$tri['STATUS_RUJUKAN'].'
            }';
            // echo '<pre>' . $data . '</pre><br>';

        
            // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
              $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
            $cURL = curl_init();
            curl_setopt($cURL, CURLOPT_URL,$url);
            curl_setopt($cURL, CURLOPT_HEADER,false);
            curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
            curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer ".$token." "
            ));
            curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
            curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
            $response = curl_exec($cURL);
            $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
            curl_close($cURL);
            $res = json_decode($response);
            // if(isset($res->fault)){
            //   $this->getToken();
            // } else {
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$response."</pre>";  
            if($httpcode == "201"){
                $simpanObservation = array(
                'observation'        => $res->id,
                'nopen'              => $tri['NOPEN'],
                'id_encounter'       => $tri['ENCOUNTER'],
                'kategori'           => 'TRIASE',
                'tipe'               => 'RUJUKAN',
                );
                $this->db->insert('ihs.tb_observation_igd', $simpanObservation);
                // echo '<pre>'.print_r($simpanObservation)."</pre>";
            }

            date_default_timezone_set('Asia/Jakarta');
            $dataTriaseRujukanIGD = array (
                'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
                'HTTPCODE'                     => $httpcode,
                'CREATED_AT'                   => date('Y-m-d H:i:s'),
                'STATUS'                       => ($httpcode == "201") ? '2' : '1',
            );

            $this->db->where('NOPEN', $tri['NOPEN']);
            $this->db->update('data_ihs.data_triaserujukan_igd', $dataTriaseRujukanIGD);

            $simpanLog = array(
                'nopen'          => $tri['NOPEN'],
                'id_encounter'   => $tri['ENCOUNTER'],
                'id_observation' => $res->id,
                'log'            => $data,
                'response'       => $response,
                'http_code'      => $httpcode,
                'kategori'       => 'TRIASE',
                'tipe'           => 'RUJUKAN',
            );
            $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
            // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postTriaseKondisi()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekTriase(2,3)->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $triase = $this->SatuSehatIGDModel->TriaseKondisi($pendaf['nopen']);
      if($triase->num_rows() > 0){
        foreach($triase->result_array() as $tri){
          // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
          $datetriase = date_create_from_format('Y-m-d', $tri['CREATED_AT_TRIASE']);
          $tglAnamnesis = $datetriase->format(DATE_ATOM);
          $data = '{
                "resourceType": "Observation",
                "status": "final",
                "category": [
                    {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                "code": "survey",
                                "display": "Survey"
                            }
                        ]
                    }
                ],
                "code": {
                    "coding": [
                        {
                            "system": "http://loinc.org",
                            "code": "75910-0",
                            "display": "Canadian triage and acuity scale [CTAS]"
                        }
                    ]
                },
                "subject": {
                    "reference": "Patient/'.$tri['ID_IHS_PASIEN'].'",
                    "display": "'.$tri['NAMA_PASIEN'].'"
                },
                "encounter": {
                    "reference": "Encounter/'.$tri['ENCOUNTER'].'"
                },
                "effectiveDateTime": "'.$tglTriase.'",
                "issued": "'.$tglTriase.'",
                "performer": [
                    {
                        "reference": "Practitioner/'.$tri['ID_IHS_PRACTITIONER'].'"
                    }
                ],
                "valueCodeableConcept": {
                    "coding": [
                        {
                            "system": "http://loinc.org",
                            "code": "'.$tri['ATS_LOINC'].'",
                            "display": "'.$tri['ATS'].'"
                        }
                    ]
                }
            }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $tri['ID_ENCOUNTER'],
        //     'kategori'           => 'TRIASE KONDISI',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $tri['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'          => 'TRIASE KONDISI',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postAnamnesisKeluhan()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAnamnesis(2,1)->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $anamnesis = $this->SatuSehatIGDModel->AnamnesisKeluhan($pendaf['nopen']);
      if($anamnesis->num_rows() > 0){
        foreach($anamnesis->result_array() as $anam){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
        //   $datetriase = date_create_from_format('Y-m-d', $anam['CREATED_AT_TRIASE']);
        //   $tglTriase = $datetriase->format(DATE_ATOM);
          $tglDaftar = date_indo($anam['TGL_DAFTAR']);
          $data = '{
                    "resourceType": "Condition",
                    "clinicalStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                "code": "active",
                                "display": "Active"
                            }
                        ]
                    },
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                    "code": "problem-list-item",
                                    "display": "Problem List Item"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://hl7.org/fhir/sid/icd-10",
                                "code": "'.$anam['KODE_ICD'].'",
                                "display": "'.$anam['DIAGNOSA'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$anam['ID_IHS_PASIEN_DUMMY'].'",
                        "display": "'.$anam['NAMAPASIEN_2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$anam['ID_ENCOUNTER'].'",
                        "display": "Kunjungan '.$anam['NAMAPASIEN_2'].' tanggal '.$tglDaftar.'"
                    }
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_condition'   => $res->id,
        //     'id_diagnosa'   => $anam['ID_DIAGNOSA'],
        //     'id_encounter'    => $anam['ID_ENCOUNTER'],
        //     'jenis'           => 1,
        //     'kode_diagnosa'   => $anam['KODE_ICD'],
        //     'diagnosa_deskripsi' => $anam['DIAGNOSA'],
        //     'nopen'    => $pendaf['nopen'],
        //     'kategori'    => 'ANAMNESIS KELUHAN',
        //     );
        //     $this->db->insert('ihs.tb_condition_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $anam['ID_ENCOUNTER'],
        //     'id_condition' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'    => 'ANAMNESIS KELUHAN',
        // );
        // $this->db->insert('ihs.tb_log_condition_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postAnamnesisRiwayatPenyakit()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAnamnesis(2,2)->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $anamnesis = $this->SatuSehatIGDModel->AnamnesisRiwayatPenyakit($pendaf['nopen']);
      if($anamnesis->num_rows() > 0){
        foreach($anamnesis->result_array() as $anam){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
        //   $datetriase = date_create_from_format('Y-m-d', $anam['CREATED_AT_TRIASE']);
        //   $tglTriase = $datetriase->format(DATE_ATOM);
          $tglDaftar = date_indo($anam['TANGGAL_DAFTAR']);
          $data = '{
                "resourceType": "Condition",
                "clinicalStatus": {
                    "coding": [
                        {
                            "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                            "code": "active",
                            "display": "Active"
                        }
                    ]
                },
                "category": [
                    {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                "code": "problem-list-item",
                                "display": "Problem List Item"
                            }
                        ]
                    }
                ],
                "code": {
                    "coding": [
                        {
                            "system": "http://hl7.org/fhir/sid/icd-10",
                            "code": "'.$anam['DIAGNOSA'].'",
                            "display": "'.$anam['DIAGNOSA_MRCONSO'].'"
                        }
                    ]
                },
                "subject": {
                    "reference": "Patient/'.$anam['id_ihs_pasien'].'",
                    "display": "'.$anam['NAMAPASIEN'].'"
                },
                "encounter": {
                    "reference": "Encounter/'.$anam['encounter'].'",
                    "display": "Kunjungan '.$anam['NAMAPASIEN'].' tanggal '.$tglDaftar.'"
                }
            }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_condition'   => $res->id,
        //     'id_diagnosa'   => $anam['ID_DIAGNOSA'],
        //     'id_encounter'    => $anam['ID_ENCOUNTER'],
        //     'jenis'           => 1,
        //     'kode_diagnosa'   => $anam['KODE_ICD'],
        //     'diagnosa_deskripsi' => $anam['DIAGNOSA'],
        //     'nopen'    => $pendaf['nopen'],
        //     'kategori'    => 'ANAMNESIS KELUHAN',
        //     );
        //     $this->db->insert('ihs.tb_condition_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $anam['ID_ENCOUNTER'],
        //     'id_condition' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'    => 'ANAMNESIS KELUHAN',
        // );
        // $this->db->insert('ihs.tb_log_condition_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postAsesmenNyeri()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,1)->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->AsesemenNyeriIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['TANGGALMASUK']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "22253000",
                                "display": "Pain"
                            }
                        ]
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "valueBoolean": '.$ases['NYERI'].'
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'ASESMEN NYERI',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'          => 'ASESMEN NYERI',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postSkalaNyeri()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'SKALA NYERI')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->SkalaNyeriIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['TANGGALMASUK']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "**********",
                                "display": "Numeric rating scale score"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "valueInteger": '.$ases['SKOR_SKALA_NYERI'].'
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'SKALA NYERI',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'          => 'SKALA NYERI',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postLokasiNyeri()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'LOKASI NYERI')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->LokasiNyeriIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['TANGGAL_SKRININGNYERI']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "38204-4",
                                "display": "Pain primary location"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "valueString": "'.$ases['LOKASI_NYERI'].'"
                }';
            //  echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'LOKASI NYERI',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'LOKASI NYERI',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postPenyebabNyeri()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'PENYEBAB NYERI')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->PenyebabNyeriIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['TANGGAL_SKRININGNYERI']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://terminology.kemkes.go.id/CodeSystem/clinical-term",
                                "code": "OC000023",
                                "display": "Penyebab nyeri"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "valueString": "'.$ases['PENYEBAB'].'"
                }';
            //  echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'PENYEBAB NYERI',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'PENYEBAB NYERI',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postRisikoJatuh()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'RISIKO JATUH')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->RisikoJatuhIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['TANGGAL_RISIKOJATUH']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "exam",
                                    "display": "Exam"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "59461-4",
                                "display": "Fall risk level [Morse Fall Scale]"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN2'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "valueQuantity": {
                        "value": '.$ases['TOTAL_SKOR'].',
                        "unit": "{score}",
                        "system": "http://unitsofmeasure.org",
                        "code": "{score}"
                    },
                    "interpretation": [
                        {
                        "coding": [
                            {
                                "system": "http://terminology.kemkes.go.id/CodeSystem/clinical-term",
                                "code": "'.$ases['KODE_TERMINOLOGI'].'",
                                "display": "'.$ases['DESKRIPSI_TERMINOLOGI'].'"
                            }
                        ],
                            "text": "'.$ases['STATUS_MFS'].'"
                        }
                    ]
                }';
            //  echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'RISIKO JATUH',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'RISIKO JATUH',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postTingkatKesadaran()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'TINGKAT KESADARAN')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->TingkatKesadaranIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['CREATEDAT']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "exam",
                                    "display": "Exam"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "67775-7",
                                "display": "Level of responsiveness"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN'].'"
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "valueCodeableConcept": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "'.$ases['KESADARAN'].'",
                                "display": "'.$ases['DESKRIPSI_KESADARAN'].'"
                            }
                        ]
                    }
                }';
            //  echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'TINGKAT KESADARAN',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'TINGKAT KESADARAN',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postVitalSign()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'VITAL SIGN')->result_array();

    if(!empty($pendaftaran)){
      foreach($pendaftaran as $pendaf) {
        $asesmen = $this->SatuSehatIGDModel->VitalSignIGD($pendaf['nopen'])->row_array();
        if(!empty($asesmen)){
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['CREATEDAT']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $token = $this->getToken();
        // START NADI
        if(isset($asesmen['NADI'])) {
        $datanadi = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "vital-signs",
                                    "display": "Vital Signs"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "8867-4",
                                "display": "Heart rate"
                            }
                        ]
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "valueQuantity": {
                        "value": '.$ases['NADI'].',
                        "unit": "beats/minute",
                        "system": "http://unitsofmeasure.org",
                        "code": "/min"
                    }
                }';
            //  echo '<pre>' . $datanadi . '</pre><br>';

        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //   "Content-Type: application/json",
        //   "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datanadi);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);

        // if($httpcode == "201"){
        //   echo 'Status Code : ' . $httpcode . '<br>';
        //   echo '<pre>'.$response."</pre>";

        //  $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'VITAL SIGN',
        //     'tipe'               => 'NADI'
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);      
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'VITAL SIGN',
        //     'tipe'           => 'NADI'
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
      }
        // END NADI

        // START PERNAPASAN
        if(isset($asesmen['PERNAPASAN'])) {
          // $token = $this->getToken();
        $datapernapasan = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "vital-signs",
                                        "display": "Vital Signs"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "9279-1",
                                    "display": "Respiratory rate"
                                }
                            ]
                        },
                        "subject": {
                            "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                            "display": "'.$ases['NAMAPASIEN'].'"
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "encounter": {
                            "reference": "Encounter'.$ases['ID_ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tglAsesmen.'",
                        "issued": "'.$tglAsesmen.'",
                        "valueQuantity": {
                            "value": '.$ases['PERNAPASAN'].',
                            "unit": "breaths/minute",
                            "system": "http://unitsofmeasure.org",
                            "code": "/min"
                        }
                    }';

        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //   "Content-Type: application/json",
        //   "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datapernapasan);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);

        // if($httpcode == "201"){
        // //   echo 'Status Code : ' . $httpcode . '<br>';
        // //   echo '<pre>'.$response."</pre>";

        //  $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'VITAL SIGN',
        //     'tipe'               => 'PERNAPASAN'
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);      
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'VITAL SIGN',
        //     'tipe'           => 'PERNAPASAN'
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
      } 
        // END PERNAPASAN 

      // START TEKANAN DARAH (Sistolik)
      if(isset($asesmen['SISTOLE'])) {
        // $token = $this->getToken();
        $datasistole = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "vital-signs",
                                    "display": "Vital Signs"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "8480-6",
                                "display": "Systolic blood pressure"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN'].'"
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "valueQuantity": {
                        "value": '.$ases['SISTOLE'].',
                        "unit": "mm[Hg]",
                        "system": "http://unitsofmeasure.org",
                        "code": "mm[Hg]"
                    }
                }';

        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //   "Content-Type: application/json",
        //   "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasistole);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);

        // if($httpcode == "201"){
        // //   echo 'Status Code : ' . $httpcode . '<br>';
        // //   echo '<pre>'.$response."</pre>";

        //  $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'VITAL SIGN',
        //     'tipe'               => 'SISTOLIK'
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);     
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'VITAL SIGN',
        //     'tipe'           => 'SISTOLIK'
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);

      }
      // END TEKANAN DARAH (Sistolik)

      // START TEKANAN DARAH (Diastolik)
      if(isset($asesmen['DIASTOLE'])) {
        // $token = $this->getToken();
        $datadiastol = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "vital-signs",
                                        "display": "Vital Signs"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "8462-4",
                                    "display": "Diastolic blood pressure"
                                }
                            ]
                        },
                        "subject": {
                            "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                            "display": "'.$ases['NAMAPASIEN'].'"
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "encounter": {
                            "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tglAsesmen.'",
                        "issued": "'.$tglAsesmen.'",
                        "valueQuantity": {
                            "value": '.$ases['DIASTOLE'].',
                            "unit": "mm[Hg]",
                            "system": "http://unitsofmeasure.org",
                            "code": "mm[Hg]"
                        }
                    }';

        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //   "Content-Type: application/json",
        //   "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datadiastol);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);

        // if($httpcode == "201"){
        // //   echo 'Status Code : ' . $httpcode . '<br>';
        // //   echo '<pre>'.$response."</pre>";

        //  $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'VITAL SIGN',
        //     'tipe'               => 'DIASTOLIK'
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);    
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'VITAL SIGN',
        //     'tipe'           => 'DIASTOLIK'
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
      }
      // END TEKANAN DARAH (Diastolik)

      // START SUHU
      if(isset($asesmen['SUHU'])) {
        // $token = $this->getToken();
        $datasuhu = '{
                        "resourceType": "Observation",
                        "status": "final",
                        "category": [
                            {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                        "code": "vital-signs",
                                        "display": "Vital Signs"
                                    }
                                ]
                            }
                        ],
                        "code": {
                            "coding": [
                                {
                                    "system": "http://loinc.org",
                                    "code": "8310-5",
                                    "display": "Body temperature"
                                }
                            ]
                        },
                        "subject": {
                            "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                            "display": "'.$ases['NAMAPASIEN'].'"
                        },
                        "performer": [
                            {
                                "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                            }
                        ],
                        "encounter": {
                            "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                        },
                        "effectiveDateTime": "'.$tglAsesmen.'",
                        "issued": "'.$tglAsesmen.'",
                        "valueQuantity": {
                            "value": '.$ases['SUHU'].',
                            "unit": "C",
                            "system": "http://unitsofmeasure.org",
                            "code": "Cel"
                        }
                    }';

        // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV

        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //   "Content-Type: application/json",
        //   "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasuhu);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);

        // if($httpcode == "201"){
        // //   echo 'Status Code : ' . $httpcode . '<br>';
        // //   echo '<pre>'.$response."</pre>";

        //  $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'VITAL SIGN',
        //     'tipe'               => 'SUHU'
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);    
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'VITAL SIGN',
        //     'tipe'           => 'SUHU'
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);

      }
      // END SUHU
      }else{
        // $simpanKosong = array(
        //   'nopen' => $cekObservation['NOPEN'],
        //   'observation' => 'tidak ada data - ' . $cekObservation['NOPEN'],
        //   'id_encounter' => $cekObservation['IDENCOUNTER'],
        //   'kategori' => "tidak ada data",
        //   'status' => "0",
        // );
        // $this->db->insert('ihs.tb_observation', $simpanKosong);
      }
    }
    }
  }

  public function postStatusPsikologis()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekAsesmenNyeri(2,'STATUS PSIKOLOGIS')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $asesmen = $this->SatuSehatIGDModel->TingkatKesadaranIGD($pendaf['nopen']);
      if($asesmen->num_rows() > 0){
        foreach($asesmen->result_array() as $ases){
          // echo $pendaf['NOPEN'] . ' - ' . $anam['ID_PROSEDUR'] . '<br>';
          $dateasesmen = date_create_from_format('Y-m-d H:i:s', $ases['CREATEDAT']);
          $tglAsesmen = $dateasesmen->format(DATE_ATOM);
        //   $tglDaftar = date_indo($ases['TANGGALMASUK']);
          $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "survey",
                                    "display": "Survey"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "8693-4",
                                "display": "Mental Status"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$ases['ID_IHS_PASIEN'].'",
                        "display": "'.$ases['NAMAPASIEN'].'"
                    },
                    "performer": [
                        {
                            "reference": "Practitioner/'.$ases['ID_IHS_DOKTER'].'"
                        }
                    ],
                    "encounter": {
                        "reference": "Encounter/'.$ases['ID_ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglAsesmen.'",
                    "issued": "'.$tglAsesmen.'",
                    "valueCodeableConcept": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "'.$ases['PSIKOLOGIS'].'",
                                "display": "'.$ases['DESKRIPSI_PSIKOLOGIS'].'"
                            }
                        ]
                    } 
                }';
            //  echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'observation'   => $res->id,
        //     'nopen'              => $pendaf['nopen'],
        //     'id_encounter'       => $ases['ID_ENCOUNTER'],
        //     'kategori'           => 'STATUS PSIKOLOGIS',
        //     );
        //     $this->db->insert('ihs.tb_observation_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $ases['ID_ENCOUNTER'],
        //     'id_observation' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'       => 'STATUS PSIKOLOGIS',
        // );
        // $this->db->insert('ihs.tb_log_observation_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }

    }

    public function postDiagnosisAwal()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekConditionIGD(2, 'DIAGNOSIS AWAL')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $condition = $this->SatuSehatIGDModel->DiagnosisAwalIGD($pendaf['nopen']);
      if($condition->num_rows() > 0){
        foreach($condition->result_array() as $con){
          // echo $pendaf['NOPEN'] . ' - ' . $con['ID_PROSEDUR'] . '<br>';
          $datecondition = date_create_from_format('Y-m-d', $con['CREATED_AT']);
          $tglCondition = $datecondition->format(DATE_ATOM);
        //   $tglDaftar = date_indo($con['TGL_DAFTAR']);
          $data = '{
                    "resourceType": "Condition",
                    "clinicalStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                "code": "active",
                                "display": "Active"
                            }
                        ]
                    },
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                    "code": "encounter-diagnosis",
                                    "display": "Encounter Diagnosis"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://hl7.org/fhir/sid/icd-10",
                                "code": "'.$con['ICD10'].'",
                                "display": "'.$con['DESKRIPSI_ICD10'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$con['ID_IHS_PASIEN'].'",
                        "display": "'.$con['NAMAPASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$con['ID_ENCOUNTER'].'"
                    },
                    "onsetDateTime": "'.$tglCondition.'",
                    "recordedDate": "'.$tglCondition.'",
                    "note": [
                        {
                            "text": "'.$con['DESKRIPSI_NOTES'].'"
                        }
                    ]
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_condition'   => $res->id,
        //     'id_diagnosa'   => con['ID_DIAGNOSA'],
        //     'id_encounter'    => con['ID_ENCOUNTER'],
        //     'jenis'           => 1,
        //     'kode_diagnosa'   => con['KODE_ICD'],
        //     'diagnosa_deskripsi' => con['DIAGNOSA'],
        //     'nopen'    => $pendaf['nopen'],
        //     'kategori'    => 'DIAGNOSIS AWAL',
        //     );
        //     $this->db->insert('ihs.tb_condition_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => con['ID_ENCOUNTER'],
        //     'id_condition' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'    => 'DIAGNOSIS AWAL',
        // );
        // $this->db->insert('ihs.tb_log_condition_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postDiagnosisKerja()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekConditionIGD(2, 'DIAGNOSIS KERJA')->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $condition = $this->SatuSehatIGDModel->DiagnosisKerjaIGD($pendaf['nopen']);
      if($condition->num_rows() > 0){
        foreach($condition->result_array() as $con){
          // echo $pendaf['NOPEN'] . ' - ' . $con['ID_PROSEDUR'] . '<br>';
          $datecondition = date_create_from_format('Y-m-d', $con['CREATED_AT']);
          $tglCondition = $datecondition->format(DATE_ATOM);
        //   $tglDaftar = date_indo($con['TGL_DAFTAR']);
          $data = '{
                    "resourceType": "Condition",
                    "clinicalStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                "code": "active",
                                "display": "Active"
                            }
                        ]
                    },
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                    "code": "encounter-diagnosis",
                                    "display": "Encounter Diagnosis"
                                }
                            ]
                        }
                    ],
                    "verificationStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
                                "code": "provisional",
                                "display": "Provisional"
                            }
                        ]
                    },
                    "code": {
                        "coding": [
                            {
                                "system": "http://hl7.org/fhir/sid/icd-10",
                                "code": "'.$con['ICD10'].'",
                                "display": "'.$con['DESKRIPSI_ICD10'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$con['ID_IHS_PASIEN'].'",
                        "display": "'.$con['NAMAPASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$con['ID_ENCOUNTER'].'"
                    },
                    "onsetDateTime": "'.$tglCondition.'",
                    "recordedDate": "'.$tglCondition.'",
                    "note": [
                        {
                            "text": "'.$con['DESKRIPSI_NOTES'].'"
                        }
                    ]
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_condition'   => $res->id,
        //     'id_diagnosa'   => $con['ID_DIAGNOSA'],
        //     'id_encounter'    => $con['ID_ENCOUNTER'],
        //     'jenis'           => 1,
        //     'kode_diagnosa'   => $con['KODE_ICD'],
        //     'diagnosa_deskripsi' => $con['DIAGNOSA'],
        //     'nopen'    => $pendaf['nopen'],
        //     'kategori'    => 'DIAGNOSIS KERJA',
        //     );
        //     $this->db->insert('ihs.tb_condition_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $con['ID_ENCOUNTER'],
        //     'id_condition' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'    => 'DIAGNOSIS KERJA',
        // );
        // $this->db->insert('ihs.tb_log_condition_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postProsedur()
  {
    $pendaftaran = $this->SatuSehatIGDModel->cekProcedureIGD()->result_array();
    foreach($pendaftaran as $pendaf){
    //   $token = $this->getToken();
      // echo '<pre>'.$token."</pre>";  
      $procedure = $this->SatuSehatIGDModel->ProcedureIGD($pendaf['nopen']);
      if($procedure->num_rows() > 0){
        foreach($procedure->result_array() as $pro){
          // echo $pendaf['NOPEN'] . ' - ' . $pro['ID_PROSEDUR'] . '<br>';
          $dateawal = date_create_from_format('Y-m-d H:i:s', $pro['TGL_MULAI']);
          $tglAwal = $dateawal->format(DATE_ATOM);

          $dateakhir = date_create_from_format('Y-m-d H:i:s', $pro['TGL_SELESAI']);
          $tglAkhir = $dateakhir->format(DATE_ATOM);
        //   $tglDaftar = date_indo($pro['TGL_DAFTAR']);
          $data = '{
                    "resourceType": "Procedure",
                    "status": "completed",
                    "category": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "'.$pro['KATEGORI'].'",
                                "display": "'.$pro['DESKRIPSI_KATEGORI'].'"
                            }
                        ],
                        "text": "Prosedur Emergensi"
                    },
                    "code": {
                        "coding": [
                            {
                                "system": "http://hl7.org/fhir/sid/icd-9-cm",
                                "code": "'.$pro['ICD9'].'",
                                "display": "'.$pro['DESKRIPSI_ICD9'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$pro['ID_IHS_PASIEN'].'",
                        "display": "'.$pro['NAMAPASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$pro['ID_ENCOUNTER'].'"
                    },
                    "performedPeriod": {
                        "start": "'.$tglAwal.'",
                        "end": "'.$tglAkhir.'"
                    },
                    "performer": [
                        {
                            "actor": {
                                "reference": "Practitioner/'.$pro['ID_IHS_DOKTER'].'",
                                "display": "'.$pro['NAMA_DOKTER'].'"
                            }
                        }
                    ],
                    "reasonCode": [
                        {
                            "coding": [
                                {
                                    "system": "http://hl7.org/fhir/sid/icd-10",
                                    "code": "'.$pro['ICD10'].'",
                                "display": "'.$pro['DESKRIPSI_ICD10'].'"
                                }
                            ]
                        }
                    ],
                    "note": [
                        {
                            "text": "Tindakan '.$pro['DESKRIPSI_ICD9'].'"
                        }
                    ]
                }';
            // echo '<pre>' . $data . '</pre><br>';

        }
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Procedure'; //DEV
        //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Procedure'; //PROD
        // $cURL = curl_init();
        // curl_setopt($cURL, CURLOPT_URL,$url);
        // curl_setopt($cURL, CURLOPT_HEADER,false);
        // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        //     "Content-Type: application/json",
        //     "Authorization: Bearer ".$token." "
        // ));
        // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        // $response = curl_exec($cURL);
        // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        // curl_close($cURL);
        // $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_prosedur_ihs'    => $res->id,
        //     'nopen'              => $pro['NOMOR'],
        //     'id_encounter'       => $pro['ID_ENCOUNTER'],
        //     'prosedur'           => $pro['ID_PROSEDUR'],
        //     );
        //     $this->db->insert('ihs.tb_procedure_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $pro['ID_ENCOUNTER'],
        //     'id_procedure' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        // );
        // $this->db->insert('ihs.tb_log_procedure_igd', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
        }
    }
        // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
    }

    public function postKondisiPulang()
    {
      $pendaftaran = $this->SatuSehatIGDModel->cekConditionIGD(2, 'KONDISI PULANG')->result_array();
      foreach($pendaftaran as $pendaf){
      //   $token = $this->getToken();
        // echo '<pre>'.$token."</pre>";  
        $condition = $this->SatuSehatIGDModel->KondisiPulangIGD($pendaf['nopen']);
        if($condition->num_rows() > 0){
          foreach($condition->result_array() as $con){
            if($con['KEADAAN'] == '1' || $con['KEADAAN'] == '2' || $con['KEADAAN'] == '3'){
                $data = '{
                    "resourceType": "Condition",
                    "clinicalStatus": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                "code": "active",
                                "display": "Active"
                            }
                        ]
                    },
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                    "code": "problem-list-item",
                                    "display": "Problem List Item"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://snomed.info/sct",
                                "code": "'.$con['KODE_KEADAAN'].'",
                                "display": "'.$con['DESKRIPSI_KEADAAN'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$con['ID_IHS_PASIEN'].'",
                        "display": "'.$con['NAMAPASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$con['ID_ENCOUNTER'].'"
                    }
                }';
            }else{
                $data = '{
                        "resourceType": "Encounter",
                        "id": "{{Encounter_uuid}}",
                        "identifier": [
                            {
                                "system": "http://sys-ids.kemkes.go.id/encounter/{{Org_id}}",
                                "value": "P20240001"
                            }
                        ],
                        "status": "in-progress",
                        "class": {
                            "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                            "code": "AMB",
                            "display": "ambulatory"
                        },
                        "subject": {
                            "reference": "Patient/100000030009",
                            "display": "Budi Santoso"
                        },
                        "participant": [
                            {
                                "type": [
                                    {
                                        "coding": [
                                            {
                                                "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                                "code": "ATND",
                                                "display": "attender"
                                            }
                                        ]
                                    }
                                ],
                                "individual": {
                                    "reference": "Practitioner/N10000001",
                                    "display": "Dokter Bronsig"
                                }
                            }
                        ],
                        "period": {
                            "start": "2022-06-14T07:00:00+07:00",
                            "end": "2022-06-14T09:00:00+07:00"
                        },
                        "location": [
                            {
                                "location": {
                                    "reference": "Location/ef011065-38c9-46f8-9c35-d1fe68966a3e",
                                    "display": "Ruang 1A, Poliklinik Rawat Jalan"
                                }
                            }
                        ],
                        "statusHistory": [
                            {
                                "status": "arrived",
                                "period": {
                                    "start": "2022-06-14T07:00:00+07:00",
                                    "end": "2022-06-14T08:00:00+07:00"
                                }
                            },
                            {
                                "status": "in-progress",
                                "period": {
                                    "start": "2022-06-14T08:00:00+07:00",
                                    "end": "2022-06-14T09:00:00+07:00"
                                }
                            }
                        ],
                        "hospitalization": {
                            "dischargeDisposition": {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/discharge-disposition",
                                        "code": "home",
                                        "display": "Home"
                                    }
                                ],
                                "text" : "Anjuran dokter untuk pulang dan kontrol kembali 1 bulan setelah minum obat"
                            }
                        },
                        "serviceProvider": {
                            "reference": "Organization/{{Org_id}}"
                        }
                    }';
            }
            
              // echo '<pre>' . $data . '</pre><br>';
  
          }
          // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition'; //DEV
          //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition'; //PROD
          // $cURL = curl_init();
          // curl_setopt($cURL, CURLOPT_URL,$url);
          // curl_setopt($cURL, CURLOPT_HEADER,false);
          // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
          // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          //     "Content-Type: application/json",
          //     "Authorization: Bearer ".$token." "
          // ));
          // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
          // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
          // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
          // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
          // $response = curl_exec($cURL);
          // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
          // curl_close($cURL);
          // $res = json_decode($response);
          // if(isset($res->fault)){
          //   $this->getToken();
          // } else {
          // echo 'Status Code : ' . $httpcode . '<br>';
          // echo '<pre>'.$response."</pre>";  
          // if($httpcode == "201"){
        //     $simpanProcedure = array(
        //     'id_condition'   => $res->id,
        //     'id_diagnosa'   => $con['ID_DIAGNOSA'],
        //     'id_encounter'    => $con['ID_ENCOUNTER'],
        //     'jenis'           => 1,
        //     'kode_diagnosa'   => $con['KODE_ICD'],
        //     'diagnosa_deskripsi' => $con['DIAGNOSA'],
        //     'nopen'    => $pendaf['nopen'],
        //     'kategori'    => 'KONDISI PULANG',
        //     );
        //     $this->db->insert('ihs.tb_condition_igd', $simpanProcedure);
        //     // echo '<pre>'.print_r($simpanProcedure)."</pre>";
        // }

        // $simpanLog = array(
        //     'id_encounter'   => $con['ID_ENCOUNTER'],
        //     'id_condition' => $res->id,
        //     'log'            => $data,
        //     'response'       => $response,
        //     'http_code'      => $httpcode,
        //     'kategori'    => 'KONDISI PULANG',
        // );
        // $this->db->insert('ihs.tb_log_condition_igd', $simpanLog);
          // echo '<pre>'.print_r($simpanLog)."</pre>";
              // }
          }
      }
          // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
      }

    public function putEncounter()
  {

    $putEncounter = $this->SatuSehatIGDModel->PasienPulang()->result_array();
    foreach($putEncounter as $putEnc){
    //   $token = $this->getToken();
    //   $status = 1;
      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $putEnc['TANGGALMASUK']);
      $tglReg = $datereg->format(DATE_ATOM);

      // Tanggal Keluar
      $datakeluar = date_create_from_format('Y-m-d H:i:s', $putEnc['TANGGALKELUAR']);
      $tglKeluar = $datakeluar->format(DATE_ATOM);

      $data = '{
        "resourceType": "Encounter",
        "id": "'.$putEnc['ID_ENCOUNTER'].'",
        "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/encounter/10000187",
                "value": "'.$putEnc['ID_IHS_PASIEN_DUMMY'].'"
            }
        ],
        "status": "finished",
        "class": {
            "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
            "code": "EMER",
            "display": "emergency"
        },
        "subject": {
            "reference": "Patient/'.$putEnc['ID_IHS_PASIEN_DUMMY'].'",
            "display": "'.$putEnc['NAMAPASIEN2'].'"
        },
        "participant": [
            {
                "type": [
                    {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                "code": "ATND",
                                "display": "attender"
                            }
                        ]
                    }
                ],
                "individual": {
                    "reference": "Practitioner/'.$putEnc['ID_IHS_DOKTER_DUMMY'].'",
                    "display": "'.$putEnc['NAMADOKTER'].'"
                }
            }
        ],
        "period": {
            "start": "'.$tglReg.'",
            "end": "'.$tglKeluar.'"
        },
        "location": [
            {
                "location": {
                    "reference": "Location/'.$putEnc['ID_IHS_BED'].'",
                    "display": "'.$putEnc['NAMA_BED'].', Instalasi Gawat Darurat, Gedung Utama, Lantai 1"
                },
                "period": {
                    "start": "'.$tglReg.'",
                    "end": "'.$tglKeluar.'"
                },
                "extension": [
                    {
                        "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/ServiceClass",
                        "extension": [
                            {
                                "url": "value",
                                "valueCodeableConcept": {
                                    "coding": [
                                        {
                                            "system": "http://terminology.kemkes.go.id/CodeSystem/locationServiceClass-Outpatient",
                                            "code": "reguler",
                                            "display": "Kelas Reguler"
                                        }
                                    ]
                                }
                            },
                            {
                                "url": "upgradeClassIndicator",
                                "valueCodeableConcept": {
                                    "coding": [
                                        {
                                            "system": "http://terminology.kemkes.go.id/CodeSystem/locationUpgradeClass",
                                            "code": "kelas-tetap",
                                            "display": "Kelas Tetap Perawatan"
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                ]
            }
        ],
        ';

        $condition = $this->SatuSehatIGDModel->ConditionPulang($putEnc['NOPEN']);
        if($condition->num_rows() > 0){
          $data .='"diagnosis": [';
        //   $status = 2;
          foreach($condition->result_array() as $con){
            $data .='{
                "condition": {
                  "reference": "Condition/'.$con['ID_IHS_ANAMNESIS_KELUHAN'].'",
                  "display": "'.$con['DIAGNOSA'].'"
                },
                "use": {
                  "coding": [
                    {
                        "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
                        "code": "AD",
                        "display": "Admission diagnosis"
                    }
                  ]
                }
              }';
          }
          $data .='],
          ';
        }

        $data .='"statusHistory": [
          {
            "status": "in-progress",
            "period": {
              "start": "' . $tglReg . '",
              "end": "' . $tglKeluar . '"
            }
          },
          {
            "status": "finished",
            "period": {
              "start": "' . $tglKeluar . '",
              "end": "' . $tglKeluar . '"
            }
          }
        ],
        "serviceProvider": {
          "reference":"Organization/10000187"
        }
      }';

    // //   $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc["IDENCOUNTER"].''; //Prod
    // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc['ID_ENCOUNTER'].''; //Dev

    $data = str_replace(',]', ']', $data);

    // echo '<pre>' . $data . '</pre>';

    // $cURL = curl_init();
    // curl_setopt($cURL, CURLOPT_URL,$url);
    // curl_setopt($cURL, CURLOPT_HEADER,false);
    // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
    //   "Content-Type: application/json",
    //   "Authorization: Bearer ".$token." "
    // ));
    // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PUT');
    // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    // $response = curl_exec($cURL);
    // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    // curl_close($cURL);
    // $res = json_decode($response);

    // if($httpcode == "200"){
    // //   echo 'Status Code : ' . $httpcode . '<br>';
    // //   echo '<pre>'.$response."</pre>";

    //   $ubahEncounter = array(
    //     'status'         => 2,
    //   );
    //   $this->db->where('tb_encounter_.nopen', $putEnc['NOPEN']);
    //   $this->db->update('ihs.tb_encounter_', $ubahEncounter);      
    // }
    // $simpanLog = array(
    //     'id_encounter'   => $putEnc['ID_ENCOUNTER'],
    //     'log'            => $data,
    //     'response'       => $response,
    //     'httpcode'       => $httpcode,
    //     'jenis'          => 'PUT',
    //   );
    //   $this->db->insert('ihs.tb_log_encounter', $simpanLog);

    $data1 = '[
                    {
                        "op": "add",
                        "path": "/operationalStatus",
                        "value": {
                            "system": "http://terminology.hl7.org/CodeSystem/v2-0116",
                            "code": "U",
                            "display": "Unoccupied"
                        }
                    }
                ]';
//   echo '<pre>' . $data1 . '</pre>';

    // $url1 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location/'.$putEnc['ID_IHS_BED'].''; //Dev
    
    // $cURL1 = curl_init();
    // curl_setopt($cURL1, CURLOPT_URL,$url1);
    // curl_setopt($cURL1, CURLOPT_HEADER,false);
    // curl_setopt($cURL1, CURLOPT_RETURNTRANSFER,true);
    // curl_setopt($cURL1, CURLOPT_HTTPHEADER, array(
    //   "Content-Type: application/json-patch+json",
    //   "Authorization: Bearer ".$token." "
    // ));
    // curl_setopt($cURL1, CURLOPT_SSL_VERIFYPEER, false);
    // curl_setopt($cURL1, CURLOPT_CUSTOMREQUEST, 'PATCH');
    // curl_setopt($cURL1, CURLOPT_POSTFIELDS, $data1);
    // curl_setopt($cURL1, CURLOPT_CONNECTTIMEOUT,10);
    // $response1 = curl_exec($cURL1);
    // $httpcode1 = curl_getinfo($cURL1, CURLINFO_HTTP_CODE);
    // curl_close($cURL1);
    // $res1 = json_decode($response1);

    // $simpanLog1 = array(
    //     'nopen'          => $putEnc['NOPEN'],
    //     'id_encounter'   => $putEnc['ID_ENCOUNTER'],
    //     'id_bed'         => $putEnc['ID_IHS_BED'],
    //     'log'            => $data1,
    //     'response'       => $response1,
    //     'http_code'      => $httpcode1,
    //     'jenis'          => 'IGD',
    //     'status_bed'     => 'Unoccupied',
    // );
    // $this->db->insert('ihs.tb_log_location_bed', $simpanLog1);

    }
  }

//------------------------------------------------------------------------------FASE 2-----------------------------------------------------------------------------------------------

    function putEncounterRuangTriase()
    {
        $putEncounter = $this->SatuSehatIGDModel->updateRuangTriase()->result_array();
        foreach($putEncounter as $putEnc){
          $token = $this->getToken();
        //   $status = 1;
            // Tanggal Reg
            $datereg = date_create_from_format('Y-m-d H:i:s', $putEnc['TGL_MASUK_IGD']);
            $tglReg = $datereg->format(DATE_ATOM);

            // Tanggal Keluar
            $datatriase = date_create_from_format('Y-m-d H:i:s', $putEnc['TGL_MASUK_TRIASE']);
            $tglTriase = $datatriase->format(DATE_ATOM);

            $data = '{
                    "resourceType": "Encounter",
                    "id": "'.$putEnc['ENCOUNTER'].'",
                    "identifier": [
                        {
                            "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
                            "value": "'.$putEnc['NOPEN'].'"
                        }
                    ],
                    "status": "triaged",
                    "class": {
                        "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                        "code": "EMER",
                        "display": "emergency"
                    },
                    "subject": {
                        "reference": "Patient/'.$putEnc['ID_IHS_PASIEN'].'",
                        "display": "'.$putEnc['NAMA_PASIEN'].'"
                    },
                    "participant": [
                        {
                            "type": [
                                {
                                    "coding": [
                                        {
                                            "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                            "code": "ATND",
                                            "display": "attender"
                                        }
                                    ]
                                }
                            ],
                            "individual": {
                                "reference": "Practitioner/'.$putEnc['ID_IHS_DOKTER'].'",
                                "display": "'.$putEnc['NAMA_DOKTER'].'"
                            }
                        }
                    ],
                    "period": {
                        "start": "'.$tglReg.'"
                    },
                    "location": [
                        {
                            "location": {
                                "reference": "Location/'.$putEnc['ID_IHS_BED'].'",
                                "display": "'.$putEnc['NAMA_BED'].', Ruangan IGD, Instalasi Gawat Darurat"
                            },
                            "period": {
                                "start": "'.$tglReg.'"
                            }
                        }
                    ],
                    "statusHistory": [
                        {
                            "status": "arrived",
                            "period": {
                                "start": "'.$tglReg.'",
                                "end": "'.$tglTriase.'"
                            }
                        },
                        {
                            "status": "triaged",
                            "period": {
                                "start": "'.$tglTriase.'"
                            }
                        }
                    ],
                    "serviceProvider": {
                        "reference": "Organization/100025609"
                    }
                }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc["ENCOUNTER"].''; //Prod
        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc['ENCOUNTER'].''; //Dev

        // echo '<pre>' . $data . '</pre>';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        // if($httpcode == "200"){
        //   echo 'Status Code : ' . $httpcode . '<br>';
        //   echo '<pre>'.$response."</pre>";

          $encounterTriase = array(
            'HTTPCODE'       => $httpcode,
            'CREATED_AT'     => date('Y-m-d H:i:s'),
            'STATUS'         => ($httpcode == "200") ? '2' : '1',
          );
          $this->db->where('NOPEN', $putEnc['NOPEN']);
          $this->db->update('data_ihs.data_masuk_triase_igd', $encounterTriase);      
        // }

        $simpanLog = array(
            'nopen'          => $putEnc['NOPEN'],
            'id_encounter'   => $putEnc['ENCOUNTER'],
            'log'            => $data,
            'response'       => $response,
            'httpcode'       => $httpcode,
            'jenis'          => 'PUT (RUANG TRIASE)',
          );
          $this->db->insert('ihs.tb_log_encounter_igd', $simpanLog);

        }
    }

    function postCarepPlanIGD(){
        $careplan = $this->SatuSehatIGDModel->sendCarePlan();
        if($careplan->num_rows() > 0){
            foreach($careplan->result_array() as $cp){
            //   $token = $this->getToken();
                // echo $pendaf['NOPEN'] . ' - ' . $tri['ID_PROSEDUR'] . '<br>';
                    $date = date_create_from_format('Y-m-d H:i:s', $cp['CREATED_AT']);
                    $tgl = $date->format(DATE_ATOM);
                    $data = '
                            {
                                "resourceType": "CarePlan",
                                "title": "Rencana Rawat",
                                "status": "active",
                                "category": [
                                    {
                                        "coding": [
                                            {
                                                "system": "http://snomed.info/sct",
                                                "code": "702779007",
                                                "display": "Emergency health care plan agreed"
                                            }
                                        ]
                                    }
                                ],
                                "intent": "plan",
                                "description": "'.$cp['POC'].'",
                                "subject": {
                                    "reference": "Patient/'.$cp['ID_IHS_PASIEN'].'",
                                    "display": "'.$cp['NAMA_PASIEN'].'"
                                },
                                "encounter": {
                                    "reference": "Encounter/'.$cp['ENCOUNTER'].'"
                                },
                                "created": "'.$tgl.'",
                                "author": {
                                    "reference": "Practitioner/'.$cp['ID_IHS_PENGISI'].'"
                                }
                            }';
                    // echo '<pre>' . $data . '</pre><br>';
                // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/CarePlan'; //DEV
                // $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/CarePlan'; //PROD
                // $cURL = curl_init();
                // curl_setopt($cURL, CURLOPT_URL,$url);
                // curl_setopt($cURL, CURLOPT_HEADER,false);
                // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
                // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                //     "Content-Type: application/json",
                //     "Authorization: Bearer ".$token." "
                // ));
                // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
                // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
                // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
                // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
                // $response = curl_exec($cURL);
                // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
                // curl_close($cURL);
                // $res = json_decode($response);
                // if(isset($res->fault)){
                //   $this->getToken();
                // } else {
                // echo 'Status Code : ' . $httpcode . '<br>';
                // echo '<pre>'.$response."</pre>";  
                // if($httpcode == "201"){
                // $simpanCarePlan = array(
                //     'id_careplan'         => $res->id,
                //     'nopen'               => $cp['NOPEN'],
                //     'id_encounter'        => $cp['ENCOUNTER'],
                //     'id_ihs_pasien'       => $cp['ID_IHS_PASIEN'],
                //     'status'              => 1,
                //     'jenis'               => 'MASUK RANAP'
                //   );

                //   $this->db->insert('ihs.tb_careplan_igd', $simpanCarePlan);  
                //     // echo '<pre>'.print_r($simpanCarePlan)."</pre>";
                // }

                // $simpanLogCareplan = array(
                //     'id_encounter'         => $s['ENCOUNTER'],
                //     'id_careplan'          => $res->id,
                //     'log'                  => $data1,
                //     'response'             => $response,
                //     'http_code'            => $httpcode,
                //     'jenis'                => 'MASUK RANAP'
                //   );
                //   $this->db->insert('ihs.tb_log_careplan_igd', $simpanLogCareplan);
                // echo '<pre>'.print_r($simpanLogCareplan)."</pre>";
                    // }
            }
        } 
    }
}
