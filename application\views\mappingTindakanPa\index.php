<style>
	.pilihFilter{
		max-width: 100px;
	}
</style>

<div class="row mt-2">
	<div class="col-md text-center">
		<button type="button" class="btn btn-success refreshMenuMappingTindakanPa btn-block"><i class="fas fa-redo-alt"></i> Refresh</button>
	</div>
</div>

<form id="formMappingTindakanPa">
	<div class="row mt-3 form-inline">
		<div class="col-md-4">
			<div class="form-group">
				<label for="pilihTindakanPa">Tindakan PA</label>
				<select id="pilihTindakanPa" name="pilihTindakanPa">
					<option disabled selected>[ PILIH ]</option>
					<?php foreach($tindakanPA as $data){ ?>
						<option value="<?=$data['ID_TINDAKAN'];?>"><?=$data['NAMA_TINDAKAN'];?></option>
					<?php } ?>
				</select>
			</div>
		</div>

		<div class="col-md-4">
			<div class="form-group">
				<label for="pilihJenis">Jenis</label>
				<select id="pilihJenis" name="pilihJenis">
					<option value="1">Histologi</option>
					<option value="2">Sitologi</option>
					<option value="3">Patmol</option>
					<option value="4">IHK</option>
				</select>
			</div>
		</div>

		<div class="col-md-4">
			<div class="form-group mt-3">
				<button type="button" class="btn btn-primary tombolSimpanTindakanPa">Simpan</button>
			</div>
		</div>
	</div>
</form>

<div class="row mt-2">
	<div class="col-md-12">
		<div class="table-responsive">
			<table id="tableMappingTindakanPa" class="table table-striped table-bordered table-hover" width="100%">
				<thead>
					<tr>
						<th>NAMA TINDAKAN</th>
						<th>JENIS</th>
						<th>AKSI</th>
					</tr>
				</thead>
				<tbody>
					<?php foreach($dataTindakanPa as $data){ ?>
						<tr class="<?=($data['STATUS'] == 1) ? 'table-success' : 'table-danger'?>">
							<td><?=$data['NAMA_TINDAKAN'];?></td>
							<td><?=$data['JENIS'];?></td>
							<td class="text-center">
								<div class="form-check">
									<input class="form-check-input cekStatusTindakanPa" value="<?=$data['ID'];?>" type="checkbox" <?=($data['STATUS'] == 1) ? 'checked' : ''?>>
								</div>
							</td>
						</tr>
					<?php } ?>
				</tbody>
				<tfoot>
					<tr>
						<th>NAMA TINDAKAN</th>
						<th>JENIS</th>
						<th>AKSI</th>
					</tr>
				</tfoot>
			</table>
		</div>
	</div>
</div>
<script>
	$(document).ready(function(){
		$('#pilihTindakanPa').select2();
		$('#pilihJenis').select2();
		$('#tableMappingTindakanPa').DataTable();

		$('.refreshMenuMappingTindakanPa').on('click', function(){
			$('.mappingTindakanPa').trigger('click');
		});

		$('.tombolSimpanTindakanPa').on('click', function(){
			form = $("#formMappingTindakanPa").serializeArray();
			if($('#pilihTindakanPa').val() != null){
				$.ajax({
					type  : 'POST',
					url   : '<?php echo base_url() ?>IHS/simpanMappingTindakanPa',
					data  : form,
					success : function(data){
						alertify.success('Data Berhasil Mapping');
						$('.mappingTindakanPa').trigger('click');
					}
				});
			}else{
				alertify.error('Data Gagal Mapping');
			}
		});

		$('#tableMappingTindakanPa').on('change', '.cekStatusTindakanPa', function () {
			var id = $(this).val();
			if($(this).is(':checked') == true){
				var cek = 1;
			}else{
				var cek = 0;
			}
			$.ajax({
				type  : 'POST',
				url   : '<?php echo base_url() ?>IHS/ubahStatusMappingTindakanPa',
				data  : {id:id, cek:cek},
				success : function(data){
					alertify.success('Data Berhasil Ubah Mapping');
					$('.mappingTindakanPa').trigger('click');
				}
			});
		});

	});
</script>