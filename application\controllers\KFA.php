<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class K<PERSON> extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('KFAModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  function ambilKFA(){
    // $dataKfa = $this->SatuSehatModel->getImagingStudyRad()->result_array();
    // foreach($dataKfa as $dk){
    //   $token = $this->getToken();
      $token = 'jmwh9Il6qLySNDE8vlo1G3wUtCS7';
      // echo '<pre>'.$dk["ACSN"]."</pre>"; 
    //   $url = 'https://api-satusehat.kemkes.go.id/kfa-v2/products?identifier=kfa&code='.$dk["KODE_KFA"].'';
      $url = 'https://api-satusehat.kemkes.go.id/kfa-v2/products/all?page=1&size=500&product_type=farmasi';
      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);
    //   var_dump($res->items->data);
      $data = $res->items->data;
      $dataKFA = array();
      $index = 0;
      foreach($data as $d){
        // echo '<pre>'.$data[$index]->kfa_code."</pre>";
        // echo '<pre>'.$data[$index]->name."</pre>";
        // echo '<pre>'.$data[$index]->farmalkes_type->code."</pre>";
        // echo '<pre>'.$data[$index]->farmalkes_type->name."</pre>";
        // echo '<pre>'.$data[$index]->farmalkes_type->group."</pre>";
        // echo '<pre>'.$data[$index]->dosage_form->code."</pre>";
        // echo '<pre>'.$data[$index]->dosage_form->name."</pre>";
        // echo '<pre>'.$data[$index]->nama_dagang."</pre>";
        // echo '<pre>'.$data[$index]->manufacturer."</pre>";
        // echo '<pre>'.$data[$index]->registrar."</pre>";
        // echo '<pre>'.$data[$index]->generik."</pre>";
        // echo '<pre>'.$data[$index]->dose_per_unit."</pre><br>";
        array_push($dataKFA, array(
            'KODE_KFA'             => $data[$index]->kfa_code,
            'NAMA_OBAT'            => $data[$index]->name,
            'AKTIF_KFA'            => $data[$index]->active,
            'KODE_FARMALKES'       => $data[$index]->farmalkes_type->code,
            'NAMA_FARMALKES'       => $data[$index]->farmalkes_type->name,
            'GRUP_FARMALKES'       => $data[$index]->farmalkes_type->group,
            'KODE_BENTUK_SEDIAAN'  => $data[$index]->dosage_form->code,
            'NAMA_BENTUK_SEDIAAN'  => $data[$index]->dosage_form->name,
            'NAMA_DAGANG'          => $data[$index]->nama_dagang,
            'MANUFAKTUR'           => $data[$index]->manufacturer,
            'REGISTRAR'            => $data[$index]->registrar,
            'GENERIK'              => $data[$index]->generik,
            'DOSIS_PER_UNIT'       => $data[$index]->dose_per_unit,
        ));
        $index++;
      }
    //   echo '<pre>'.print_r($dataKFA)."</pre><br>";
    //   $this->db->insert_batch('data_ihs.tb_master_kfa', $dataKFA);
    //   echo '<pre>'.print_r($res)."</pre><br>"; 
    //   echo '<pre> Nama Obat: '.$res->result->name."</pre>";
    //   echo '<pre> Kode KFA 93: '.$res->result->kfa_code."</pre>";
    //   echo '<pre> Status Obat: '.$res->result->active."</pre>";
    //   echo '<pre> Tipe Farmalkes Kode: '.$res->result->farmalkes_type->code."</pre>";
    //   echo '<pre> Tipe Farmalkes: '.$res->result->farmalkes_type->name."</pre>";
    //   echo '<pre> Tipe Farmalkes Grup: '.$res->result->farmalkes_type->group."</pre>";
    //   echo '<pre> Satuan Obat: '.$res->result->ucum->name."</pre>";
    //   echo '<pre> Satuan Obat (singkatan): '.$res->result->ucum->cs_code."</pre>";
    //   echo '<pre> Jenis Obat Kode: '.$res->result->dosage_form->code."</pre>";
    //   echo '<pre> Jenis Obat: '.$res->result->dosage_form->name."</pre>";
    //   echo '<pre> Kategori Obat Kode: '.$res->result->controlled_drug->code."</pre>";
    //   echo '<pre> Kategori Obat: '.$res->result->controlled_drug->name."</pre>";
    //   echo '<pre> Rute Pemberian Kode: '.$res->result->rute_pemberian->code."</pre>";
    //   echo '<pre> Rute Pemberian: '.$res->result->rute_pemberian->name."</pre>";
    //   echo '<pre> Produksi Buatan: '.$res->result->produksi_buatan."</pre>";
    //   echo '<pre> Nama Dagang: '.$res->result->nama_dagang."</pre>";
    //   echo '<pre> Manufaktur: '.$res->result->manufacturer."</pre>";
    //   echo '<pre> Registrar: '.$res->result->registrar."</pre>";
    //   echo '<pre> Generik: '.$res->result->generik."</pre>";
    //   echo '<pre> Dosis per Unit: '.$res->result->dose_per_unit."</pre>";
    //   echo '<pre> Kode KFA 94: '.$res->result->packaging_ids[0]->kfa_code."</pre>";
    //   echo '<pre> Packaging: '.$res->result->packaging_ids[0]->name."</pre>";
    //   echo '<pre> Kode KFA 92: '.$res->result->product_template->kfa_code."</pre>";
    //   echo '<pre> Display Name: '.$res->result->product_template->display_name."</pre>";
    //   echo '<pre> Kode KFA 91: '.$res->result->active_ingredients[0]->kfa_code."</pre>";
    //   echo '<pre> Zat Aktif: '.$res->result->active_ingredients[0]->zat_aktif."</pre>";
    //   echo '<pre> Deskripsi: '.$res->result->description."</pre>";
    //   echo '<pre> Indikasi: '.$res->result->indication."</pre>";
    //   echo '<pre> Peringatan: '.$res->result->warning."</pre>";
    //   echo '<pre> Efek Samping: '.$res->result->side_effect."</pre>";
    // }
  }

  function getDetailKFA(){
    $dataKfa = $this->KFAModel->getKFA()->result_array();
    foreach($dataKfa as $dk){
    //   $token = $this->getToken();
      // $token = 'TKV6a39mkCyosA5M5UYDcXx8OMm6';
      // echo '<pre>'.$dk["ACSN"]."</pre>"; 
      // $url = 'https://api-satusehat.kemkes.go.id/kfa-v2/products?identifier=kfa&code='.$dk["KODE_KFA"].'';
      // $url = 'https://api-satusehat.kemkes.go.id/kfa-v2/products?identifier=kfa&code=93000137';
      // $cURL = curl_init();
      // curl_setopt($cURL, CURLOPT_URL,$url);
      // curl_setopt($cURL, CURLOPT_HEADER,false);
      // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      // curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      //     "Content-Type: application/json",
      //     "Authorization: Bearer ".$token." "
      // ));
      // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      // curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
      // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      // $response = curl_exec($cURL);
      // $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      // curl_close($cURL);
      // $res = json_decode($response);

      if($res->result != NULL){

        // echo '<pre> Nama Obat: '.$res->result->name."</pre>";
        // echo '<pre> Kode KFA 93: '.$res->result->kfa_code."</pre>";
        // echo '<pre> Status Obat: '.$res->result->active."</pre>";
        // echo '<pre> Tipe Farmalkes Kode: '.$res->result->farmalkes_type->code."</pre>";
        // echo '<pre> Tipe Farmalkes: '.$res->result->farmalkes_type->name."</pre>";
        // echo '<pre> Tipe Farmalkes Grup: '.$res->result->farmalkes_type->group."</pre>";
        // echo '<pre> Satuan Obat: '.$res->result->ucum->name."</pre>";
        // echo '<pre> Satuan Obat (singkatan): '.$res->result->ucum->cs_code."</pre>";
        // echo '<pre> Jenis Obat Kode: '.$res->result->dosage_form->code."</pre>";
        // echo '<pre> Jenis Obat: '.$res->result->dosage_form->name."</pre>";
        // echo '<pre> Kategori Obat Kode: '.$res->result->controlled_drug->code."</pre>";
        // echo '<pre> Kategori Obat: '.$res->result->controlled_drug->name."</pre>";
        // echo '<pre> Rute Pemberian Kode: '.$res->result->rute_pemberian->code."</pre>";
        // echo '<pre> Rute Pemberian: '.$res->result->rute_pemberian->name."</pre>";
        // echo '<pre> Produksi Buatan: '.$res->result->produksi_buatan."</pre>";
        // echo '<pre> Nama Dagang: '.$res->result->nama_dagang."</pre>";
        // echo '<pre> Manufaktur: '.$res->result->manufacturer."</pre>";
        // echo '<pre> Registrar: '.$res->result->registrar."</pre>";
        // echo '<pre> Generik: '.$res->result->generik."</pre>";
        // echo '<pre> Dosis per Unit: '.$res->result->dose_per_unit."</pre>";
        // echo '<pre> Kode KFA 92: '.$res->result->product_template->kfa_code."</pre>";
        // echo '<pre> Display Name: '.$res->result->product_template->display_name."</pre><br>";
        // echo '<pre> Deskripsi: '.$res->result->description."</pre>";
        // echo '<pre> Indikasi: '.$res->result->indication."</pre>";
        // echo '<pre> Peringatan: '.$res->result->warning."</pre>";
        // echo '<pre> Efek Samping: '.$res->result->side_effect."</pre><br>";

        $dataKFADetail = array(
          'KODE_KFA'                  => $res->result->kfa_code,
          'KODE_KFA_92'               => $res->result->product_template->kfa_code,
          'PRODUCT_TEMPLATE_NAME'     => $res->result->product_template->name,
          'PRODUCT_TEMPLATE_DISPLAY'  => $res->result->product_template->display_name,
          'PRODUCT_TEMPLATE_ACTIVE'   => $res->result->product_template->active,
          'UCUM_CODE'                 => $res->result->ucum->cs_code,
          'UCUM_NAME'                 => $res->result->ucum->name,
          'CONTROLLED_DRUG_CODE'      => $res->result->controlled_drug->code,
          'CONTROLLED_DRUG_NAME'      => $res->result->controlled_drug->name,
          'RUTE_PEMBERIAN_CODE'       => $res->result->rute_pemberian->code,
          'RUTE_PEMBERIAN_NAME'       => $res->result->rute_pemberian->name,
          'PRODUKSI_BUATAN'           => $res->result->produksi_buatan,
          'DESKIRPSI'                 => $res->result->description,
          'INDIKASI'                  => $res->result->indication,
          'PERINGATAN'                => $res->result->warning,
          'EFEK_SAMPING'              => $res->result->side_effect,
        );

        // echo '<pre>'.print_r($dataKFADetail)."</pre><br>";
        // $this->db->insert('data_ihs.tb_master_kfa_detail', $dataKFADetail);

        $index = 0;
        $dataKFAPackaging = array();
        $packaging = $res->result->packaging_ids;
        foreach($packaging as $p){
            // echo '<pre>'.$packaging[$index]->qty."</pre>";
            // echo '<pre>'.$packaging[$index]->name."</pre>";
            // echo '<pre>'.$packaging[$index]->uom_id."</pre>";
            // echo '<pre>'.$packaging[$index]->kfa_code."</pre>";
            // echo '<pre>'.$packaging[$index]->pack_price."</pre><br>";
            array_push($dataKFAPackaging, array(
              'KODE_KFA'     => $res->result->kfa_code,
              'KODE_KFA_94'  => $packaging[$index]->kfa_code,
              'QTY'          => $packaging[$index]->qty,
              'NAME'         => $packaging[$index]->name,
              'UOM_ID'       => $packaging[$index]->uom_id,
              'PACK_PRICE'   => $packaging[$index]->pack_price,
            ));
            $index++;
        }
        // echo '<pre>'.print_r($dataKFAPackaging)."</pre><br>";
        // $this->db->insert_batch('data_ihs.tb_master_kfa_packaging', $dataKFAPackaging);

        $index = 0;
        $dataKFAIngredients = array();
        $ingredients = $res->result->active_ingredients;
        foreach($ingredients as $i){
            // echo '<pre>'.$ingredients[$index]->state."</pre>";
            // echo '<pre>'.$ingredients[$index]->active."</pre>";
            // echo '<pre>'.$ingredients[$index]->kfa_code."</pre>";
            // echo '<pre>'.$ingredients[$index]->zat_aktif."</pre>";
            // echo '<pre>'.$ingredients[$index]->kekuatan_zat_aktif."</pre><br>";
            array_push($dataKFAIngredients, array(
              'KODE_KFA'             => $res->result->kfa_code,
              'KODE_KFA_91'          => $ingredients[$index]->kfa_code,
              'STATE'                => $ingredients[$index]->state,
              'ACTIVE'               => $ingredients[$index]->active,
              'ZAT_AKTIF'            => $ingredients[$index]->zat_aktif,
              'KEKUATAN_ZAT_AKTIF'   => $ingredients[$index]->kekuatan_zat_aktif,
            ));
            $index++;
        }
        // echo '<pre>'.print_r($dataKFAIngredients)."</pre><br>";
        // $this->db->insert_batch('data_ihs.tb_master_kfa_ingredients', $dataKFAIngredients);

        $index = 0;
        $dataKFADosage = array();
        $dosage = $res->result->dosage_usage;
        foreach($dosage as $d){
            //  echo '<pre>'.$dosage[$index]->qty."</pre>";
            //  echo '<pre>'.$dosage[$index]->name."</pre>";
            //  echo '<pre>'.$dosage[$index]->period."</pre>";
            //  echo '<pre>'.$dosage[$index]->qty_uom."</pre>";
            //  echo '<pre>'.$dosage[$index]->category."</pre>";
            //  echo '<pre>'.$dosage[$index]->duration."</pre>";
            //  echo '<pre>'.$dosage[$index]->qty_high."</pre>";
            //  echo '<pre>'.$dosage[$index]->frequency."</pre>";
            //  echo '<pre>'.$dosage[$index]->period_ucum."</pre>";
            //  echo '<pre>'.$dosage[$index]->display_name."</pre>";
            //  echo '<pre>'.$dosage[$index]->duration_max."</pre>";
            //  echo '<pre>'.$dosage[$index]->duration_ucum."</pre>";
            //  echo '<pre>'.$dosage[$index]->frequency_max."</pre><br>";
            array_push($dataKFADosage, array(
              'KODE_KFA'        => $res->result->kfa_code,
              'QTY'             => $dosage[$index]->qty,
              'NAME'            => $dosage[$index]->name,
              'PERIOD'          => $dosage[$index]->period,
              'QTY_HIGH'        => $dosage[$index]->qty_high,
              'QTY_UOM'         => $dosage[$index]->qty_uom,
              'CATEGORY'        => $dosage[$index]->category,
              'DURATION'        => $dosage[$index]->duration,
              'DURATION_MAX'    => $dosage[$index]->duration_max,
              'FREQUENCY'       => $dosage[$index]->frequency,
              'FREQUENCY_MAX'   => $dosage[$index]->frequency_max,
              'PERIOD_UCUM'     => $dosage[$index]->period_ucum,
              'DISPLAY'         => $dosage[$index]->display_name,
            ));
            $index++;
        }
        // echo '<pre>'.print_r($dataKFADosage)."</pre><br>";
        // $this->db->insert_batch('data_ihs.tb_master_kfa_dosage', $dataKFADosage);
      }else{
        // $dataKFA = array (
        //   'STATUS'                       => 127,
        // );

        // $this->db->where('KODE_KFA', $dk['KODE_KFA']);
        // $this->db->update('data_ihs.tb_master_kfa', $dataKFA);
      }
    }
  }
}
