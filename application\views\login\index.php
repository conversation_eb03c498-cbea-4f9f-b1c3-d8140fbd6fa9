<?php
    $this->load->view('layout/head');
?>
    <div class="screen-cover d-none d-xl-none"></div>

    <div class="row">
        <div class="col-12 col-md-12 mt-5">
            <div class="nav">
                <div class="d-flex justify-content-center align-items-center w-100 mb-3 mb-md-0">
                    <div class="d-flex justify-content-center align-items-center w-100">
                        <h2 class="nav-title">Welcome To IHS - Dharmais</h2>
                    </div>
                </div>
            </div>

            <div class="content mt-2">

              <form id="formLogin">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center">
                          <div class="col-md-3">
                            <input type="text" id="username" name="username" class="form-control" placeholder="Username">
                            <input type="password" id="password" name="password" class="form-control mt-2" placeholder="Password">
                          </div>
                        </div>
                    </div>

                    <div class="col-12 mt-3">
                        <div class="d-flex justify-content-center">
                            <button id="login" class="btn btn-primary" type="button">Sign In</button>
                            <button id="cancel" class="btn btn-danger" type="button">Cancel</button>
                        </div>
                    </div>
                </div>
              </form>

            </div>
        </div>
    </div>

    <script src="<?= base_url() ?>assets/js/jquery-3.5.1.js"></script>
    <script>
      $('#username').keydown(function(e) {
          if (e.which == 13) {
          $('#password').focus();
          }
      });

      $('#password').keydown(function(e) {
          if (e.which == 13) {
          $('#login').click();
          }
      });

      $("#login").unbind().click(function() {
          var user = document.getElementById("username").value;
          var form = $("#formLogin").serialize();
          $.ajax("<?= base_url('Login/signin') ?>", {
          dataType: 'json',
          type: 'POST',
          data: form,
          success: function(data) {
              if (data.status == 200) {
                window.location.href = "<?= base_url('IHS') ?>";
              } else {
                window.location.href = "<?= base_url('login') ?>";
              }
          }
          });
      });
    </script>

<?php 
  $this->load->view('layout/footer');
?>