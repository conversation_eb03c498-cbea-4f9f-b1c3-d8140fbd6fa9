<ul class="nav nav-tabs">
  <li class="nav-item">
    <a class="nav-link active btn-primary" href="#belum-mapping" data-toggle="tab" style="color: #FFF !important">Belum dimapping</a>
  </li>
  <li class="nav-item">
    <a class="nav-link btn-warning" href="#sudah-mapping" data-toggle="tab" style="color: #FFF !important">Sudah dimapping</a>
  </li>
  <li class="nav-item">
    <a class="nav-link btn-danger" href="#belum-mapping-tanpa" data-toggle="tab" style="color: #FFF !important">Belum Mapping Tanpa nilai dan satuan</a>
  </li>
</ul>

<div class="tab-content">
  <div class="tab-pane active" id="belum-mapping">
    <table id="datatable-belum-mapping" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>ACTION</th>
                <th>KODE</th>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>NILAI</th>
                <th>SATUAN</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach($parameterLab as $parameterLab): ?>
              <tr>
                <td><button class="btn btn-primary paramlab" data-id="<?= $parameterLab['ID_PARAMETER'] ?>" data-tin="<?= $parameterLab['TINDAKAN_SIMPEL'] ?>" data-param="<?= $parameterLab['PARAMETER'] ?>" data-indeks="<?= $parameterLab['INDEKS'] ?>" data-sat="<?= $parameterLab['SATUAN'] ?>" data-nilai="<?= $parameterLab['NILAI'] ?>">Save</button></td>
                <td><input type="text" class="kode"></td>
                <td><?= $parameterLab['TINDAKAN_SIMPEL'] ?></td>
                <td><?= $parameterLab['PARAMETER'] ?></td>
                <td><?= $parameterLab['NILAI'] ?></td>
                <td><?= $parameterLab['SATUAN'] ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr>
                <th>ACTION</th>
                <th>KODE</th>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>NILAI</th>
                <th>SATUAN</th>
            </tr>
        </tfoot>
    </table>
  </div>
  <div class="tab-pane" id="sudah-mapping">
    <table id="datatable-sudah-mapping" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>ACTION</th>
                <th>KODE</th>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>NILAI</th>
                <th>SATUAN</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach($parameterLabDone as $parameterLabDone): ?>
              <tr>
                <td><button class="btn btn-success paramlab-done" data-id="<?= $parameterLabDone['id'] ?>">Ubah</button></td>
                <td><input type="text" class="kode_loinc_edit" value="<?= $parameterLabDone['kode_loinc'] ?>"></td>
                <td><?= $parameterLabDone['tindakan'] ?></td>
                <td><?= $parameterLabDone['parameter'] ?></td>
                <td><?= $parameterLabDone['nilai'] ?></td>
                <td><?= $parameterLabDone['satuan'] ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr>
                <th>ACTION</th>
                <th>KODE</th>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>NILAI</th>
                <th>SATUAN</th>
            </tr>
        </tfoot>
    </table>
  </div>
  <div class="tab-pane" id="belum-mapping-tanpa">
    <table id="datatable-belum-mapping-tanpa" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>KODE</th>
                <th>ACTION</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach($parameterLabTanpa as $parameterLabTanpa): ?>
              <tr>
                <td><?= $parameterLabTanpa['TINDAKAN_SIMPEL'] ?></td>
                <td><?= $parameterLabTanpa['PARAMETER'] ?></td>
                <td><input type="text" class="kodetanpa"></td>
                <td><button class="btn btn-primary paramlabtanpa" data-id="<?= $parameterLabTanpa['ID_PARAMETER'] ?>" data-tin="<?= $parameterLabTanpa['TINDAKAN_SIMPEL'] ?>" data-param="<?= $parameterLabTanpa['PARAMETER'] ?>" data-indeks="<?= $parameterLabTanpa['INDEKS'] ?>">Save</button></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr>
                <th>TINDAKAN</th>
                <th>PARAMETER</th>
                <th>KODE</th>
                <th>ACTION</th>
            </tr>
        </tfoot>
    </table>
  </div>
</div>

    <script>
      $(document).ready(function () {
          $('#datatable-belum-mapping').DataTable({
            // scrollX: true
          });

          $('#datatable-sudah-mapping').DataTable({
            // scrollX: true
          });

          $('#datatable-belum-mapping-tanpa').DataTable({
            // scrollX: true
          });

          $(document).on('click', '.paramlab', function(){
            id = $(this).attr("data-id");
            param = $(this).attr("data-param");
            tindakan = $(this).attr("data-tin");
            indeks = $(this).attr("data-indeks");
            nilai = $(this).attr("data-nilai");
            satuan = $(this).attr("data-sat");
            kode = $(this).closest("tr").find(".kode").val();

            $.ajax({
              url: '<?= base_url('IHS/saveMappingLoinc') ?>',
              method: 'POST',
              data: {id, param, kode, tindakan, indeks, nilai, satuan},
              success: function(){
                location.reload();
              }
            });
          });

          $(document).on('click', '.paramlabtanpa', function(){
            id = $(this).attr("data-id");
            param = $(this).attr("data-param");
            tindakan = $(this).attr("data-tin");
            indeks = $(this).attr("data-indeks");
            kode = $(this).closest("tr").find(".kodetanpa").val();

            $.ajax({
              url: '<?= base_url('IHS/saveMappingLoincTanpa') ?>',
              method: 'POST',
              data: {id, param, kode, tindakan, indeks},
              success: function(){
                location.reload();
              }
            });
          });

          $(document).on('click', '.paramlab-done', function(){
            id = $(this).attr("data-id");
            kode = $(this).closest("tr").find(".kode_loinc_edit").val();
            
            $.ajax({
              url: '<?= base_url('IHS/editMappingLoinc') ?>',
              method: 'POST',
              data: {id, kode},
              success: function(){
                location.reload();
              }
            });
          });
      });
    </script>