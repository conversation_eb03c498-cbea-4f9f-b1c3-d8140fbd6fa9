<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class YankesModel extends CI_Model {

	function Survey()
	{
		$query ="SELECT
					*,
					dm.ID ID_SURVEY,
					pel.KODE KODE_PELAYANAN
				FROM
					keluhan_pasien.tb_data_masyarakat dm
					LEFT JOIN keluhan_pasien.tb_data_masyarakat_responden dmr ON dmr.ID_DATA_MASYARAKAT = dm.ID AND dmr.STATUS = 1
					LEFT JOIN keluhan_pasien.tb_survey_fasyankes sf ON sf.ID_SURVEY_FK = dm.ID
					LEFT JOIN keluhan_pasien.referensi_FASYANKES pel ON pel.ID_REF = dm.PELAYANAN AND pel.JENIS = 15 AND pel.STATUS = 1
				WHERE dm.ID NOT IN (
							SELECT sf1.ID_SURVEY_FK
							FROM keluhan_pasien.tb_survey_fasyankes sf1)";
		$bind = $this->db->query($query);
		return $bind;
	}

	function <PERSON><PERSON>han()
	{
		$query ="SELECT
					dk.*
				FROM
					keluhan_pasien.tb_daftar_keluhan dk
					LEFT JOIN keluhan_pasien.tb_keluhan_fasyankes kf ON kf.ID_KELUHAN_FK = dk.ID
				WHERE dk.ID NOT IN (
							SELECT kf1.ID_KELUHAN_FK
							FROM keluhan_pasien.tb_keluhan_fasyankes kf1)";
		$bind = $this->db->query($query);
		return $bind;
	}

	function KeluhanAdministrasi($id)
	{
		$query ="SELECT
					* 
				FROM
					keluhan_pasien.tb_keluhan tk
				WHERE tk.ID_DAFTAR_KELUHAN = $id AND tk.JENIS = 4 AND tk.STATUS = 1";
		$bind = $this->db->query($query);
		return $bind;
	}

	function KeluhanPetugasMedis($id)
	{
		$query ="SELECT
					* 
				FROM
					keluhan_pasien.tb_keluhan tk
				WHERE tk.ID_DAFTAR_KELUHAN = $id AND tk.JENIS = 5 AND tk.STATUS = 1";
		$bind = $this->db->query($query);
		return $bind;
	}

	function KeluhanPetugasNonMedis($id)
	{
		$query ="SELECT
					* 
				FROM
					keluhan_pasien.tb_keluhan tk
				WHERE tk.ID_DAFTAR_KELUHAN = $id AND tk.JENIS = 6 AND tk.STATUS = 1";
		$bind = $this->db->query($query);
		return $bind;
	}

	function KeluhanLayanan($id)
	{
		$query ="SELECT
					* 
				FROM
					keluhan_pasien.tb_keluhan tk
				WHERE tk.ID_DAFTAR_KELUHAN = $id AND tk.JENIS = 7 AND tk.STATUS = 1";
		$bind = $this->db->query($query);
		return $bind;
	}

	function KeluhanFasilitas($id)
	{
		$query ="SELECT
					* 
				FROM
					keluhan_pasien.tb_keluhan tk
				WHERE tk.ID_DAFTAR_KELUHAN = $id AND tk.JENIS = 8 AND tk.STATUS = 1";
		$bind = $this->db->query($query);
		return $bind;
	}

	function Respon()
	{
		$query ="SELECT
					dk.*,
					kf.ID_FASYANKES
				FROM
					keluhan_pasien.tb_daftar_keluhan dk
					LEFT JOIN keluhan_pasien.tb_respon_fasyankes rf ON rf.ID_KELUHAN_FK = dk.ID
					LEFT JOIN keluhan_pasien.tb_keluhan_fasyankes kf ON kf.ID_KELUHAN_FK = dk.ID
				WHERE dk.ID NOT IN (
							SELECT rf1.ID_KELUHAN_FK
							FROM keluhan_pasien.tb_respon_fasyankes rf1) AND dk.STATUS = 2";
		$bind = $this->db->query($query);
		return $bind;
	}

}
