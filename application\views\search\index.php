<h1>Search Data Encounter</h1>

<div class="tab-content">
    <form id="formGetListEncounter">
      <label><br>NOMR</label>
      <div class="row col-md-12">
        <input type="text" name="nomr" placeholder="nomr" class="form-control col-md-3" required>
        <button type="submit" style="margin-left: 15px;" class="btn btn-warning">Search</button>
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="resultGetListEncounter"></pre>
</div>
<script>
  $('#formGetListEncounter').submit(function (event) {
    form = $("#formGetListEncounter").serializeArray();
    $.ajax({
      url: '<?= base_url('IHS/getListEncounter') ?>',
      method: 'POST',
      data: form,
      success: function(res){
        $("#resultGetListEncounter").html(res);
      }
    });
    event.preventDefault();
  });
</script>