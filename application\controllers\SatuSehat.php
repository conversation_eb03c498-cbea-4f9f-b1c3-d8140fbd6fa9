<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehat extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    // if($this->session->userdata('logged_in') == FALSE ){
    //   redirect('Login');
    // }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('IHSModel');
    $this->load->model('SatuSehatModel');
  }

  public function getToken()
  {
    $postDataArray = [
      'client_id' => 'fs2BJRXk1OgoRCIkZA6UPsAGdvv2lwPS0TI76WCXWxtyAHz7','client_secret' => '2VeU4CziJdKZxnfHLn7aw6kktOC4EAnVHKVlUIy3ZQetvjAyCfq6czKDjklfdNFk'
    ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    echo $response;exit();
    curl_close($cURL);
    $obj1 = json_decode($response);
    $token = $obj1->access_token;
    $this->session->set_userdata('token', $token);

    $this->index();
  }

  public function index()
  {
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Organization/10000187';

    $headers = array(
      "Accept: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." ",
    );

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $res = json_decode($response);

    $data = array(
      'response' => $res,
      'getPartOf' => $this->IHSModel->getPartOf(),
      'getRuangan' => $this->IHSModel->getRuangan(),
      'parameterLab' => $this->IHSModel->parameterLab(),
      'parameterLabDone' => $this->IHSModel->parameterLabDone(),
      'parameterLabTanpa' => $this->IHSModel->parameterLabTanpa(),
      'getMappingLoinc' => $this->IHSModel->getMappingLoinc()
    );
    
    if(isset($res->active)){
      $this->load->view('dashboard', $data);
    } else {
      $this->getToken();
    }
  }

  public function postEncounter()
  {
    $dataKunjungan = $this->IHSModel->dataKunjungan()->result_array();
    foreach($dataKunjungan as $data1){

      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
      $tglReg = $datereg->format(DATE_ATOM);

      // get ID Location from database
      $getIdLocation = $this->IHSModel->getIdLocation($data1['ID_RUANGAN']);

      $data = '{
        "resourceType": "Encounter",
        "status": "arrived",
        "class": {
            "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
            "code": "AMB",
            "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/100000030009",
          "display": "'.$data1['NAMAPASIEN'].'"
        },
        "participant": [
          {
              "type": [
                  {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                              "code": "ATND",
                              "display": "attender"
                          }
                      ]
                  }
              ],
              "individual": {
                  "reference": "Practitioner/*********",
                  "display": "'.$data1['DPJP'].'"
              }
          }
        ],
        "period": {
            "start": "'.$tglReg.'" ';
      $data .= '
        },
        "location": [
            {
                "location": {
                    "reference": "Location/'.$getIdLocation['ID_IHS_LOCATION'].'",
                    "display": "'.$getIdLocation['KET_IHS_LOCATION'].'"
                }
            }
        ],
        "statusHistory": [
            {
                "status": "arrived",
                "period": {
                    "start": "'.$tglReg.'",
                    "end": "'.$tglReg.'"
                }
            }';
    $data .= '
        ],
        "serviceProvider": {
            "reference": "Organization/10000187"
        },
        "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/encounter/10000187",
                "value": "'.$data1['NOPEN'].'"
            }
        ]
      }';

      // echo $data;

      $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$this->session->userdata('token')." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if(isset($res->fault)){
        $this->getToken();
      } else {
        echo 'Status Code : ' . $httpcode . '<br>';
        echo '<pre>'.$response."</pre>";
  
        // Insert encounter 
        $simpanEncounter = array(
          'encounter' => $res->id,
          'nopen' => $data1['NOPEN'],
        );

        // Insert encounter log
        $simpanLogEncounter = array(
          'log'       => $data,
          'httpcode'       => $httpcode,
          'id_encounter' => $res->id,
          'response'       => $response,
          'jenis'       => 'POST',
        );
        $this->db->insert('ihs.tb_encounter', $simpanEncounter);    
        $this->db->insert('ihs.tb_log_encounter', $simpanLogEncounter);    
        
      }
    }

  }

  public function postCondition()
  {
    $pendaftaran = $this->SatuSehatModel->cekEncounter();
    foreach($pendaftaran as $pendaf){
      $condition = $this->SatuSehatModel->condition($pendaf['NOPEN']);

    foreach($condition->result_array() as $cond){
      $data = '{
           "resourceType": "Condition",
           "clinicalStatus": {
              "coding": [
                 {
                    "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                    "code": "active",
                    "display": "Active"
                 }
              ]
           },
           "category": [
              {
                 "coding": [
                    {
                       "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                       "code": "encounter-diagnosis",
                       "display": "Encounter Diagnosis"
                    }
                 ]
              }
           ],
           "code": {
              "coding": [
                 {
                    "system": "http://hl7.org/fhir/sid/icd-10",
                    "code": "'.$cond['DIAGNOSA'].'",
                    "display": "'.$cond['DIAGNOSA_DESKRIPSI'].'"
                 }
              ]
           },
           "subject": {
              "reference": "Patient/100000030009",
              "display": "' . $cond['NAMA_PASIEN'] . '"
           },
           "encounter": {
              "reference": "Encounter/' . $pendaf['ENCOUNTER'] . '",
              "display": "insert condition"
           }
        }';

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition';
    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo '<pre>'.$response."</pre>";

      $simpanCondition = array(
        'id_encounter'   => $pendaf['ENCOUNTER'],
        'id_condition'   => $res->id,
        'nopen'          => $cond['NOPEN'],
        'status'         => 1,
      );
      $this->db->insert('ihs.tb_condition', $simpanCondition);

      $simpanLog = array(
        'id_encounter'   => $pendaf['ENCOUNTER'],
        'id_condition'   => $res->id,
        'log'            => $data,
        'response'       => $response,
        'http_code'      => $httpcode,
      );
      $this->db->insert('ihs.tb_log_condition', $simpanLog);      
      }
    }
    }
  }

  public function postObservation()
  {
    $cekObservation = $this->SatuSehatModel->cekObservation();


    foreach($cekObservation as $cekObservation) {
      $observation = $this->SatuSehatModel->observation($cekObservation['nopen']);
      // $observation = $this->SatuSehatModel->observation(2208311300);

      if(isset($observation['nadi'])) {
        $datanadi = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8867-4",
                      "display": "Heart rate"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/P00030004"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['encounter'].'",
              "display": "Pemeriksaan Nadi"
          },
          "effectiveDateTime": "'.DATE('Y-m-d', strtotime($observation['created_at'])).'",
          "valueQuantity": {
              "value": '.$observation['nadi'].',
              "unit": "beats/minute",
              "system": "http://unitsofmeasure.org",
              "code": "/min"
          }
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datanadi);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanNadi = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['encounter'],
            'kategori' => "nadi"
          );
          $this->db->insert('ihs.tb_observation', $simpanNadi);
        }
      }

      if(isset($observation['pernapasan'])) {
        $datapernapasan = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "9279-1",
                      "display": "Respiratory rate"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/P00030004"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['encounter'].'",
              "display": "Pemeriksaan Pernapasan"
          },
          "effectiveDateTime": "'.DATE('Y-m-d', strtotime($observation['created_at'])).'",
          "valueQuantity": {
              "value": '.$observation['pernapasan'].',
              "unit": "breaths/minute",
              "system": "http://unitsofmeasure.org",
              "code": "/min"
          }
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datapernapasan);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanPernapasan = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['encounter'],
            'kategori' => "pernapasan"
          );
          $this->db->insert('ihs.tb_observation', $simpanPernapasan);
        }
      }

      if(isset($observation['tekanan_darah'])) {
        $datasistole = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8480-6",
                      "display": "Systolic blood pressure"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/P00030004"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['encounter'].'",
              "display": "Pemeriksaan Sistolik"
          },
          "effectiveDateTime": "'.DATE('Y-m-d', strtotime($observation['created_at'])).'",
          "valueQuantity": {
              "value": '.$observation['tekanan_darah'].',
              "unit": "mm[Hg]",
              "system": "http://unitsofmeasure.org",
              "code": "mm[Hg]"
          }
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasistole);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanSistole = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['encounter'],
            'kategori' => "sistole"
          );
          $this->db->insert('ihs.tb_observation', $simpanSistole);
        }
      }

      if(isset($observation['per_tekanan_darah'])) {
        $datadiastol = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8462-4",
                      "display": "Diastolic blood pressure"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/P00030004"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['encounter'].'",
              "display": "Pemeriksaan Diastolik"
          },
          "effectiveDateTime": "'.DATE('Y-m-d', strtotime($observation['created_at'])).'",
          "valueQuantity": {
              "value": '.$observation['per_tekanan_darah'].',
              "unit": "mm[Hg]",
              "system": "http://unitsofmeasure.org",
              "code": "mm[Hg]"
          }
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datadiastol);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanDiastol = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['encounter'],
            'kategori' => "diastol"
          );
          $this->db->insert('ihs.tb_observation', $simpanDiastol);
        }
      }

      if(isset($observation['suhu'])) {
        $datasuhu = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8310-5",
                      "display": "Body temperature"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/P00030004"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['encounter'].'",
              "display": "Pemeriksaan Suhu Tubuh"
          },
          "effectiveDateTime": "'.DATE('Y-m-d', strtotime($observation['created_at'])).'",
          "valueQuantity": {
              "value": '.$observation['suhu'].',
              "unit": "C",
              "system": "http://unitsofmeasure.org",
              "code": "Cel"
          }
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasuhu);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanSuhu = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['encounter'],
            'kategori' => "suhu"
          );
          $this->db->insert('ihs.tb_observation', $simpanSuhu);
        }
      }


      // echo $cekObservation['nopen'];
      // echo $datanadi;

    

    // echo $obasrvation['tekanan_darah'];
    // echo "<br>";
    // echo $obasrvation['per_tekanan_darah'];
    // echo "<br>";
    // echo $obasrvation['pernapasan'];
    // echo "<br>";
    // echo $obasrvation['nadi'];
    // echo "<br>";
    // echo $obasrvation['suhu'];
    // echo "<br>";
    // echo $obasrvation['tb'];
    // echo "<br>";
    // echo $obasrvation['bb'];
    // echo "<br>";
    }
  }

  public function putEncounter()
  {
    $putEncounter = $this->SatuSehatModel->finishEncounter();
    foreach($putEncounter as $putEnc){
    $status = 1;

      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $putEnc['TGLREG']);
      $tglReg = $datereg->format(DATE_ATOM);

      // Tanggal Reg
      $datakeluar = date_create_from_format('Y-m-d H:i:s', $putEnc['TGLKELUAR']);
      $tglKeluar = $datakeluar->format(DATE_ATOM);

      // get ID Location from database
      $getIdLocation = $this->IHSModel->getIdLocation($putEnc['ID_RUANGAN']);

      $data = '{
        "resourceType": "Encounter",
        "id": "' . $putEnc['ENCOUNTER'] . '",
        "identifier": [
          {
            "system": "http://sys-ids.kemkes.go.id/encounter/10000187",
            "value": "' . $putEnc['NOPEN'] . '"
          }
        ],
        "status": "finished",
        "class": {
          "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
          "code": "AMB",
          "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/100000030009",
          "display": "' . $putEnc['NAMAPASIEN'] . '"
        },
        "participant": [
          {
            "type": [
              {
                "coding": [
                  {
                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                    "code": "ATND",
                    "display": "attender"
                  }
                ]
              }
            ],
            "individual": {
              "reference": "Practitioner/*********",
              "display": "' . $putEnc['DPJP'] . '"
            }
          }
        ],
        "period": {
          "start": "' . $tglReg . '",
          "end": "' . $tglKeluar . '"
        },
        "location": [
          {
            "location": {
              "reference": "Location/'.$getIdLocation['ID_IHS_LOCATION'].'",
              "display": "'.$getIdLocation['KET_IHS_LOCATION'].'"
            }
          }
        ],';

        $condition = $this->SatuSehatModel->condition($putEnc['NOPEN']);
        if($condition->num_rows() > 0){
          $data .='"diagnosis": [';
          $status = 2;
          foreach($condition->result_array() as $con){
            $data .='{
                "condition": {
                  "reference": "Condition/'.$con['ID_CONDITION'].'",
                  "display": "'.$con['DIAGNOSA_DESKRIPSI'].'"
                },
                "use": {
                  "coding": [
                    {
                      "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
                      "code": "DD",
                      "display": "Discharge diagnosis"
                    }
                  ]
                },
                "rank": '.$con['JENIS'].'
              },';
          }
          $data .='],';
        }

        $data .='"statusHistory": [
          {
            "status": "arrived",
            "period": {
              "start": "' . $tglReg . '",
              "end": "' . $tglReg . '"
            }
          },
          {
            "status": "finished",
            "period": {
              "start": "' . $tglKeluar . '",
              "end": "' . $tglKeluar . '"
            }
          }
        ],
        "serviceProvider": {
          "reference":"Organization/10000187"
        }
      }';

      $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc["ENCOUNTER"].'';

    $data = str_replace(',]', ']', $data);

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo '<pre>'.$response."</pre>";

      $ubahCondition = array(
        'status'         => $status,
      );
      $this->db->where('tb_condition.nopen', $putEnc['NOPEN']);
      $this->db->update('ihs.tb_condition', $ubahCondition);

      $simpanLog = array(
        'id_encounter'   => $putEnc['ENCOUNTER'],
        'log'            => $data,
        'response'       => $response,
        'httpcode'      => $httpcode,
        'jenis'          => 'PUT',
      );
      $this->db->insert('ihs.tb_log_encounter', $simpanLog);      
      }
    }
  }

  public function postProcedure()
  {
    $pendaftaran = $this->SatuSehatModel->cekProcedure();
    foreach($pendaftaran as $pendaf){
      $procedure = $this->SatuSehatModel->procedure($pendaf['NOPEN']);

    foreach($procedure->result_array() as $proc){
      $data = '{
        "resourceType": "Procedure",
        "status": "completed",
        "code": {
            "coding": [
                {
                    "system": "http://hl7.org/fhir/sid/icd-9-cm",
                    "code": "'.$proc['KODE'].'",
                    "display": "'.$proc['DESKRIPSI'].'"
                }
            ]
        },
        "subject": {
            "reference": "Patient/P00030004",
            "display": "'.$pendaf['NAMAPASIEN'].'"
        },
        "encounter": {
            "reference": "Encounter/'.$pendaf['ENCOUNTER'].'",
            "display": "Tindakan Rontgen Dada Budi Santoso pada Selasa tanggal 14 Juni 2022"
        }
    }';

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Procedure';
    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo '<pre>'.$response."</pre>";      

      $simpanProcedure = array(
        'id_encounter'   => $pendaf['ENCOUNTER'],
        'id_procedure'   => $res->id,
        'nopen'          => $proc['NOPEN'],
        'status'         => 1,
      );
      $this->db->insert('ihs.tb_procedure', $simpanProcedure);

      $simpanLog = array(
        'id_encounter'   => $pendaf['ENCOUNTER'],
        'id_procedure'   => $res->id,
        'log'            => $data,
        'response'       => $response,
        'http_code'      => $httpcode,
      );
      $this->db->insert('ihs.tb_log_procedure', $simpanLog);
      }
    }
    }
  }

  public function postComposition()
  {
    $cekComposition = $this->SatuSehatModel->cekComposition();


    foreach($cekComposition as $cekComposition) {

      $composition = $this->SatuSehatModel->composition($cekComposition['nopen']);

      foreach($composition as $composition) {
        $data = '{
          "resourceType": "Composition",
          "identifier": {
            "system": "http://sys-ids.kemkes.go.id/composition/10000187",
            "value": "'.$cekComposition['nopen'].'"
          },
          "status": "final",
          "type": {
            "coding": [ 
              {
                "system": "http://loinc.org",
                "code": "18842-5",
                "display": "Discharge summary"
              } 
            ]
          },
          "category": [
            {
              "coding": [
                {
                  "system": "http://loinc.org",
                  "code": "LP173421-1",
                  "display": "Report"
                } 
              ]
            } 
          ],
          "subject": {
            "reference": "Patient/100000030009",
            "display": "'.$composition['NAMA'].'"
          },
          "encounter": {
            "reference": "Encounter/'.$cekComposition['encounter'].'",
            "display": "Kunjungan '.$composition['NAMA'].' pada tanggal '.date('d M Y', strtotime($composition['created_at'])).'"
          },
          "date": "'.date('Y-m-d', strtotime($composition['created_at'])).'",
          "author": [
            {
              "reference": "Practitioner/*********",
              "display": "'.$composition['practitioner'].'"
            } 
          ],
          "title": "Resume Medis Rawat Jalan",
          "custodian": {
            "reference": "Organization/10000187"
          },
          "section": [
            {
              "code": {
                "coding": [
                  {
                    "system": "http://loinc.org",
                    "code": "42344-2",
                    "display": "Discharge diet (narrative)"
                  } 
                ]
              }, 
              "text": {
                "status": "additional",';
        if(isset($composition['jenis_diet'])){
          $data .= '"div": "'.$composition['diet'].', '.$composition['jenis_diet'].'" ';
        } else {
          $data .= '"div": "'.$composition['diet'].'"';
        }
        $data .='
              }
            } 
          ]
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Composition';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpancomposition = array(
            'nopen' => $composition['nopen'],
            'composition' => $res->id,
            'id_encounter' => $cekComposition['encounter']
          );
          $this->db->insert('ihs.tb_composition', $simpancomposition);
        }

        // echo $data;
      }
    
    }
  }

  public function postMedicationRequest()
  {
    $cekMedicationRequest = $this->SatuSehatModel->cekMedicationRequest();
    foreach($cekMedicationRequest as $dataMed) {
      $medicationDokter = $this->SatuSehatModel->medicationDokter($dataMed['NOPEN']);
      foreach($medicationDokter as $medicationDokter) {
        $data = '{
          "resourceType": "Medication",
          "meta": {
              "profile": [
                  "https://fhir.kemkes.go.id/r4/StructureDefinition/Medication"
              ]
          },
          "identifier": [
              {
                  "system": "http://sys-ids.kemkes.go.id/medication/10000187",
                  "use": "official",
                  "value": "' . $medicationDokter['ID_OBAT'] . '"
              }
          ],
          "status": "'.$medicationDokter['KET_STATUS'].'",
          "form": {
              "coding": [
                  {
                      "system": "https://terminology.kemkes.go.id/CodeSystem/medication-form",
                      "code": "'.$medicationDokter['CODE_SEDIAAN_SATU_SEHAT'].'",
                      "display": "'.$medicationDokter['SEDIAAN_SATUSEHAT'].'"
                  }
              ]
          },
          "extension": [
              {
                  "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/MedicationType",
                  "valueCodeableConcept": {
                      "coding": [
                          {
                              "system": "https://terminology.kemkes.go.id/CodeSystem/medication-type",
                              "code": "'.$medicationDokter['KODE_RACIKAN'].'",
                              "display": "'.$medicationDokter['KODE_RACIKAN_KET'].'"
                          }
                      ]
                  }
              }
              ]
        }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Medication';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // echo $data;exit();
        if(isset($res->fault)){
          $this->getToken();
        } else {
          // if(){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>"; 

          $simpanMedication = array(
            'id_encounter' => $dataMed['ENCOUNTER'],
            'id_medication' => $res->id,
            'nopen' => $dataMed['NOPEN'],
            'jenis' => "REQUEST",
            'status' => 1,
          );
          $this->db->insert('ihs.tb_medication', $simpanMedication);

          $simpanLogMedication = array(
            'log'       => $data,
            'http_code'       => $httpcode,
            'id_encounter' => $dataMed['ENCOUNTER'],
            'id_medication' => $res->id,
            'response'       => $response,
            'jenis'       => "POST",
          );
          $this->db->insert('ihs.tb_log_medication', $simpanLogMedication);
          $idMedication = $res->id;

          $data1 = '{
             "resourceType": "MedicationRequest",
             "status": "completed",
             "intent": "order",
             "category": [
                 {
                     "coding": [
                         {
                             "system": "http://terminology.hl7.org/CodeSystem/medicationrequest-category",
                             "code": "outpatient",
                             "display": "Outpatient"
                         }
                     ]
                 }
             ],
             "medicationReference": {
                 "reference": "Medication/'.$res->id.'",
                 "display": "Medication Request Dokter"
             },
             "subject": {
                 "reference": "Patient/100000030009",
                 "display": "'.$medicationDokter['NAMA_PASIEN'].'"
             },
             "encounter": {
                 "reference": "Encounter/'.$dataMed['ENCOUNTER'].'"
             },
             "authoredOn": "'.date_create_from_format('Y-m-d', $medicationDokter['TANGGAL_RESEP']).'",
             "requester": {
                 "reference": "Practitioner/*********",
                 "display": "'.$medicationDokter['CREATED_BY'].'"
             },
             "dosageInstruction": [
                 {
                     "sequence": 1,
                     "timing": {
                         "repeat": {
                             "frequency": 1,
                             "period": 1,
                             "periodUnit": "d"
                         }
                     },
                     "route": {
                         "coding": [
                             {
                                 "system": "http://www.whocc.no/atc",
                                 "code": "'.$medicationDokter['KODE_JALUR_PEMBERIAN'].'",
                                 "display": "'.$medicationDokter['NAMA_JALUR_PEMBERIAN'].'"
                             }
                         ]
                     },
                     "doseAndRate": [
                         {
                             "doseQuantity": {
                                 "value": 4,
                                 "unit": "'.$medicationDokter['NAMA_BENTUK_OBAT'].'",
                                 "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                                 "code": "'.$medicationDokter['NAMA_BENTUK_OBAT'].'"
                             }
                         }
                     ]
                 }
             ],
             "dispenseRequest": {
                 "quantity": {
                     "value": '.$medicationDokter['JUMLAH_OBAT'].',
                     "unit": "'.$medicationDokter['NAMA_BENTUK_OBAT'].'",
                     "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                     "code": "'.$medicationDokter['NAMA_BENTUK_OBAT'].'"
                 }
             }
          }';

          $url1 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/MedicationRequest';

        $cURL1 = curl_init();
        curl_setopt($cURL1, CURLOPT_URL,$url1);
        curl_setopt($cURL1, CURLOPT_HEADER,false);
        curl_setopt($cURL1, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL1, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL1, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL1, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL1, CURLOPT_POSTFIELDS, $data1);
        curl_setopt($cURL1, CURLOPT_CONNECTTIMEOUT,10);
        $response1 = curl_exec($cURL1);
        $httpcode1 = curl_getinfo($cURL1, CURLINFO_HTTP_CODE);
        curl_close($cURL1);
        $res1 = json_decode($response1);
        // echo $data1;exit();

        if(isset($res1->fault)){
          $this->getToken();
        } else {
          echo 'Status Code : ' . $httpcode1 . '<br>';
          echo '<pre>'.$response1."</pre>"; 

          $ubahMedication = array(
            'id_medication_child' => $res1->id,
          );
          $this->db->where('tb_medication.id_medication', $idMedication);
          $this->db->update('ihs.tb_medication', $ubahMedication);

          $ubahLogMedication = array(
            'id_medication_child' => $res1->id,
            'log_child'       => $data1,
            'response_child'       => $response1,
            'jenis'       => "POST",
          );
          $this->db->where('tb_log_medication.id_medication', $idMedication);
          $this->db->update('ihs.tb_log_medication', $ubahLogMedication);
        }

        // }
        }
      }
    }
  }

  public function postServiceRequest()
  {

    $cekServiceRequest = $this->SatuSehatModel->cekServiceRequest();

    foreach($cekServiceRequest as $dataSer) {
      $hasilPasien=0;

      if($dataSer['KTP_PASIEN'] != "" || $dataSer['KTP_PASIEN'] != NULL){
        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.str_replace(' ', '', $dataSer['KTP_PASIEN']).' ';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        curl_close($cURL);
        $res = json_decode($response);
        $hasilPasien = $res->total;
        if($hasilPasien == 1 || $hasilPasien == 2){
          $idIHSPasien = $res->entry[0]->resource->id;
          $namaPasien = $res->entry[0]->resource->name[0]->text;
        }
      }

      if($hasilPasien == 1 || $hasilPasien == 2){
        $serviceRequest = $this->SatuSehatModel->serviceRequest(**********);

        foreach($serviceRequest as $serviceRequest) {
          $data = '{
      "resourceType": "ServiceRequest",
      "status": "active",
      "intent": "original-order",
      "code": {
        "coding": [
          {
          "system": "http://loinc.org",
          "code": "'.$serviceRequest['LOINC'].'",
          "display": "'.$serviceRequest['TINDAKAN'].'"
          }
        ],
        "text": "'.$serviceRequest['TINDAKAN'].'"
      },
      "subject": {
        "reference": "Patient/100000030009"
      },
      "encounter": {
        "reference": "Encounter/'.$dataSer['ENCOUNTER'].'",
        "display": "Insert ServiceRequest"
      }
    }';

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/ServiceRequest';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        
        if(isset($res->fault)){
          $this->getToken();
        } else {
          if($httpcode == "201"){
            echo 'Status Code : ' . $httpcode . '<br>';
            echo '<pre>'.$response."</pre>";

            $simpanServiceRequest = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'SERVICEREQUEST'
            );

            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest);  

            // START SPECIMEN
            $data1 = '{
        "resourceType": "Specimen",
        "type": {
          "coding": [
            {
              "system": "http://snomed.info/sct",
              "code": "45710003",
              "display": "'.$serviceRequest['SPECIMEN_DESK'].'"
            }
          ]
        },
        "subject": {
          "reference": "Patient/100000030009",
          "display": "Budi Santoso"
        },
        "request": [
          {
            "reference": "ServiceRequest/'.$res->id.'"
          }
        ]
      }';

      $url1 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Specimen';

        $cURL1 = curl_init();
        curl_setopt($cURL1, CURLOPT_URL,$url1);
        curl_setopt($cURL1, CURLOPT_HEADER,false);
        curl_setopt($cURL1, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL1, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL1, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL1, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL1, CURLOPT_POSTFIELDS, $data1);
        curl_setopt($cURL1, CURLOPT_CONNECTTIMEOUT,10);
        $response1 = curl_exec($cURL1);
        $httpcode1 = curl_getinfo($cURL1, CURLINFO_HTTP_CODE);
        curl_close($cURL1);
        $res1 = json_decode($response1);
        if(isset($res1->fault)){
          $this->getToken();
        } else {
          if($httpcode1 == "201"){
            echo 'Status Code : ' . $httpcode1 . '<br>';
            echo '<pre>'.$response1."</pre>";

            $simpanServiceRequest1 = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res1->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'SPECIMEN'
            );
            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest1);
          }
          $simpanLog1 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res1->id,
            'log'                  => $data1,
            'response'             => $response1,
            'http_code'            => $httpcode1,
            'jenis'                => 'SPECIMEN'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog1);
        }
            // END SPECIMEN
        
          }
        }
        }

        // START OBSERVATION, DIAGNOSTIC REPORT
        $observationDiagnosticReport = $this->SatuSehatModel->observationDiagnosticReport(**********);
        foreach($observationDiagnosticReport as $observationDiagnosticReport){
          $data2 = '{
          "resourceType": "Observation",
          "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/observation/10000187",
                "value": "'.$dataSer['NOPEN'].'"
            }
          ],
          "status": "final",
          "category": [
            {
              "coding": [
                {
                    "system":"http://terminology.hl7.org/CodeSystem/observation-category",
                    "code": "laboratory",
                    "display": "Laboratory"
                } 
              ]
            }
          ],
          "code": {
            "coding": [
              {
                  "system": "http://loinc.org",
                  "code": "'.$observationDiagnosticReport['LOINC'].'",
                  "display": "'.$observationDiagnosticReport['TINDAKAN'].'"
              }
            ]
          },
          "subject": {
              "reference": "Patient/100000030009"
          },
          "encounter": {
              "reference": "Encounter/'.$dataSer['ENCOUNTER'].'"
          },
          "effectiveDateTime": "2021-07-02",
          "issued": "2022-08-07T15:30:10+01:00",
          "performer": [
              {
                  "reference": "Practitioner/*********"
              }, {
                  "reference": "Organization/10000187"
              }
          ],
          "specimen": {
              "reference": "Specimen/'.$observationDiagnosticReport['ID_IHS_SPECIMEN'].'"
          },
          "basedOn": [
            {
                "reference": "ServiceRequest/'.$observationDiagnosticReport['ID_SERVICE_REQUEST'].'"
            }
          ],
          "valueCodeableConcept": {
              "coding": [
                {
                    "system": "http://snomed.info/sct",
                    "code": "260347006",
                    "display": "'.$observationDiagnosticReport['HASIL'].'"
                } 
              ]
          },
          "referenceRange": [
            {
                "text" : "'.$observationDiagnosticReport['HASIL'].'"
            }
          ]
        }';

        $url2 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL2 = curl_init();
        curl_setopt($cURL2, CURLOPT_URL,$url2);
        curl_setopt($cURL2, CURLOPT_HEADER,false);
        curl_setopt($cURL2, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL2, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL2, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL2, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL2, CURLOPT_POSTFIELDS, $data2);
        curl_setopt($cURL2, CURLOPT_CONNECTTIMEOUT,10);
        $response2 = curl_exec($cURL2);
        $httpcode2 = curl_getinfo($cURL2, CURLINFO_HTTP_CODE);
        curl_close($cURL2);
        $res2 = json_decode($response2);
        // echo $response2; exit();
        if(isset($res2->fault)){
          $this->getToken();
        } else {
          if($httpcode2 == "201"){
            echo 'Status Code : ' . $httpcode2 . '<br>';
            echo '<pre>'.$response2."</pre>";

            $simpanServiceRequest2 = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res2->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'OBSERVATION'
            );
            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest2);
          }
          $simpanLog2 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res2->id,
            'log'                  => $data2,
            'response'             => $response2,
            'http_code'            => $httpcode2,
            'jenis'                => 'OBSERVATION'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog2);
        }

        $data3 = '{
            "resourceType": "DiagnosticReport",
            "identifier": [
                {
                    "system": "http://sys-ids.kemkes.go.id/diagnostic/10000187/lab",
                    "use": "official",
                    "value": "'.$dataSer['NOPEN'].'"
                }
            ],
            "status": "final",
            "category": [
                {
                    "coding": [
                        {
                            "system": "http://terminology.hl7.org/CodeSystem/v2-0074",
                            "code": "MB",
                            "display": "Microbiology"
                        }
                    ]
                }
            ],
            "code": {
                "coding": [
                    {
                        "system": "http://loinc.org",
                        "code": "'.$observationDiagnosticReport['LOINC'].'",
                        "display": "'.$observationDiagnosticReport['TINDAKAN'].'"
                    }
                ]
            },
            "subject": {
                "reference": "Patient/100000030009"
            },
            "encounter": {
                "reference": "Encounter/'.$dataSer['ENCOUNTER'].'"
            },
            "effectiveDateTime": "2012-12-01T12:00:00+01:00",
            "issued": "2012-12-01T12:00:00+01:00",
            "performer": [
                {
                    "reference": "Practitioner/*********"
                },
                {
                    "reference": "Organization/10000187"
                }
            ],
            "result": [
                {
                    "reference": "Observation/'.$res2->id.'"
                }
            ],
            "specimen": [
                {
                    "reference": "Specimen/'.$observationDiagnosticReport['ID_IHS_SPECIMEN'].'"
                }
            ],
            "basedOn": [
                {
                    "reference": "ServiceRequest/'.$observationDiagnosticReport['ID_SERVICE_REQUEST'].'"
                }
            ],
            "conclusion": "Berdasarkan pemeriksaan hasil nya '.$observationDiagnosticReport['HASIL'].'"
          }';
          $url3 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/DiagnosticReport';

          $cURL3 = curl_init();
          curl_setopt($cURL3, CURLOPT_URL,$url3);
          curl_setopt($cURL3, CURLOPT_HEADER,false);
          curl_setopt($cURL3, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL3, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$this->session->userdata('token')." "
          ));
          curl_setopt($cURL3, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL3, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL3, CURLOPT_POSTFIELDS, $data3);
          curl_setopt($cURL3, CURLOPT_CONNECTTIMEOUT,10);
          $response3 = curl_exec($cURL3);
          $httpcode3 = curl_getinfo($cURL3, CURLINFO_HTTP_CODE);
          curl_close($cURL3);
          $res3 = json_decode($response3);

          if(isset($res3->fault)){
            $this->getToken();
          } else {
            if($httpcode3 == "201"){
              echo 'Status Code : ' . $httpcode3 . '<br>';
              echo '<pre>'.$response3."</pre>";

              $simpanServiceRequest3 = array(
                'id_encounter'         => $dataSer['ENCOUNTER'],
                'id_ihs'               => $res3->id,
                'nopen'                => $dataSer['NOPEN'],
                'status'               => 1,
                'jenis'                => 'DIAGNOSTICREPORT'
              );
              $this->db->insert('ihs.tb_service_request', $simpanServiceRequest3);
            }
            $simpanLog3 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res3->id,
            'log'                  => $data3,
            'response'             => $response3,
            'http_code'            => $httpcode3,
            'jenis'                => 'DIAGNOSTICREPORT'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog3);
          }
        }
        // END OBSERVATION, DIAGNOSTIC REPORT
      }

    }
  }

  public function postServiceRequest_old()
  {
    $cekServiceRequest = $this->SatuSehatModel->cekServiceRequest();
    foreach($cekServiceRequest as $dataSer) {
      $hasilPasien=0;

      if($dataSer['KTP_PASIEN'] != "" || $dataSer['KTP_PASIEN'] != NULL){
        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.str_replace(' ', '', $dataSer['KTP_PASIEN']).' ';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        curl_close($cURL);
        $res = json_decode($response);
        $hasilPasien = $res->total;
        if($hasilPasien == 1 || $hasilPasien == 2){
          $idIHSPasien = $res->entry[0]->resource->id;
          $namaPasien = $res->entry[0]->resource->name[0]->text;
        }
      }

      if($hasilPasien == 1 || $hasilPasien == 2){
        // $serviceRequest = $this->SatuSehatModel->serviceRequest($dataSer['NOPEN']);
        $serviceRequest = $this->SatuSehatModel->serviceRequest(**********);
        foreach($serviceRequest as $serviceRequest) {
          $data = '{
      "resourceType": "ServiceRequest",
      "status": "active",
      "intent": "original-order",
      "code": {
        "coding": [
          {
          "system": "http://loinc.org",
          "code": "'.$serviceRequest['LOINC'].'",
          "display": "'.$serviceRequest['TINDAKAN'].'"
          }
        ],
        "text": "'.$serviceRequest['TINDAKAN'].'"
      },
      "subject": {
        "reference": "Patient/'.$idIHSPasien.'"
      },
      "encounter": {
        "reference": "Encounter/'.$dataSer['ENCOUNTER'].'",
        "display": "Insert ServiceRequest"
      }
    }';

        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/ServiceRequest';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        if(isset($res->fault)){
          $this->getToken();
        } else {
          if($httpcode == "201"){
            echo 'Status Code : ' . $httpcode . '<br>';
            echo '<pre>'.$response."</pre>";

            $simpanServiceRequest = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'SERVICEREQUEST'
            );
            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest);  

            // START SPECIMEN
            $data1 = '{
        "resourceType": "Specimen",
        "type": {
          "coding": [
            {
              "system": "http://snomed.info/sct",
              "code": "45710003",
              "display": "'.$serviceRequest['SPECIMEN_DESK'].'"
            }
          ]
        },
        "subject": {
          "reference": "Patient/'.$idIHSPasien.'",
          "display": "'.$namaPasien.'"
        },
        "request": [
          {
            "reference": "ServiceRequest/'.$res->id.'"
          }
        ]
      }';

      $url1 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Specimen';

        $cURL1 = curl_init();
        curl_setopt($cURL1, CURLOPT_URL,$url1);
        curl_setopt($cURL1, CURLOPT_HEADER,false);
        curl_setopt($cURL1, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL1, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL1, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL1, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL1, CURLOPT_POSTFIELDS, $data1);
        curl_setopt($cURL1, CURLOPT_CONNECTTIMEOUT,10);
        $response1 = curl_exec($cURL1);
        $httpcode1 = curl_getinfo($cURL1, CURLINFO_HTTP_CODE);
        curl_close($cURL1);
        $res1 = json_decode($response1);
        if(isset($res1->fault)){
          $this->getToken();
        } else {
          if($httpcode1 == "201"){
            echo 'Status Code : ' . $httpcode1 . '<br>';
            echo '<pre>'.$response1."</pre>";

            $simpanServiceRequest1 = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res1->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'SPECIMEN'
            );
            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest1);
          }
          $simpanLog1 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res1->id,
            'log'                  => $data1,
            'response'             => $response1,
            'http_code'            => $httpcode1,
            'jenis'                => 'SPECIMEN'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog1);
        }
            // END SPECIMEN

        // START OBSERVATION
        $data2 = '{
          "resourceType": "Observation",
          "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/observation/10000187",
                "value": "'.$dataSer['NOPEN'].'"
            }
          ],
          "status": "final",
          "category": [
            {
              "coding": [
                {
                    "system":"http://terminology.hl7.org/CodeSystem/observation-category",
                    "code": "laboratory",
                    "display": "Laboratory"
                } 
              ]
            }
          ],
          "code": {
            "coding": [
              {
                  "system": "http://loinc.org",
                  "code": "11477-7",
                  "display": "'.$serviceRequest['TINDAKAN'].'"
              }
            ]
          },
          "subject": {
              "reference": "Patient/'.$idIHSPasien.'"
          },
          "encounter": {
              "reference": "Encounter/'.$dataSer['ENCOUNTER'].'"
          },
          "effectiveDateTime": "2021-07-02",
          "issued": "2022-08-07T15:30:10+01:00",
          "performer": [
              {
                  "reference": "Practitioner/*********"
              }, {
                  "reference": "Organization/10000187"
              }
          ],
          "specimen": {
              "reference": "Specimen/'.$res1->id.'"
          },
          "basedOn": [
            {
                "reference": "ServiceRequest/'.$res->id.'"
            }
          ],
          "valueCodeableConcept": {
              "coding": [
                {
                    "system": "http://snomed.info/sct",
                    "code": "260347006",
                    "display": "'.$serviceRequest['LIS_HASIL'].'"
                } 
              ]
          },
          "referenceRange": [
            {
                "text" : "'.$serviceRequest['LIS_FLAG'].'"
            }
          ]
        }';

        $url2 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL2 = curl_init();
        curl_setopt($cURL2, CURLOPT_URL,$url2);
        curl_setopt($cURL2, CURLOPT_HEADER,false);
        curl_setopt($cURL2, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL2, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL2, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL2, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL2, CURLOPT_POSTFIELDS, $data2);
        curl_setopt($cURL2, CURLOPT_CONNECTTIMEOUT,10);
        $response2 = curl_exec($cURL2);
        $httpcode2 = curl_getinfo($cURL2, CURLINFO_HTTP_CODE);
        curl_close($cURL2);
        $res2 = json_decode($response2);
        if(isset($res2->fault)){
          $this->getToken();
        } else {
          if($httpcode2 == "201"){
            echo 'Status Code : ' . $httpcode2 . '<br>';
            echo '<pre>'.$response2."</pre>";

            $simpanServiceRequest2 = array(
              'id_encounter'         => $dataSer['ENCOUNTER'],
              'id_ihs'               => $res2->id,
              'nopen'                => $dataSer['NOPEN'],
              'status'               => 1,
              'jenis'                => 'OBSERVATION'
            );
            $this->db->insert('ihs.tb_service_request', $simpanServiceRequest2);
          }
          $simpanLog2 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res2->id,
            'log'                  => $data2,
            'response'             => $response2,
            'http_code'            => $httpcode2,
            'jenis'                => 'OBSERVATION'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog2);
        }
        // END OBSERVATION

        // START DIAGNOSTIC REPORT
        $data3 = '{
            "resourceType": "DiagnosticReport",
            "identifier": [
                {
                    "system": "http://sys-ids.kemkes.go.id/diagnostic/10000187/lab",
                    "use": "official",
                    "value": "'.$dataSer['NOPEN'].'"
                }
            ],
            "status": "final",
            "category": [
                {
                    "coding": [
                        {
                            "system": "http://terminology.hl7.org/CodeSystem/v2-0074",
                            "code": "MB",
                            "display": "Microbiology"
                        }
                    ]
                }
            ],
            "code": {
                "coding": [
                    {
                        "system": "http://loinc.org",
                        "code": "97097-0",
                        "display": "'.$serviceRequest['TINDAKAN'].'"
                    }
                ]
            },
            "subject": {
                "reference": "Patient/'.$idIHSPasien.'"
            },
            "encounter": {
                "reference": "Encounter/'.$dataSer['ENCOUNTER'].'"
            },
            "effectiveDateTime": "2012-12-01T12:00:00+01:00",
            "issued": "2012-12-01T12:00:00+01:00",
            "performer": [
                {
                    "reference": "Practitioner/*********"
                },
                {
                    "reference": "Organization/10000004"
                }
            ],
            "result": [
                {
                    "reference": "Observation/'.$res2->id.'"
                }
            ],
            "specimen": [
                {
                    "reference": "Specimen/'.$res1->id.'"
                }
            ],
            "basedOn": [
                {
                    "reference": "ServiceRequest/'.$res->id.'"
                }
            ],
            "conclusion": "Berdasarkan pemeriksaan hasil nya '.$serviceRequest['LIS_FLAG'].'"
          }';

          $url3 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/DiagnosticReport';

          $cURL3 = curl_init();
          curl_setopt($cURL3, CURLOPT_URL,$url3);
          curl_setopt($cURL3, CURLOPT_HEADER,false);
          curl_setopt($cURL3, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL3, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$this->session->userdata('token')." "
          ));
          curl_setopt($cURL3, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL3, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL3, CURLOPT_POSTFIELDS, $data3);
          curl_setopt($cURL3, CURLOPT_CONNECTTIMEOUT,10);
          $response3 = curl_exec($cURL3);
          $httpcode3 = curl_getinfo($cURL3, CURLINFO_HTTP_CODE);
          curl_close($cURL3);
          $res3 = json_decode($response3);
          if(isset($res3->fault)){
            $this->getToken();
          } else {
            if($httpcode3 == "201"){
              echo 'Status Code : ' . $httpcode3 . '<br>';
              echo '<pre>'.$response3."</pre>";

              $simpanServiceRequest3 = array(
                'id_encounter'         => $dataSer['ENCOUNTER'],
                'id_ihs'               => $res3->id,
                'nopen'                => $dataSer['NOPEN'],
                'status'               => 1,
                'jenis'                => 'DIAGNOSTICREPORT'
              );
              $this->db->insert('ihs.tb_service_request', $simpanServiceRequest3);
            }
            $simpanLog3 = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res3->id,
            'log'                  => $data3,
            'response'             => $response3,
            'http_code'            => $httpcode3,
            'jenis'                => 'DIAGNOSTICREPORT'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog3);
          }
        // END DIAGNOSTIC REPORT

          }
          $simpanLog = array(
            'id_encounter'         => $dataSer['ENCOUNTER'],
            'id_ihs'               => $res->id,
            'log'                  => $data,
            'response'             => $response,
            'http_code'            => $httpcode,
            'jenis'                => 'SERVICEREQUEST'
          );
          $this->db->insert('ihs.tb_log_service_request', $simpanLog);
        }

        }
      }

    }
  }

  public function postMedicationDispense()
  {
    $cekMedicationDispense = $this->SatuSehatModel->cekMedicationDispense();


    foreach($cekMedicationDispense as $cekMedicationDispense) {

      $medicationDispense = $this->SatuSehatModel->medicationDispense($cekMedicationDispense['nopen']);

      foreach($medicationDispense as $medicationDispense) {
      
        $dataMedication = '{
          "resourceType": "Medication",
          "meta": {
              "profile": [
                  "https://fhir.kemkes.go.id/r4/StructureDefinition/Medication"
              ]
          },
          "identifier": [
              {
                  "system": "http://sys-ids.kemkes.go.id/medication/10000187",
                  "use": "official",
                  "value": "'.$medicationDispense['ORDER_ID'].'"
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://sys-ids.kemkes.go.id/kfa",';
                      if($medicationDispense['KFA_CODE3'] != '-'){
                        $dataMedication .='"code": "'.$medicationDispense['KFA_CODE3'].'",';
                      }else{
                        $dataMedication .='"code": "'.$medicationDispense['KFA_CODE2'].'",';
                      }
                      $dataMedication .='"display": "'.$medicationDispense['NAMA_OBAT'].'"
                  }
              ]
          },
          "status": "active",
          "manufacturer": {
              "reference": "Organization/10000187"
          },
          "form": {
              "coding": [
                  {
                      "system": "https://terminology.kemkes.go.id/CodeSystem/medication-form",
                      "code": "'.$medicationDispense['CODE_SEDIAAN_SATU_SEHAT'].'",
                      "display": "'.$medicationDispense['SEDIAAN_SATUSEHAT'].'"
                  }
              ]
          },
          "ingredient": [
              {
                  "itemCodeableConcept": {
                      "coding": [
                          {
                              "system": "http://sys-ids.kemkes.go.id/kfa",
                              "code": "'.$medicationDispense['KFA_CODE1'].'",
                              "display": ""
                          }
                      ]
                  },
                  "isActive": true,
                  "strength": {
                      "numerator": {
                          "value": 150,
                          "system": "http://unitsofmeasure.org",
                          "code": "mg"
                      },
                      "denominator": {
                          "value": 1,
                          "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                          "code": "'.$medicationDispense['NAMA_SATUAN_OBAT'].'"
                      }
                  }
              }
          ],
          "extension": [
              {
                  "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/MedicationType",
                  "valueCodeableConcept": {
                      "coding": [
                          {
                              "system": "https://terminology.kemkes.go.id/CodeSystem/medication-type'.$medicationDispense['RACIKAN'].'",
                              ';
                              if($medicationDispense['RACIKAN'] == 'Non-Racikan') {
        $dataMedication .='
                              "code": "NC",
                              "display": "Non-compound"
                              ';
                            } else {
        $dataMedication .='
                              "code": "C",
                              "display": "compound"
                              ';
                            }
        $dataMedication .='
                          }
                      ]
                  }
              }
          ]
        }';

        // echo print_r($dataMedication);
        // exit();
        $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Medication';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$this->session->userdata('token')." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $dataMedication);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if(isset($res->fault)){
          $this->getToken();
        } else {
          // $this->funcMedicationDispense($res->id, $medicationDispense['NOPEN']);

          echo 'Status Code : ' . $httpcode . '<br>';
          // echo '<pre>'.$response."</pre>"; 

          $simpanMedication = array(
            'id_encounter' => $cekMedicationDispense['id_encounter'],
            'id_medication' => $res->id,
            'nopen' => $cekMedicationDispense['nopen'],
            'jenis' => "DISPENSE",
            'status' => 1,
          );
          $this->db->insert('ihs.tb_medication', $simpanMedication);

          $simpanLogMedication = array(
            'log'       => $dataMedication,
            'http_code'       => $httpcode,
            'id_encounter' => $cekMedicationDispense['id_encounter'],
            'id_medication' => $res->id,
            'response'       => $response,
            'jenis'       => "POST",
          );
          $this->db->insert('ihs.tb_log_medication', $simpanLogMedication);
          $idMedication = $res->id;
          // Dispense
        $dataDispense = '{
            "resourceType": "MedicationDispense",
            "identifier": [
                {
                    "system": "http://sys-ids.kemkes.go.id/prescription/10000187",
                    "use": "official",
                    "value": "'.$medicationDispense['ORDER_ID'].'"
                },
                {
                    "system": "http://sys-ids.kemkes.go.id/prescription-item/10000187",
                    "use": "official",
                    "value": "'.$medicationDispense['ORDER_ID'].'-1"
                }
            ],
            "status": "completed",
            "category": {
                "coding": [
                    {
                        "system": "http://terminology.hl7.org/fhir/CodeSystem/medicationdispense-category",
                        "code": "outpatient",
                        "display": "Outpatient"
                    }
                ]
            },
            "medicationReference": {
                "reference": "Medication/'.$res->id.'",
                "display": "Obat Anti Tuberculosis / Rifampicin 150 mg / Isoniazid 75 mg / Pyrazinamide 400 mg / Ethambutol 275 mg Kaplet Salut Selaput (KIMIA FARMA)"
            },
            "subject": {
                "reference": "Patient/100000030009",
                "display": "Budi Santoso"
            },
            "context": {
                "reference": "Encounter/'.$cekMedicationDispense['id_encounter'].'"
            },
            "performer": [
                {
                    "actor": {
                        "reference": "Practitioner/N10000003",
                        "display": "John Miller"
                    }
                }
            ],
            "location": {
                "reference": "Location/52e135eb-1956-4871-ba13-e833e662484d",
                "display": "Apotek RSUD Jati Asih"
            },
            "authorizingPrescription": [
                {
                    "reference": "MedicationRequest/957a0dbc-6821-4675-9a1f-9c024907a609"
                }
            ],
            "quantity": {
                "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                "code": "'.$medicationDispense['NAMA_SATUAN_OBAT'].'",
                "value": 120
            },
            "daysSupply": {
                "value": 30,
                "unit": "Day",
                "system": "http://unitsofmeasure.org",
                "code": "d"
            },
            "whenPrepared": "'.date('Y-m-d', strtotime($medicationDispense['CREATED_AT'])).'T'.date('H:i:s', strtotime($medicationDispense['CREATED_AT'])).'Z",
            "whenHandedOver": "'.date('Y-m-d', strtotime($medicationDispense['CREATED_AT'])).'T'.date('H:i:s', strtotime($medicationDispense['CREATED_AT'])).'Z",
            "dosageInstruction": [
                {
                    "sequence": 1,
                    "text": "Diminum 4 tablet sekali dalam sehari",
                    "timing": {
                        "repeat": {
                            "frequency": 1,
                            "period": 1,
                            "periodUnit": "d"
                        }
                    },
                    "doseAndRate": [
                        {
                            "type": {
                                "coding": [
                                    {
                                        "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
                                        "code": "ordered",
                                        "display": "Ordered"
                                    }
                                ]
                            },
                            "doseQuantity": {
                                "value": 4,
                                "unit": "'.$medicationDispense['NAMA_SATUAN_OBAT'].'",
                                "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                                "code": "'.$medicationDispense['NAMA_SATUAN_OBAT'].'"
                            }
                        }
                    ]
                }
            ]
          }';

          $url2 = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/MedicationDispense';

          $cURL2 = curl_init();
          curl_setopt($cURL2, CURLOPT_URL,$url2);
          curl_setopt($cURL2, CURLOPT_HEADER,false);
          curl_setopt($cURL2, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL2, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$this->session->userdata('token')." "
          ));
          curl_setopt($cURL2, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL2, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL2, CURLOPT_POSTFIELDS, $dataDispense);
          curl_setopt($cURL2, CURLOPT_CONNECTTIMEOUT,10);
          $response2 = curl_exec($cURL2);
          $httpcode2 = curl_getinfo($cURL2, CURLINFO_HTTP_CODE);
          curl_close($cURL2);
          $res2 = json_decode($response2);

          if(isset($res2->fault)){
            $this->getToken();
          } else {
            echo 'Status Code : ' . $httpcode2 . '<br>';
            echo '<pre>'.$response2."</pre>"; 
  
            $ubahMedication = array(
              'id_medication_child' => $res2->id,
            );
            $this->db->where('tb_medication.id_medication', $idMedication);
            $this->db->update('ihs.tb_medication', $ubahMedication);
  
            $ubahLogMedication = array(
              'id_medication_child' => $res2->id,
              'log_child'       => $dataDispense,
              'response_child'       => $response2,
              'jenis'       => "POST",
            );
            $this->db->where('tb_log_medication.id_medication', $idMedication);
            $this->db->update('ihs.tb_log_medication', $ubahLogMedication);
          }
        }
      }
    }
  }

  // public function funcMedicationDispense($idihs, $nopen)
  // {
  //   $medicationDispense2 = $this->SatuSehatModel->medicationDispense2($nopen);
    
  //   foreach($medicationDispense2 as $medicationDispens2) {
  //     $dataDispense = '{
  //       "resourceType": "MedicationDispense",
  //       "identifier": [
  //           {
  //               "system": "http://sys-ids.kemkes.go.id/prescription/10000187",
  //               "use": "official",
  //               "value": "'.$medicationDispens2['ORDER_ID'].'"
  //           },
  //           {
  //               "system": "http://sys-ids.kemkes.go.id/prescription-item/10000187",
  //               "use": "official",
  //               "value": "'.$medicationDispens2['ORDER_ID'].'-1"
  //           }
  //       ],
  //       "status": "completed",
  //       "category": {
  //           "coding": [
  //               {
  //                   "system": "http://terminology.hl7.org/fhir/CodeSystem/medicationdispense-category",
  //                   "code": "outpatient",
  //                   "display": "Outpatient"
  //               }
  //           ]
  //       },
  //       "medicationReference": {
  //           "reference": "Medication/'.$$idihs.'",
  //           "display": "Obat Anti Tuberculosis / Rifampicin 150 mg / Isoniazid 75 mg / Pyrazinamide 400 mg / Ethambutol 275 mg Kaplet Salut Selaput (KIMIA FARMA)"
  //       },
  //       "subject": {
  //           "reference": "Patient/100000030009",
  //           "display": "Budi Santoso"
  //       },
  //       "context": {
  //           "reference": "Encounter/2823ed1d-3e3e-434e-9a5b-9c579d192787"
  //       },
  //       "performer": [
  //           {
  //               "actor": {
  //                   "reference": "Practitioner/N10000003",
  //                   "display": "John Miller"
  //               }
  //           }
  //       ],
  //       "location": {
  //           "reference": "Location/52e135eb-1956-4871-ba13-e833e662484d",
  //           "display": "Apotek RSUD Jati Asih"
  //       },
  //       "authorizingPrescription": [
  //           {
  //               "reference": "MedicationRequest/957a0dbc-6821-4675-9a1f-9c024907a609"
  //           }
  //       ],
  //       "quantity": {
  //           "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
  //           "code": "TAB",
  //           "value": 120
  //       },
  //       "daysSupply": {
  //           "value": 30,
  //           "unit": "Day",
  //           "system": "http://unitsofmeasure.org",
  //           "code": "d"
  //       },
  //       "whenPrepared": "2022-01-15T10:20:00Z",
  //       "whenHandedOver": "2022-01-15T16:20:00Z",
  //       "dosageInstruction": [
  //           {
  //               "sequence": 1,
  //               "text": "Diminum 4 tablet sekali dalam sehari",
  //               "timing": {
  //                   "repeat": {
  //                       "frequency": 1,
  //                       "period": 1,
  //                       "periodUnit": "d"
  //                   }
  //               },
  //               "doseAndRate": [
  //                   {
  //                       "type": {
  //                           "coding": [
  //                               {
  //                                   "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
  //                                   "code": "ordered",
  //                                   "display": "Ordered"
  //                               }
  //                           ]
  //                       },
  //                       "doseQuantity": {
  //                           "value": 4,
  //                           "unit": "TAB",
  //                           "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
  //                           "code": "TAB"
  //                       }
  //                   }
  //               ]
  //           }
  //       ]
  //     }';
  //   }
  // }
}
