<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatData extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("Asia/Bangkok");
  }
//----------------------------------------------------------------------------------RAJAL----------------------------------------------------------------------------------------------
    public function storeServiceRequestLabRAJAL()
    {
        $query = $this->db->query("CALL data_ihs.ServiceRequestLabRajal()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
    public function storeSpecimenLabRAJAL()
    {
        $query = $this->db->query("CALL data_ihs.SpecimenLabRajal()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
    public function storeServiceRequestRadRAJAL()
    {
        $query = $this->db->query("CALL data_ihs.ServiceRequestRadRajal()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
    public function storeObservationRadiologi()
    {
        $query = $this->db->query("CALL data_ihs.ObservationRadiologi()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
    public function storeDiagnosticReportRadiologi()
    {
        $query = $this->db->query("CALL data_ihs.DiagnosticReportRadiologi()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
//------------------------------------------------------------------------------------IGD----------------------------------------------------------------------------------------------
    public function storeEncounterIGD()
    {
        $query = $this->db->query("CALL data_ihs.EncounterIGD_New()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

    public function storeRuangTriaseIGD()
    {
        $query = $this->db->query("CALL data_ihs.RuangTriaseIGD()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

    public function storeTriaseTransportIGD()
    {
        $query = $this->db->query("CALL data_ihs.TriaseTransportIGD()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

    public function storeTriaseRujukanIGD()
    {
        $query = $this->db->query("CALL data_ihs.TriaseRujukanIGD()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

//----------------------------------------------------------------------RANAP---------------------------------------------------------------------------------------------------------
    public function storeServiceRequestRANAP()
    {
        $query = $this->db->query("CALL data_ihs.ServiceRequestMasukRanap()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

    public function storeEncounterRANAP()
    {
        $query = $this->db->query("CALL data_ihs.EncounterRANAP_New()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }

    public function storeCarePlanRANAP()
    {
        $query = $this->db->query("CALL data_ihs.CarePlanRANAP()");
        $res = $query->result_array();
        $query->next_result(); 
        $query->free_result(); 
        return $res;
    }
//------------------------------------------------------------------------------------REGKAN------------------------------------------------------------------------------------------
}
