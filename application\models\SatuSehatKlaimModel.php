<?php
defined('BASEPATH') or exit('No direct script access allowed');

class SatuSehatKlaimModel extends CI_Model
{

    function __construct(){
		parent::__construct();
        $this->load->database();
	}


    public function insertToken($data)
    {
        $this->db->insert('log.token_satset', $data);
    }

    public function dataKepesertaan()
	{
		$query = "SELECT * FROM data_ihs.data_kepesertaan ";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}
}