<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Api extends CI_Controller {
   public function __construct()
   {
      parent::__construct();
      date_default_timezone_set("Asia/Bangkok");
      $this->load->model('IHSModel');
   }

   public function index()
   {
      $response = '{"fault":{"faultstring":"Invalid access"}}';

      header('Content-Type: application/json');
      header('HTTP/1.1 404 Not Found');
      echo $response;
   }

   public function encounter()
   {
      $requestMethod = $_SERVER["REQUEST_METHOD"];
      $user = $_SERVER['PHP_AUTH_USER'];
      $pass = $_SERVER['PHP_AUTH_PW'];

      if (strtoupper($requestMethod) == 'GET') {
         if($user == 'dharmais' && $pass == 'P@ssw0rdDh4rma1s') {

            $nik = $this->input->get('nik');
            $name = $this->input->get('name');
            $birthdate = $this->input->get('birthdate');
            $birthplace = $this->input->get('birthplace');

            if(!empty($_GET['nik'])) {
               if(strlen($nik) == 16) {
                  $dataEncounter = $this->IHSModel->dataKunjunganV2($nik)->result_array();

                  if(!isset($dataEncounter)){
                     $error_nik_notfound = array(
                        "nik" => $_GET['nik'],
                        "is_exist" => "false",
                        "message" => "NIK tidak ditemukan"
                     );      
                     header('Content-Type: application/json');
                     header('HTTP/1.1 200 OK');
                     $response = json_encode($error_nik_notfound);
                  } else {
                     $response='[';
                     foreach($dataEncounter as $data1){
                     $getToken = $this->guidv4();

                     // Tanggal Reg
                     $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
                     $tglReg = $date->format(DATE_ATOM);

                     // Tanggal Keluar
                     $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLKELUAR']);
                     $tglKeluar = $date->format(DATE_ATOM);

                     // get ID Location from database
                     $getIdLocation = $this->IHSModel->getIdLocation($data1['ID_RUANGAN']);

                     $response .= '{
                        "resourceType": "Encounter",
                        "status": "finished",
                        "class": {
                           "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
                           "code": "AMB",
                           "display": "ambulatory"
                        },
                        "subject": {
                           "reference": "Patient/'.$data1['KTP_PASIEN'].'",
                           "display": "'.$data1['NAMAPASIEN'].'"
                        },
                        "participant": [
                           {
                              "type": [
                                 {
                                    "coding": [
                                       {
                                          "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                                          "code": "ATND",
                                          "display": "attender"
                                       }
                                    ]
                                 }
                              ],
                              "individual": {
                                 "reference": "Practitioner/'.$data1['KTP_DOKTER'].'"
                              }
                           }
                        ],
                        "period": {
                           "start": "'.$tglReg.'",
                           "end": "'.$tglKeluar.'"
                        },
                        "location": [
                           {
                              "location": {
                                 "reference": "Location/'.$getIdLocation['ID_IHS_LOCATION'].'",
                                 "display": "'.$getIdLocation['KET_IHS_LOCATION'].'"
                              }
                           }
                        ],
                        "id" : "'.$getToken.'",
                        "statusHistory": [
                        {
                              "period": {
                                    "end": "'.$tglKeluar.'",
                                    "start": "'.$tglReg.'"
                              },
                              "status": "arrived"
                           },
                           {
                              "period": {
                                    "end": "'.$tglKeluar.'",
                                    "start": "'.$tglKeluar.'"
                              },
                              "status": "finished"
                           }
                     
                     ]
                     },';

                     $simpanGiveApi = array(
                     'uuid'       => $getToken,
                     'nopen'       => $data1['NOPEN']
                     );
                     $this->db->insert('ihs.tb_give_api', $simpanGiveApi);

                     $dataCondition = $this->IHSModel->dataTindakanV2($data1['NOPEN'])->result_array();
                     foreach($dataCondition as $data2){
                     $response .='{
                        "resourceType": "Condition",
                        "clinicalStatus": {
                           "coding": [
                                 {
                                 "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                                 "code": "active",
                                 "display": "Active"
                                 }
                           ]
                           },
                           "category": [
                           {
                                 "coding": [
                                 {
                                       "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                                       "code": "encounter-diagnosis",
                                       "display": "Encounter Diagnosis"
                                 }
                                 ]
                           }
                           ],
                           "code": {
                           "coding": [
                                 {
                                 "system": "http://hl7.org/fhir/sid/icd-10",
                                 "code": "'.$data2['DIAGNOSA'].'", 
                                 "display": "'.$data2['DIAGNOSA_DESKRIPSI'].'"
                                 }
                           ]
                           },
                           "subject": {
                           "reference": "Patient/'.$data1['KTP_PASIEN'].'", 
                           "display": "'.$data1['NAMAPASIEN'].'"
                           },
                           "encounter": {
                           "reference": "Encounter/'.$getToken.'" 
                           }
                        },';
                     }
                     }

                     $response .=']';

                     header('Content-Type: application/json');
                     header('HTTP/1.1 200 OK');
                  }

                  // echo $response;
                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
                  echo str_replace(',]', ']', $response);

                  $simpanLogApiEncounter = array(
                     'nopen'        => $data1['NOPEN'],
                     'jenis'        => 2,
                     'log'          => $response
                  );
                  $this->db->insert('ihs.tb_log_api', $simpanLogApiEncounter);
               } else {
                  $error_patient_notfound = array(
                     "is_exist" => "false",
                     "message" => "It's not a NIK"
                     );      
                     header('Content-Type: application/json');
                     header('HTTP/1.1 404 Not Found');
                     $response = json_encode($error_patient_notfound);
   
                  // echo $response;
                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
                  echo str_replace(',]', ']', $response);
               }

            } else {
               if(!empty($_GET['name']) && !empty($_GET['birthdate']) && !empty($_GET['birthplace']))
               {
               $dataPasienAlternatif = $this->IHSModel->dataPasienAlternatif($name,$birthdate,$birthplace);

               // echo "<pre>".print_r($dataPasienAlternatif)."</pre>";
               if(!isset($dataPasienAlternatif)) {
                  $error_patient_notfound = array(
                     "name" => $_GET['name'],
                     "birthdate" => $_GET['birthdate'],
                     "birthplace" => $_GET['birthplace'],
                     "is_exist" => "false",
                     "message" => "nama tidak ditemukan"
                  );      
                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
                  $response = json_encode($error_patient_notfound);
               } else {
                  $response='{
                     "resourceType": "Patient",
                     "meta": {
                        "profile": [
                           "https://fhir.kemkes.go.id/r4/StructureDefinition/Patient"
                        ]
                     },
                     "identifier": [
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/nik",
                           "value": "'. $nik .'"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/nik-ibu",
                           "value": "9999999999999999"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/paspor",
                           "value": "A09999999"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/kk",
                           "value": "989898989898989"
                        }
                     ],
                     "active": true,
                     "name": [
                        {
                           "use": "official",
                           "text": "'. $dataPasienAlternatif['NAMA'] .'"
                        }
                     ],
                     "telecom": [
                        {
                           "system": "phone",
                           "value": "'. $dataPasienAlternatif['TELEPON_SELULER'] .'",
                           "use": "mobile"
                        },
                        {
                           "system": "phone",
                           "value": "'. $dataPasienAlternatif['TELEPON_RUMAH'] .'",
                           "use": "home"
                        },
                        {
                           "system": "email",
                           "value": "'. $dataPasienAlternatif['EMAIL'] .'",
                           "use": "home"
                        }
                     ],
                     "gender": "'. $dataPasienAlternatif['JENIS_KELAMIN_ING'] .'",
                     "birthDate": "'. $dataPasienAlternatif['TANGGAL_LAHIR'] .'",
                     "deceasedBoolean": false,
                     "address": [
                        {
                           "use": "home",
                           "line": [
                              "'. $dataPasienAlternatif['ALAMAT'] .'"
                           ],
                           "city": "'. $dataPasienAlternatif['KOTA'] .'",
                           "postalCode": "'. $dataPasienAlternatif['KODEPOS'] .'",
                           "country": "'. $dataPasienAlternatif['KEWARGANEGARAAN'] .'",
                           "extension": [
                              {
                                 "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/administrativeCode",
                                 "extension": [
                                    {
                                       "url": "province",
                                       "valueCode": "'. $dataPasienAlternatif['KODE_WILAYAH'] .'"
                                    },
                                    {
                                       "url": "city",
                                       "valueCode": "'. $dataPasienAlternatif['KODE_KOTA'] .'"
                                    },
                                    {
                                       "url": "district",
                                       "valueCode": "'. $dataPasienAlternatif['KODE_KECAMATAN'] .'"
                                    },
                                    {
                                       "url": "village",
                                       "valueCode": "'. $dataPasienAlternatif['KODE_DESA'] .'"
                                    },
                                    {
                                       "url": "rt",
                                       "valueCode": "'. $dataPasienAlternatif['RT'] .'"
                                    },
                                    {
                                       "url": "rw",
                                       "valueCode": "'. $dataPasienAlternatif['RW'] .'"
                                    }
                                 ]
                              }
                           ]
                        }
                     ],
                     "maritalStatus": {
                        "coding": [
                           {
                              "system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus",
                              "code": "'. $dataPasienAlternatif['STATUS_KAWIN_KODE'] .'",
                              "display": "'. $dataPasienAlternatif['STATUS_KAWIN_VAR'] .'"
                           }
                        ],
                        "text": "'. $dataPasienAlternatif['STATUS_KAWIN_VAR'] .'"
                     },
                     "multipleBirthBoolean": false,
                     "contact": [
                        {
                           "relationship": [
                              {
                                 "coding": [
                                    {
                                       "system": "http://terminology.hl7.org/CodeSystem/v2-0131",
                                       "code": "C"
                                    }
                                 ]
                              }
                           ],
                           "name": {
                              "use": "official",
                              "text": "'. $dataPasienAlternatif['NAMA_KELUARGA'] .'"
                           },
                           "telecom": [
                              {
                                 "system": "phone",
                                 "value": "'. $dataPasienAlternatif['NO_HP_KELUARGA'] .'",
                                 "use": "mobile"
                              }
                           ]
                        }
                     ],
                     "communication": [
                        {
                           "language": {
                              "coding": [
                                 {
                                    "system": "urn:ietf:bcp:47",
                                    "code": "id-ID",
                                    "display": "'. $dataPasienAlternatif['BAHASA_SEHARIHARI'] .'"
                                 }
                              ],
                              "text": "'. $dataPasienAlternatif['BAHASA_SEHARIHARI'] .'"
                           },
                           "preferred": true
                        }
                     ],
                     "extension": [
                        {
                           "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/birthPlace",
                           "valueAddress": {
                              "city": "'. $dataPasienAlternatif['TEMPAT_LAHIR'] .'",
                              "country": "ID"
                           }
                        },
                        {
                           "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/citizenshipStatus",
                           "valueCode": "I"
                        }
                     ]
                  }';
                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
               }
               } else {
               $error_patient_notfound = array(
                  "is_exist" => "false",
                  "message" => "gagal"
               );      
               header('Content-Type: application/json');
               header('HTTP/1.1 404 Not Found');
               $response = json_encode($error_patient_notfound);
               }

               // echo $response;
               header('Content-Type: application/json');
                header('HTTP/1.1 200 OK');
               echo str_replace(',]', ']', $response);
            }
         } else {
            $response=array(
               'status' => 0,
               'message' =>'failed'
            );
            header('Content-Type: application/json');
            header('WWW-Authenticate: Basic realm="My Realm"');
            header('HTTP/1.1 403 OK');
            echo json_encode($response);
         }
      } else {
         $response=array(
            'status' => 0,
            'message' =>'failed'
         );
         header('Content-Type: application/json');
         header('WWW-Authenticate: Basic realm="My Realm"');
         header('HTTP/1.1 403 OK');
         echo json_encode($response);
      }
   }

   function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
}

  public function patient()
  {
      $requestMethod = $_SERVER["REQUEST_METHOD"];
      $user = $_SERVER['PHP_AUTH_USER'];
      $pass = $_SERVER['PHP_AUTH_PW'];

      if (strtoupper($requestMethod) == 'GET') {
         if($user == 'dharmais' && $pass == 'P@ssw0rdDh4rma1s') {

            $nik = $this->input->get('nik');
            $name = $this->input->get('name');
            $birthdate = $this->input->get('birthdate');
            $birthplace = $this->input->get('birthplace');
            
         // Check NIK
         if(!empty($_GET['nik'])) {
            if(strlen($nik) == 16) {
               $dataPasien = $this->IHSModel->dataPasien($nik);
               // jika nik tidak ditemukan 
               // Check Query disini
               // End check query

               // echo "</pre>".print_r($dataPasien)."</pre>";
                  if(!isset($dataPasien)){
                  $error_nik_notfound = array(
                     "nik" => $_GET['nik'],
                     "is_exist" => "false",
                     "message" => "NIK tidak ditemukan"
                  );      
                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
                  $response = json_encode($error_nik_notfound);
                  } else {
                  $response='{
                     "resourceType": "Patient",
                     "meta": {
                        "profile": [
                           "https://fhir.kemkes.go.id/r4/StructureDefinition/Patient"
                        ]
                     },
                     "identifier": [
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/nik",
                           "value": "'. $nik .'"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/nik-ibu",
                           "value": "9999999999999999"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/paspor",
                           "value": "A09999999"
                        },
                        {
                           "use": "official",
                           "system": "https://fhir.kemkes.go.id/id/kk",
                           "value": "989898989898989"
                        }
                     ],
                     "active": true,
                     "name": [
                        {
                           "use": "official",
                           "text": "'. $dataPasien['NAMA'] .'"
                        }
                     ],
                     "telecom": [
                        {
                           "system": "phone",
                           "value": "'. $dataPasien['TELEPON_SELULER'] .'",
                           "use": "mobile"
                        },
                        {
                           "system": "phone",
                           "value": "'. $dataPasien['TELEPON_RUMAH'] .'",
                           "use": "home"
                        },
                        {
                           "system": "email",
                           "value": "'. $dataPasien['EMAIL'] .'",
                           "use": "home"
                        }
                     ],
                     "gender": "'. $dataPasien['JENIS_KELAMIN_ING'] .'",
                     "birthDate": "'. $dataPasien['TANGGAL_LAHIR'] .'",
                     "deceasedBoolean": false,
                     "address": [
                        {
                           "use": "home",
                           "line": [
                              "'. $dataPasien['ALAMAT'] .'"
                           ],
                           "city": "'. $dataPasien['KOTA'] .'",
                           "postalCode": "'. $dataPasien['KODEPOS'] .'",
                           "country": "'. $dataPasien['KEWARGANEGARAAN'] .'",
                           "extension": [
                              {
                                 "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/administrativeCode",
                                 "extension": [
                                    {
                                       "url": "province",
                                       "valueCode": "'. $dataPasien['KODE_WILAYAH'] .'"
                                    },
                                    {
                                       "url": "city",
                                       "valueCode": "'. $dataPasien['KODE_KOTA'] .'"
                                    },
                                    {
                                       "url": "district",
                                       "valueCode": "'. $dataPasien['KODE_KECAMATAN'] .'"
                                    },
                                    {
                                       "url": "village",
                                       "valueCode": "'. $dataPasien['KODE_DESA'] .'"
                                    },
                                    {
                                       "url": "rt",
                                       "valueCode": "'. $dataPasien['RT'] .'"
                                    },
                                    {
                                       "url": "rw",
                                       "valueCode": "'. $dataPasien['RW'] .'"
                                    }
                                 ]
                              }
                           ]
                        }
                     ],
                     "maritalStatus": {
                        "coding": [
                           {
                              "system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus",
                              "code": "'. $dataPasien['STATUS_KAWIN_KODE'] .'",
                              "display": "'. $dataPasien['STATUS_KAWIN_VAR'] .'"
                           }
                        ],
                        "text": "'. $dataPasien['STATUS_KAWIN_VAR'] .'"
                     },
                     "multipleBirthBoolean": false,
                     "contact": [
                        {
                           "relationship": [
                              {
                                 "coding": [
                                    {
                                       "system": "http://terminology.hl7.org/CodeSystem/v2-0131",
                                       "code": "C"
                                    }
                                 ]
                              }
                           ],
                           "name": {
                              "use": "official",
                              "text": "'. $dataPasien['NAMA_KELUARGA'] .'"
                           },
                           "telecom": [
                              {
                                 "system": "phone",
                                 "value": "'. $dataPasien['NO_HP_KELUARGA'] .'",
                                 "use": "mobile"
                              }
                           ]
                        }
                     ],
                     "communication": [
                        {
                           "language": {
                              "coding": [
                                 {
                                    "system": "urn:ietf:bcp:47",
                                    "code": "id-ID",
                                    "display": "'. $dataPasien['BAHASA_SEHARIHARI'] .'"
                                 }
                              ],
                              "text": "'. $dataPasien['BAHASA_SEHARIHARI'] .'"
                           },
                           "preferred": true
                        }
                     ],
                     "extension": [
                        {
                           "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/birthPlace",
                           "valueAddress": {
                              "city": "'. $dataPasien['TEMPAT_LAHIR'] .'",
                              "country": "ID"
                           }
                        },
                        {
                           "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/citizenshipStatus",
                           "valueCode": "I"
                        }
                     ]
                  }';

                  header('Content-Type: application/json');
                  header('HTTP/1.1 200 OK');
                  }

               // echo $response;
               header('Content-Type: application/json');
                header('HTTP/1.1 200 OK');
               echo str_replace(',]', ']', $response);

               $simpanLogApiPatient = array(
                  'norm'        => $dataPasien['NORM'],
                  'jenis'        => 1,
                  'log'          => $response
               );
               $this->db->insert('ihs.tb_log_api', $simpanLogApiPatient);
            } else {
               $error_patient_notfound = array(
                  "is_exist" => "false",
                  "message" => "It's not a NIK"
                  );      
                  header('Content-Type: application/json');
                  header('HTTP/1.1 404 Not Found');
                  $response = json_encode($error_patient_notfound);

              header('Content-Type: application/json');
              header('HTTP/1.1 200 OK');
               echo str_replace(',]', ']', $response);
            }

         } else {
            if(!empty($_GET['name']) && !empty($_GET['birthdate']) && !empty($_GET['birthplace']))
            {
               $dataPasienAlternatif = $this->IHSModel->dataPasienAlternatif($name,$birthdate,$birthplace);
               if(!isset($dataPasienAlternatif)) {
               $error_patient_notfound = array(
                  "name" => $_GET['name'],
                  "birthdate" => $_GET['birthdate'],
                  "birthplace" => $_GET['birthplace'],
                  "is_exist" => "false",
                  "message" => "nama tidak ditemukan"
               );      
               header('Content-Type: application/json');
               header('HTTP/1.1 200 OK');
               $response = json_encode($error_patient_notfound);
               } else {
               $response='{
                  "resourceType": "Patient",
                  "meta": {
                     "profile": [
                        "https://fhir.kemkes.go.id/r4/StructureDefinition/Patient"
                     ]
                  },
                  "identifier": [
                     {
                        "use": "official",
                        "system": "https://fhir.kemkes.go.id/id/nik",
                        "value": "'. $nik .'"
                     },
                     {
                        "use": "official",
                        "system": "https://fhir.kemkes.go.id/id/nik-ibu",
                        "value": "9999999999999999"
                     },
                     {
                        "use": "official",
                        "system": "https://fhir.kemkes.go.id/id/paspor",
                        "value": "A09999999"
                     },
                     {
                        "use": "official",
                        "system": "https://fhir.kemkes.go.id/id/kk",
                        "value": "989898989898989"
                     }
                  ],
                  "active": true,
                  "name": [
                     {
                        "use": "official",
                        "text": "'. $dataPasienAlternatif['NAMA'] .'"
                     }
                  ],
                  "telecom": [
                     {
                        "system": "phone",
                        "value": "'. $dataPasienAlternatif['TELEPON_SELULER'] .'",
                        "use": "mobile"
                     },
                     {
                        "system": "phone",
                        "value": "'. $dataPasienAlternatif['TELEPON_RUMAH'] .'",
                        "use": "home"
                     },
                     {
                        "system": "email",
                        "value": "'. $dataPasienAlternatif['EMAIL'] .'",
                        "use": "home"
                     }
                  ],
                  "gender": "'. $dataPasienAlternatif['JENIS_KELAMIN_ING'] .'",
                  "birthDate": "'. $dataPasienAlternatif['TANGGAL_LAHIR'] .'",
                  "deceasedBoolean": false,
                  "address": [
                     {
                        "use": "home",
                        "line": [
                           "'. $dataPasienAlternatif['ALAMAT'] .'"
                        ],
                        "city": "'. $dataPasienAlternatif['KOTA'] .'",
                        "postalCode": "'. $dataPasienAlternatif['KODEPOS'] .'",
                        "country": "'. $dataPasienAlternatif['KEWARGANEGARAAN'] .'",
                        "extension": [
                           {
                              "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/administrativeCode",
                              "extension": [
                                 {
                                    "url": "province",
                                    "valueCode": "'. $dataPasienAlternatif['KODE_WILAYAH'] .'"
                                 },
                                 {
                                    "url": "city",
                                    "valueCode": "'. $dataPasienAlternatif['KODE_KOTA'] .'"
                                 },
                                 {
                                    "url": "district",
                                    "valueCode": "'. $dataPasienAlternatif['KODE_KECAMATAN'] .'"
                                 },
                                 {
                                    "url": "village",
                                    "valueCode": "'. $dataPasienAlternatif['KODE_DESA'] .'"
                                 },
                                 {
                                    "url": "rt",
                                    "valueCode": "'. $dataPasienAlternatif['RT'] .'"
                                 },
                                 {
                                    "url": "rw",
                                    "valueCode": "'. $dataPasienAlternatif['RW'] .'"
                                 }
                              ]
                           }
                        ]
                     }
                  ],
                  "maritalStatus": {
                     "coding": [
                        {
                           "system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus",
                           "code": "'. $dataPasienAlternatif['STATUS_KAWIN_KODE'] .'",
                           "display": "'. $dataPasienAlternatif['STATUS_KAWIN_VAR'] .'"
                        }
                     ],
                     "text": "'. $dataPasienAlternatif['STATUS_KAWIN_VAR'] .'"
                  },
                  "multipleBirthBoolean": false,
                  "contact": [
                     {
                        "relationship": [
                           {
                              "coding": [
                                 {
                                    "system": "http://terminology.hl7.org/CodeSystem/v2-0131",
                                    "code": "C"
                                 }
                              ]
                           }
                        ],
                        "name": {
                           "use": "official",
                           "text": "'. $dataPasienAlternatif['NAMA_KELUARGA'] .'"
                        },
                        "telecom": [
                           {
                              "system": "phone",
                              "value": "'. $dataPasienAlternatif['NO_HP_KELUARGA'] .'",
                              "use": "mobile"
                           }
                        ]
                     }
                  ],
                  "communication": [
                     {
                        "language": {
                           "coding": [
                              {
                                 "system": "urn:ietf:bcp:47",
                                 "code": "id-ID",
                                 "display": "'. $dataPasienAlternatif['BAHASA_SEHARIHARI'] .'"
                              }
                           ],
                           "text": "'. $dataPasienAlternatif['BAHASA_SEHARIHARI'] .'"
                        },
                        "preferred": true
                     }
                  ],
                  "extension": [
                     {
                        "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/birthPlace",
                        "valueAddress": {
                           "city": "'. $dataPasienAlternatif['TEMPAT_LAHIR'] .'",
                           "country": "ID"
                        }
                     },
                     {
                        "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/citizenshipStatus",
                        "valueCode": "I"
                     }
                  ]
               }';
               header('Content-Type: application/json');
               header('HTTP/1.1 200 OK');
               }
            } else {
               $error_patient_notfound = array(
               "is_exist" => "false",
               "message" => "gagal"
               );      
               header('Content-Type: application/json');
               header('HTTP/1.1 404 Not Found');
               $response = json_encode($error_patient_notfound);
            }

            // echo $response;
            header('Content-Type: application/json');
            header('HTTP/1.1 200 OK');
            echo str_replace(',]', ']', $response);
         }
         } else {
         $response=array(
            'status' => 0,
            'message' =>'failed'
         );
         header('Content-Type: application/json');
         header('WWW-Authenticate: Basic realm="My Realm"');
         header('HTTP/1.1 403 OK');
         echo json_encode($response);
         }
      } else {
         $response=array(
         'status' => 0,
         'message' =>'failed'
         );
         header('Content-Type: application/json');
         header('WWW-Authenticate: Basic realm="My Realm"');
         header('HTTP/1.1 403 OK');
         echo json_encode($response);
      }
   }

   public function bundleRme()
   {
    $requestMethod = $_SERVER["REQUEST_METHOD"];
    $user = $_SERVER['PHP_AUTH_USER'];
    $pass = $_SERVER['PHP_AUTH_PW'];

    if (strtoupper($requestMethod) == 'GET') {
         if($user == 'dharmais' && $pass == 'P@ssw0rdDh4rma1s') {
          
      $response = '{
  "resourceType": "Bundle",
  "type": "transaction",
  "entry": [';
     $dataKunjunganV2 = $this->IHSModel->dataKunjunganV2($_GET['nik'])->result_array();
     foreach($dataKunjunganV2 as $data1){

    $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
    $tglReg = $date->format(DATE_ATOM);

    $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLKELUAR']);
    $tglKeluar = $date->format(DATE_ATOM);

      $getTokenEncounter = $this->guidv4();
      $getIdLocation = $this->IHSModel->getIdLocation($data1['ID_RUANGAN']);

      $response .= '{
      "fullUrl": "urn:uuid:'.$getTokenEncounter.'",
      "resource": {
        "resourceType": "Encounter",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/encounter/1000001",
            "use": "official",
            "value": "R001"
          }
        ],
        "status": "finished",
        "class": {
          "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
          "code": "AMB",
          "display": "ambulatory"
        },
        "type": [{
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "11429006", 
                "display": "Consultation"
              }
            ]
          }, {
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "367336001",
                "display": "Chemotherapy"
              }
            ]
          }
        ],
        "priority": {
          "coding": [{
              "system": "http://terminology.hl7.org/ValueSet/v3-ActPriority",
              "code": "R",
              "display": "routine"
            }
          ]
        },
        "subject": {
          "reference": "Patient/'.$data1['KTP_PASIEN'].'",
          "display": "'.$data1['NAMAPASIEN'].'"
        },
        "participant": [{
            "type": [{
                "coding": [{
                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                    "code": "ATND"
                  }
                ]
              }
            ],
            "individual": {
              "reference": "Practitioner/'.$data1['KTP_DOKTER'].'"
            }
          }
        ],
        "period": {
          "start": "'.$tglReg.'",
          "end": "'.$tglKeluar.'"
        },
        "reasonCode": [{
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "4114003",
                "display": "Parenteral chemotherapy for malignant neoplasm"
              }
            ],
            "text": "Oral chemotherapy"
          }
        ],
        "diagnosis": [';
        $dataTindakanV2 = $this->IHSModel->dataTindakanV2($data1['NOPEN'])->result_array();
        foreach($dataTindakanV2 as $data2){
          $getTokenCondition = $this->guidv4();
          $kirimKeluarCondition[] = array(
            'getTokenEncounter' => $getTokenEncounter,
            'getTokenCondition' => $getTokenCondition,
            'getNopen' => $data1['NOPEN'],
            'DIAGNOSA' => $data2['DIAGNOSA'],
            'DIAGNOSA_DESKRIPSI' => $data2['DIAGNOSA_DESKRIPSI'],
            'KTP_PASIEN' => $data1['KTP_PASIEN'],
            'NAMAPASIEN' => $data1['NAMAPASIEN'],
            'KTP_DOKTER' => $data1['KTP_DOKTER'],
            'DPJP' => $data1['DPJP'],
            'tglReg' => $tglReg,
          );
          $response .= '{
            "condition": {
              "reference": "urn:uuid:'.$getTokenCondition.'",
              "display": "'.$data2['DIAGNOSA_DESKRIPSI'].'"
            },
            "use": {
              "coding": [
                {
                  "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
                  "code": "DD",
                  "display": "Discharge diagnosis"
                }
              ]
            },
            "rank": '.$data2['JENIS'].'
          },';
        }

        $response .= '],
        "location": [{
            "location": {
              "reference": "Location/'.$getIdLocation['ID_IHS_LOCATION'].'",
              "display": "'.$getIdLocation['KET_IHS_LOCATION'].'"
            }
          }
        ],
        "hospitalization": {
          "dietPreference": [{
              "coding": [{
                  "system": "http://snomed.info/sct",
                  "code": "276026009",
                  "display": "Fluid balance regulation"
                }
              ]
            }
          ],
          "specialCourtesy": [{
              "coding": [{
                  "system": "http://terminology.hl7.org/CodeSystem/v3-EncounterSpecialCourtesy",
                  "code": "NRM",
                  "display": "normal courtesy"
                }
              ]
            }
          ],
          "specialArrangement": [{
              "coding": [{
                  "system": "http://terminology.hl7.org/CodeSystem/encounter-special-arrangements",
                  "code": "wheel",
                  "display": "Wheelchair"
                }
              ]
            }
          ],
          "dischargeDisposition": {
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "306689006",
                "display": "Discharge to home"
              }
            ]
          }
        },
        "serviceProvider": {
          "reference": "Organization/10000211"
        }
      },
      "request": {
        "method": "POST",
        "url": "Encounter"
      }
    },';

    $getTokenComposition = $this->guidv4();
    $dataLab = $this->IHSModel->dataLab($data1['NOPEN'])->result_array();

    $response .= '{
      "fullUrl": "urn:uuid:d8743f0d-f9b8-43ab-83fb-14ac653a0c48",
      "resource": {
        "resourceType": "Composition",
        "status": "final",
        "type": {
          "coding": [{
              "system": "http://loinc.org",
              "code": "28655-9"
            }
          ],
          "text": "Discharge Summary"
        },
        "subject": {
          "reference": "Patient/P00030004",
          "display": "Roel" 
        },
        "encounter": {
          "reference": "urn:uuid:'.$getTokenComposition.'"
        },
        "date": "2013-02-01T08:00:00+07:00",
        "author": [{
            "reference": "Practitioner/N10000001",
            "display": "Dokter Bronsig"
          }
        ],
        "title": "Discharge Summary",
        "confidentiality": "N",
        "section": [';

    // data lab loinc
    foreach($dataLab as $dataLab) {

      // echo "<pre>".print_r($dataLab['PARAMETER'])."</pre>";
    $response .= '{
                    "title": "Chief complaint",
                    "code": {
                      "coding": [{
                          "system": "http://loinc.org",
                          "code": "",
                          "display": "'.$dataLab['PARAMETER'].'"
                        }
                      ]
                    },
                    "text": {
                      "status": "additional",
                      "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">1. High fever and severe headache\n2. The patient is treated for a tumor</div>"
                    }
                  },';
    }

    $response .= ']
                },
                "request": {
                  "method": "POST",
                  "url": "Composition"
                }
              },';

    $response .= '{
      "fullUrl": "urn:uuid:40b3f72a-8729-11ec-a8a3-0242ac120002",
      "resource": {
        "resourceType": "ServiceRequest",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/procedure/1000001",
            "value": "022101"
          }
        ],
        "status": "completed",
        "intent": "directive",
        "priority": "routine",
        "code": {
          "coding": [{
              "system": "http://loinc.org",
              "code": "58410-2",
              "display": "CBC panel - Blood by Automated count"
            }
          ],
          "text": "Complete Blood Count"
        },
        "subject": {
          "reference": "Patient/P00030004"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd",
          "display": "Roels encounter on January 28th, 2013"
        },
        "occurrenceDateTime": "2022-02-01T08:00:00+07:00",
        "requester": {
          "reference": "Organization/10000211"
        },
        "performer": [{
            "reference": "Organization/10000211"
          }
        ],
        "specimen": [{
            "reference": "urn:uuid:ffb1594c-28f9-419c-a390-614d06163b7c",
            "display": "Whole blood"
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "ServiceRequest"
      }
    },{
      "fullUrl": "urn:uuid:6680b60f-e625-42a5-aa81-d9d9fb395a97",
      "resource": {
        "resourceType": "DiagnosticReport",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/diagnostic/1000001/lab",
            "value": "5234342"
          }
        ],
        "status": "final",
        "category": [{
            "coding": [{
                "system": "http://terminology.hl7.org/CodeSystem/v2-0074",
                "code": "HM",
                "display": "Hematology"
              }
            ]
          }
        ],
        "basedOn": [{
            "reference": "urn:uuid:40b3f72a-8729-11ec-a8a3-0242ac120002"
          }
        ],
        "code": {
          "coding": [{
              "system": "http://loinc.org",
              "code": "58410-2",
              "display": "CBC panel - Blood by Automated count"
            }
          ],
          "text": "Complete Blood Count"
        },
        "subject": {
          "reference": "Patient/P00030004"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd",
          "display": "Roels encounter on January 28th, 2013"
        },
        "effectiveDateTime": "2022-02-01T08:30:00+07:00",
        "issued": "2022-02-01T11:45:33+07:00",
        "performer": [{
            "reference": "Organization/10000211",
            "display": "Vertical District Hospital"
          }, {
            "reference": "Practitioner/N10000002",
            "display": "Dr. dr. Voigt MARS."
          }
        ],
        "resultsInterpreter": [{
            "reference": "Practitioner/N10000002",
            "display": "Dr. dr. Voigt MARS."
          }
        ],
        "result": [{
            "reference": "urn:uuid:686b2fac-93fd-11ea-bb37-2242ac131001",
            "display": "Leukocytes"
          }, {
            "reference": "urn:uuid:686b2fac-93fd-11ea-bb37-2242ac131003",
            "display": "Hemoglobin"
          }
        ],
        "specimen": [{
            "reference": "urn:uuid:ffb1594c-28f9-419c-a390-614d06163b7c"
          }
        ],
        "conclusion": "Example conclusion of the test results, narative ..."
      },
      "request": {
        "method": "POST",
        "url": "DiagnosticReport"
      }
    },{
      "fullUrl": "urn:uuid:686b2fac-93fd-11ea-bb37-2242ac131001",
      "resource": {
        "resourceType": "Observation",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/observation/1000001",
            "value": "111333"
          }
        ],
        "status": "final",
        "code": {
          "coding": [{
              "system": "http://loinc.org",
              "code": "6690-2",
              "display": "Leukocytes [#/volume] in Blood by Automated count"
            }
          ]
        },
        "effectiveDateTime": "2022-02-01T08:30:00+07:00", 
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "performer": [{
            "reference": "Practitioner/N10000002", 
            "display": "Dr. dr. Voigt MARS."
          }
        ],
        "basedOn": [{
            "reference": "urn:uuid:40b3f72a-8729-11ec-a8a3-0242ac120002" 
          }
        ],
        "valueQuantity": {
          "value": 6.6,
          "unit": "10^3/uL",
          "system": "http://unitsofmeasure.org",
          "code": "10*3/uL" 
        },
        "interpretation": [{
            "coding": [{
                "system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
                "code": "N",
                "display": "Normal"
              }
            ]
          }
        ],
        "referenceRange": [{
            "low": {
              "value": 3.4,
              "unit": "10^3/uL",
              "system": "http://unitsofmeasure.org",
              "code": "10*3/uL" 
            },
            "high": {
              "value": 10.8,
              "unit": "10^3/uL", 
              "system": "http://unitsofmeasure.org",
              "code": "10*3/uL" 
            },
            "type": {
              "coding": [{
                  "system": "http://terminology.hl7.org/CodeSystem/referencerange-meaning",
                  "code": "normal",
                  "display": "Normal Range"
                }
              ],
              "text": "Normal Range"
            }
          }
        ],
        "specimen": {
          "reference": "urn:uuid:ffb1594c-28f9-419c-a390-614d06163b7c", 
          "display": "Whole blood"
        }
      },
      "request": {
        "method": "POST",
        "url": "Observation"
      }
    },{
      "fullUrl": "urn:uuid:ffb1594c-28f9-419c-a390-614d06163b7c",
      "resource": {
        "resourceType": "Specimen",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/specimen/1000001",
            "value": "52222"
          }
        ],
        "type": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/v2-0487",
              "code": "BLD",
              "display": "Whole blood"
            }
          ]
        },
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "receivedTime": "2022-02-01T08:25:00+07:00", 
        "collection": {
          "collectedDateTime": "2022-02-01T08:25:00+07:00" 
        }
      },
      "request": {
        "method": "POST",
        "url": "Specimen"
      }
    },{
      "fullUrl": "urn:uuid:255bb67a-8722-42d7-92ed-1997adb28bfd",
      "resource": {
        "resourceType": "Media",
        "identifier": [{
            "use": "official",
            "type": {
              "text": "InstanceUID"
            },
            "system": "urn:dicom:uid",
            "value": "urn:oid:1.2.840.11361907579238403408700.3.1.04.19970327150033"
          }, {
            "type": {
              "text": "accessionNo"
            },
            "system": "http://sys-ids.kemkes.go.id/accessionno/1000001",
            "value": "1234567"
          }, {
            "type": {
              "text": "studyId"
            },
            "system": "urn:dicom:uid",
            "value": "urn:oid:1.2.840.113619.2.21.848.34082.0.538976288.3"
          }, {
            "type": {
              "text": "seriesId"
            },
            "system": "urn:dicom:uid",
            "value": "urn:oid:1.2.840.113619.2.21.3408.700.0.757923840.3.0"
          }
        ],
      "status": "completed",
        "modality": {
          "coding": [{
              "system": "http://dicom.nema.org/resources/ontology/DCM",
              "code": "CT"
            }
          ]
        },
        "view": {
          "coding": [{
              "system": "http://snomed.info/sct",
              "code": "399067008",
              "display": "Lateral projection"
            }
          ]
        },
        "subject": {
          "reference": "Patient/P00030004" 
        },
        "device": {
          "display": "G.E. Medical Systems"
        },
        "height": 480,
        "width": 640,
        "content": {
          "contentType": "application/dicom",
          "url": "http://imaging.acme.com/wado/server?requestType=WADO&contentType=application%2Fdicom&studyUid=1.2.840.113619.2.21.848.34082.0.538976288.3&seriesUid=1.2.840.113619.2.21.3408.700.0.757923840.3.0&objectUid=1.2.840.11361907579238403408700.3.1.04.19970327150033"
        }
      },
      "request": {
        "method": "POST",
        "url": "Media"
      }
    },';

    foreach($kirimKeluarCondition as $data3){
      $response .= '{
      "fullUrl": "urn:uuid:'.$data3['getTokenCondition'].'",
      "resource": {
        "resourceType": "Condition",
        "clinicalStatus": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
              "code": "resolved"
            }
          ]
        },
        "verificationStatus": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/condition-ver-status",
              "code": "confirmed"
            }
          ]
        },
        "category": [{
            "coding": [{
                "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                "code": "encounter-diagnosis"
              }
            ]
          }
        ],
        "severity": {
          "coding": [{
              "system": "http://snomed.info/sct",
              "code": "24484000",
              "display": "Severe"
            }
          ]
        },
        "code": {
          "coding": [{
              "system": "http://hl7.org/fhir/sid/icd-10",
              "code": "'.$data3['DIAGNOSA'].'",
              "display": "'.$data3['DIAGNOSA_DESKRIPSI'].'"
            }
          ]
        },
        "subject": {
          "reference": "Patient/'.$data3['KTP_PASIEN'].'", 
          "display": "'.$data3['NAMAPASIEN'].'"
        },
        "encounter": {
          "reference": "urn:uuid:'.$data3['getTokenEncounter'].'", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "onsetDateTime": "2012-12-01",
        "recordedDate": "2012-12-01",
        "recorder": {
          "reference": "Practitioner/'.$data3['KTP_DOKTER'].'", 
          "display": "'.$data3['DPJP'].'"
        },
        "asserter": {
          "reference": "Practitioner/'.$data3['KTP_DOKTER'].'", 
          "display": "'.$data3['DPJP'].'"
        }
      },
      "request": {
        "method": "POST",
        "url": "Condition"
      }
    },';
    }

    $response .= '{
      "fullUrl": "urn:uuid:0e625729-4487-4bca-b1e9-3a22313e0fbb",
      "resource": {
        "resourceType": "Procedure",
        "status": "completed",
        "category": {
          "coding": [{
              "system": "http://snomed.info/sct",
              "code": "409063005",
              "display": "Counselling"
            }
          ],
          "text": "Consultation"
        },
        "code": {
          "coding": [{
              "system": "http://hl7.org/fhir/sid/icd-9-cm",
              "code": "89.07",
              "display": "Consultation, described as comprehensive"
            }
          ]
        },
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "performedPeriod": {
          "start": "2013-01-28T13:31:00+01:00",
          "end": "2013-01-28T14:27:00+01:00"
        },
        "performer": [{
            "function": {
              "coding": [{
                  "system": "http://snomed.info/sct",
                  "code": "310512001",
                  "display": "Medical oncologist"
                }
              ]
            },
            "actor": {
              "reference": "Practitioner/N10000001", 
              "display": "Dokter Bronsig"
            }
          }
        ],
        "reasonCode": [{
            "coding": [{
                "system": "http://hl7.org/fhir/sid/icd-10",
                "code": "R50.9",
                "display": "Fever, unspecified"
              }
            ],
            "text": "Fever"
          }
        ],
        "note": [{
            "text": "Catatan konsultasi ...."
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "Procedure"
      }
    },{
      "fullUrl": "urn:uuid:5904c5b9-7d0b-4eb6-bec4-42f667b809ae",
      "resource": {
        "resourceType": "MedicationRequest",
        "identifier": [{
            "use": "official",
            "system": "http://sys-ids.kemkes.go.id/prescription/1000001",
            "value": "12345689"
          }
        ],
        "status": "active",
        "intent": "order",
        "medicationReference": {
          "reference": "urn:uuid:d74a56d1-7a74-44e8-b927-f743729d72de", 
          "display": "Myleran 2mg tablet"
        },
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "authoredOn": "2015-01-15", 
        "requester": {
          "reference": "Practitioner/N10000001", 
          "display": "Patrick Pump"
        },
        "reasonCode": [{
            "coding": [{
                "system": "http://hl7.org/fhir/sid/icd-10",
                "code": "C50.1",
                "display": "Malignant neoplasm, central portion of breast"
              }
            ]
          }
        ],
        "dosageInstruction": [{
            "sequence": 1,
            "text": "6 mg PO daily for remission induction; adjust dosage to white blood cell (WBC) count.  With hold treatment if WBC is less than 15,000/ µL; resume when WBC is greater than 50,000/ µL",
            "timing": {
              "repeat": {
                "frequency": 1,
                "period": 1,
                "periodUnit": "d"
              }
            },
            "route": {
              "coding": [{
                  "system": "http://snomed.info/sct",
                  "code": "26643006",
                  "display": "Oral route (qualifier value)"
                }
              ]
            },
            "doseAndRate": [{
                "type": {
                  "coding": [{
                      "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
                      "code": "ordered",
                      "display": "Ordered"
                    }
                  ]
                },
                "doseQuantity": {
                  "value": 6,
                  "unit": "mg",
                  "system": "http://unitsofmeasure.org",
                  "code": "mg" 
                }
              }
            ]
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "MedicationRequest"
      }
    }, {
      "fullUrl": "urn:uuid:d74a56d1-7a74-44e8-b927-f743729d72de",
      "resource": {
        "resourceType": "Medication",
        "code": {
          "coding": [{
              "system": "http://hl7.org/fhir/sid/ndc",
              "code": "76388-713-25",
              "display": "Myleran 2mg tablet, film coated"
            }
          ]
        },
        "form": {
          "coding": [{
              "system": "http://snomed.info/sct",
              "code": "385057009",
              "display": "Film-coated tablet (qualifier value)"
            } 
          ]
        }
      },
      "request": {
        "method": "POST",
        "url": "Medication"
      }
    }, {
      "fullUrl": "urn:uuid:8e278565-0e84-4834-87d1-030a7a497597",
      "resource": {
        "resourceType": "MedicationRequest",
        "identifier": [{
            "use": "official",
            "system": "http://sys-ids.kemkes.go.id/prescription/1000001",
            "value": "12345690"
          }
        ],
        "status": "active",
        "intent": "order",
        "medicationReference": {
          "reference": "urn:uuid:c442c6d9-0d17-4f35-a014-e167478ee8b6", 
          "display": "Ointment (Compound)"
        },
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "authoredOn": "2015-01-15", 
        "requester": {
          "reference": "Practitioner/N10000001", 
          "display": "Patrick Pump"
        },
        "reasonCode": [{
            "coding": [{
                "system": "http://hl7.org/fhir/sid/icd-10",
                "code": "L21.1",
                "display": "Seborrhoeic infantile dermatitis"
              }
            ]
          }
        ],
        "dosageInstruction": [{
            "sequence": 1,
            "text": "Apply twice daily to affected area on left arm",
            "additionalInstruction": [{
                "text": "Apply sparingly"
              }
            ],
            "timing": {
              "repeat": {
                "frequency": 2,
                "period": 1,
                "periodUnit": "d"
              }
            },
            "site": {
              "coding": [{
                  "system": "http://snomed.info/sct",
                  "code": "72098002",
                  "display": "Entire left upper arm (body structure)"
                } 
              ]
            },
            "route": {
              "coding": [{
                  "system": "http://snomed.info/sct",
                  "code": "359540000",
                  "display": "Topical (qualifier value)"
                } 
              ]
            },
            "doseAndRate": [{
                "type": {
                  "coding": [{
                      "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
                      "code": "ordered",
                      "display": "Ordered"
                    }
                  ]
                },
                "doseQuantity": {
                  "value": 1,
                  "unit": "ea",
                  "system": "http://unitsofmeasure.org",
                  "code": "ea" 
                }
              }
            ]
          }
        ],
        "dispenseRequest": {
          "validityPeriod": {
            "start": "2015-01-15",
            "end": "2016-01-15"
          },
          "numberOfRepeatsAllowed": 3,
          "quantity": {
            "value": 30,
            "unit": "g",
            "system": "http://unitsofmeasure.org",
            "code": "g" 
          },
          "expectedSupplyDuration": {
            "value": 10,
            "unit": "days",
            "system": "http://unitsofmeasure.org",
            "code": "d" 
          }
        }
      },
      "request": {
        "method": "POST",
        "url": "MedicationRequest"
      }
    },{
      "fullUrl": "urn:uuid:43bbd031-6dc0-4a32-b5ad-795b8aeab85d",
      "resource": {
        "resourceType": "AllergyIntolerance",
        "clinicalStatus": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical",
              "code": "active",
              "display": "Active"
            }
          ]
        },
        "verificationStatus": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification",
              "code": "unconfirmed",
              "display": "Unconfirmed"
            }
          ]
        },
        "category": [
          "medication"
        ],
        "criticality": "high",
        "code": {
          "coding": [{
              "system": "http://sys-ids.kemkes.go.id/kfa",
              "code": "7980",
              "display": "Penicillin G"
            }
          ]
        },
        "patient": {
          "reference": "Patient/P00030004" 
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "recordedDate": "2010-03-01", 
        "recorder": {
          "reference": "Practitioner/N10000001" 
        },
        "reaction": [{
            "manifestation": [{
                "coding": [{
                    "system": "http://snomed.info/sct",
                    "code": "247472004",
                    "display": "Hives"
                  } 
                ]
              }
            ]
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "AllergyIntolerance"
      }
    },{
      "fullUrl": "urn:uuid:f569fe20-2b94-4021-8938-d94b80a2e25d",
      "resource": {
        "resourceType": "MedicationDispense",
        "status": "completed",
        "medicationReference": {
          "reference": "urn:uuid:34c20ccf-d7db-4088-bafb-006c673e24f7", 
          "display": "Myleran 2mg tablet"
        },
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "context": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "performer": [{
            "actor": {
              "reference": "Practitioner/N10000001" 
            }
          }
        ],
        "authorizingPrescription": [{
            "reference": "urn:uuid:5904c5b9-7d0b-4eb6-bec4-42f667b809ae"
          }
        ],
        "type": {
          "coding": [{
              "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
              "code": "RFP",
              "display": "Refill - Part Fill"
            }
          ]
        },
        "quantity": {
          "value": 90,
          "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
          "code": "TAB"
        }, 
        "daysSupply": {
          "value": 30,
          "unit": "Day",
          "system": "http://unitsofmeasure.org",
          "code": "d" 
        },
        "whenPrepared": "2015-01-15T10:20:00Z", 
        "whenHandedOver": "2015-01-15T16:20:00Z", 
        "dosageInstruction": [{
            "sequence": 1,
            "text": "Take 3 tablets (6mg) once daily",
            "timing": {
              "repeat": {
                "frequency": 1,
                "period": 1,
                "periodUnit": "d"
              }
            },
            "doseAndRate": [{
                "type": {
                  "coding": [{
                      "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
                      "code": "calculated",
                      "display": "Calculated"
                    }
                  ]
                },
                "doseQuantity": {
                  "value": 6,
                  "unit": "mg",
                  "system": "http://unitsofmeasure.org",
                  "code": "mg" 
                }
              }
            ]
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "MedicationDispense"
      }
    },{
      "fullUrl": "urn:uuid:64c3941e-f2a7-4a1b-a3d5-48b6a47db073",
      "resource": {
        "resourceType": "CarePlan",
        "status": "active",
        "intent": "plan",
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "careTeam": [{
            "reference": "urn:uuid:f7e94b0e-35a8-4c6e-b816-10bae8d5f71e"
          }
        ],
        "addresses": [{
            "reference": "urn:uuid:560a749f-2475-44d8-bf7e-435ceef0ee49", 
            "display": "Roels head-neck tumor"
          }
        ],
        "activity": [{
            "detail": {
              "kind": "ServiceRequest",
              "code": {
                "coding": [{
                    "system": "http://snomed.info/sct",
                    "code": "367336001",
                    "display": "Chemotherapy"
                  } 
                ]
              },
              "status": "in-progress",
              "doNotPerform": false,
              "productReference": {
                "reference": "urn:uuid:d74a56d1-7a74-44e8-b927-f743729d72de" 
              }
            }
          }
        ],
        "request": {
        "method": "POST",
        "url": "CarePlan"
      }
      }, {
      "fullUrl": "urn:uuid:f7e94b0e-35a8-4c6e-b816-10bae8d5f71e",
      "resource": {
        "resourceType": "CareTeam",
        "identifier": [{
            "system": "http://sys-ids.kemkes.go.id/careteam/1000001",
            "value": "1200045"
          }
        ],
        "status": "active",
        "category": [{
            "coding": [{
                "system": "http://loinc.org",
                "code": "LA27976-2", 
                "display": "Encounter-focused care team"
              }
            ]
          }
        ],
        "name": "Mister Roel Care Team for Chemotherapy",
        "subject": {
          "reference": "Patient/P00030004", 
          "display": "Roel"
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd" 
        },
        "period": {
          "end": "2013-01-01"
        },
        "participant": [{
            "role": [{
                "text": "Patient" 
              }
            ],
            "member": {
              "reference": "Patient/P00030004", 
              "display": "Roel"
            }
          }, {
            "role": [{
                "text": "medical oncologist" 
              }
            ],
            "member": {
              "reference": "Practitioner/N10000001", 
              "display": "Dokter Bronsig"
            },
            "onBehalfOf": {
              "reference": "Organization/10000211" 
            },
            "period": {
              "end": "2013-01-01"
            }
          }, {
            "role": [{
                "coding": [{
                    "system": "http://snomed.info/sct",
                    "code": "224552008", 
                    "display": "Oncology Nurse"
                  }
                ],
                "text": "Oncology Nurse"
              }
            ],
            "member": {
              "reference": "Practitioner/N10000007", 
              "display": "Dorothy"
            },
            "onBehalfOf": {
              "reference": "Organization/10000211" 
            },
            "period": {
              "end": "2013-01-01"
            }
          }
        ],
        "managingOrganization": [{
            "reference": "Organization/10000211" 
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "CareTeam"
      }
    }, {
      "fullUrl": "urn:uuid:e49a7ce1-7523-4aed-ba0a-f78bf14936f7",
      "resource": {
        "resourceType": "ServiceRequest",
        "status": "active",
        "intent": "order",
        "category": [{
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "3457005",
                "display": "Patient referral"
              }
            ],
            "text": "Patient referral"
          }
        ],
        "code": {
          "coding": [{
              "system": "http://snomed.info/sct",
              "code": "710830005",
              "display": "Assessment of passive range of motion (procedure)"
            }
          ],
          "text": "Assessment of passive range of motion"
        },
        "subject": {
          "reference": "Patient/P00030004" 
        },
        "encounter": {
          "reference": "urn:uuid:10494a2e-0410-4ec7-834d-bd7f77de4ebd", 
          "display": "Roels encounter on January 28th, 2013"
        },
        "occurrenceDateTime": "2016-09-27", 
        "authoredOn": "2016-09-20", 
        "requester": {
          "reference": "Practitioner/N10000001", 
          "display": "Dokter Bronsig"
        },
        "performer": [{
            "reference": "Practitioner/N10000005", 
            "display": "Paul Therapist, PT"
          }, {
            "reference": "Organization/F10000002", 
            "display": "Horizontal District Hospital"
          }
        ],
        "reasonCode": [{
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "201886002",
                "display": "osteoarthritis"
              }
            ],
            "text": "assessment of mobility limitations due to osteoarthritis"
          }
        ],
        "bodySite": [{
            "coding": [{
                "system": "http://snomed.info/sct",
                "code": "36701003",
                "display": "Both knees (body structure)"
              }
            ],
            "text": "Both knees"
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "ServiceRequest"
      }
    },';
     }

     $response .= ']}';
     header('Content-Type: application/json');
      header('HTTP/1.1 200 OK');
     echo str_replace(',]', ']', $response);

     } else {
            $response=array(
               'status' => 0,
               'message' =>'failed'
            );
            header('Content-Type: application/json');
            header('WWW-Authenticate: Basic realm="My Realm"');
            header('HTTP/1.1 403 OK');
            echo json_encode($response);
          }
         }else {
         $response=array(
            'status' => 0,
            'message' =>'failed'
         );
         header('Content-Type: application/json');
         header('WWW-Authenticate: Basic realm="My Realm"');
         header('HTTP/1.1 403 OK');
         echo json_encode($response);
      }

}
  
}
