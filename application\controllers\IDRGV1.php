<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class IDRGV1 extends CI_Controller {

    private $config;

    public function __construct()
    {
      parent::__construct();
      date_default_timezone_set("UTC");
      $this->load->model('IdrgModel');
      $this->load->library('encrypt');
      //date_default_timezone_set("Asia/Bangkok");
      $this->config["key"] = "0d88ebac3b7b545166a65373e9c5b81972f08e4c7b334b58164f21e8dbdd8756";
    }

    public function sendRequest($action = "", $method = "GET", $data = "") {
        $curl = curl_init();
       
        $token = $this->getToken();
        $headers = array(
            #"Accept: application/Json",
            "Content-type: application/json"
         );	

        $url = 'http://*************/E-Klaim/ws.php';
        
		curl_setopt($curl, CURLOPT_URL, $url."/".$action);
		curl_setopt($curl, CURLOPT_HEADER, false);
	
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        
		//curl_setopt($curl, CURLOPT_FAILONERROR, true); 
		curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true); 
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); 
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		
        $result = curl_exec($curl);
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
		return $resp = array(
            'response' => $httpcode,
            'result' => json_decode($result),
            );
    }

    public function diagnosaInaGrouper($params = array()) {
		if(isset($params["keyword"])){
			$keyword = $params["keyword"];
		}
		$data = array(
			"keyword" => $keyword
		);
		$request = array(
			"metadata" => array(
				"method" => "search_diagnosis_inagrouper"
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->encrypt->encrypt($data, $this->config["key"]);
		
		$result = json_decode($this->encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"])); 		
		return $result;
	}

  	public function diagnosaSet($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "idrg_diagnosa_set",
				"nomor_sep" => $data["nomor_sep"]
			),
			"data" => array(
				"diagnosa" => $data['diagnosa']
			)
			
		);
		
		$data = json_encode($request);			
		$data = $this->encrypt->encrypt($data, $this->config["key"]);	
		$result = $this->sendRequest("POST", $data, "application/json");
		$result = $this->encrypt->decrypt($result, $this->config["key"]);
		
		return json_decode($result);
	}

	public function procedureSet($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "idrg_procedure_set",
				"nomor_sep" => $data["nomor_sep"]
			),
			"data" => array(
				"procedure" => $data['procedure']
			)
			
		);
		
		$data = json_encode($request);
		//file_put_contents("grouperSetProc.txt", $data);
		$data = $this->encrypt->encrypt($data, $this->config["key"]);

		$result = $this->sendRequest("POST", $data, "application/json");
		$result = $this->encrypt->decrypt($result, $this->config["key"]);
			
		return json_decode($result);
	}

    public function getTesting(){     
                echo "GET :";
                echo "<br/><br/>";
                echo "RESPONSE : testing";
                echo "<br/><br/>";
                echo "RESULT :";      
    }




}