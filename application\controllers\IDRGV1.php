
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class IDRGV1 extends CI_Controller {

    private $encrypt_config;

    public function __construct()
    {
      parent::__construct();
      date_default_timezone_set("UTC");
      $this->load->model('IdrgModel');
      $this->load->library('custom_encrypt');
      //date_default_timezone_set("Asia/Bangkok");
      $this->encrypt_config["key"] = "0d88ebac3b7b545166a65373e9c5b81972f08e4c7b334b58164f21e8dbdd8756";
    }

    private function sendRequest($method = "GET", $data = "", $contenType = "application/json", $url = "") {
		$curl = curl_init();
		
		$headers = array(
			"Accept: application/Json"
		);		
		
		 $url = 'http://*************/E-Klaim/ws.php';
		
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_HEADER, false);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

		//curl_setopt($curl, CURLOPT_FAILONERROR, true); 
		curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true); 
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); 
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		
		$result = curl_exec($curl);
		
		curl_close($curl);
		return $result;
	}
	

    public function diagnosaInaGrouper() {
		$id = $this->input->get('keyword');
        $keyword = isset($id)? $id: "";
		$data = array(
			"keyword" => $keyword
		);
		$request = array(
			"metadata" => array(
				"method" => "search_diagnosis_inagrouper"
			),
			"data" => $data
		);
		$data = $this->custom_encrypt->encrypt(json_encode($request), $this->encrypt_config["key"]);
		$response = $this->sendRequest("POST", $data);
		$result = json_decode($this->custom_encrypt->decrypt($response, $this->encrypt_config["key"]));
        echo json_encode($result);
		 //return $result;
	}

    public function procedurInaGrouper() {
		$id = $this->input->get('keyword');
        $keyword = isset($id)? $id: "";
		$data = array(
			"keyword" => $keyword
		);
		$request = array(
			"metadata" => array(
				"method" => "search_procedures_inagrouper"
			),
			"data" => $data
		);
		
		$data = $this->custom_encrypt->encrypt(json_encode($request), $this->encrypt_config["key"]);
		$response = $this->sendRequest("POST", $data);
		$result = json_decode($this->custom_encrypt->decrypt($response, $this->encrypt_config["key"]));
        echo json_encode($result); 		
		//return $result;
	}

  	public function diagnosaIdrgSet($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "idrg_diagnosa_set",
				"nomor_sep" => $data["nomor_sep"]
			),
			"data" => array(
				"diagnosa" => $data['diagnosa']
			)
			
		);
		
		$data = json_encode($request);			
		$data = $this->custom_encrypt->encrypt($data, $this->encrypt_config["key"]);
		$response = $this->sendRequest("POST", $data, "application/json");
		$result = $this->custom_encrypt->decrypt($response['result'], $this->encrypt_config["key"]);
		
		return json_decode($result);
	}

	public function procedureIdrgSet($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "idrg_procedure_set",
				"nomor_sep" => $data["nomor_sep"]
			),
			"data" => array(
				"procedure" => $data['procedure']
			)
			
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->encrypt_config["key"]);
		$response = $this->sendRequest("POST", $data, "application/json");
		$result = $this->custom_encrypt->decrypt($response['result'], $this->encrypt_config["key"]);
			
		return json_decode($result);
	}


	public function newClaim() {
		$id = $this->input->get('nopen');
        $nopen = isset($id)? $id: "";
		$datajson = $this->IdrgModel->dataNewClaim($nopen);
		$data = $this->custom_encrypt->encrypt($datajson[0]["LOG"], $this->encrypt_config["key"]);	
		$result = $this->sendRequest("POST", $data, "application/json");
		$result = $this->custom_encrypt->decrypt($result, $this->encrypt_config["key"]);
		$dataedit = array(
					'RESPONSE'=> $result
				);                    
        $this->IdrgModel->editNewClaim($dataedit,$datajson[0]["ID"]);
		var_dump($result);
		//return json_decode($result);
	}

	public function newKlaim() {
		try {
			// Get input parameter
			$nopen = $this->input->post('nopen');

			// Validate input
			if (empty($nopen)) {
				$response = [
					'status' => 'error',
					'message' => 'Parameter nopen is required'
				];
				$this->output
					->set_content_type('application/json')
					->set_output(json_encode($response));
				return;
			}

			// Get data from database
			$datajson = $this->IdrgModel->dataNewClaim($nopen);

			// Check if data exists
			if (empty($datajson) || !isset($datajson[0]["LOG"])) {
				$response = [
					'status' => 'error',
					'message' => 'Data tidak ditemukan untuk nopen: ' . $nopen
				];
				$this->output
					->set_content_type('application/json')
					->set_output(json_encode($response));
				return;
			}

			// Encrypt and send request
			$data = $this->custom_encrypt->encrypt($datajson[0]["LOG"], $this->encrypt_config["key"]);
			$response_data = $this->sendRequest("POST", $data, "application/json");

			// Check if sendRequest returned proper response
			if (!isset($response_data['result'])) {
				$response = [
					'status' => 'error',
					'message' => 'Invalid response from server'
				];
				$this->output
					->set_content_type('application/json')
					->set_output(json_encode($response));
				return;
			}

			// Decrypt the result
			$result = $this->custom_encrypt->decrypt($response_data['result'], $this->encrypt_config["key"]);

			if ($result && $result !== "SIGNATURE_NOT_MATCH") {
				// Update database with response
				$dataedit = array(
					'RESPONSE' => $result
				);
				$this->IdrgModel->editNewClaim($dataedit, $datajson[0]["ID"]);

				// Parse the result if it's JSON
				$parsed_result = json_decode($result, true);

				$response = [
					'status' => 'success',
					'message' => 'Klaim berhasil diproses',
					'nopen' => $nopen,
					'result' => $parsed_result ? $parsed_result : $result
				];
			} else {
				$response = [
					'status' => 'error',
					'message' => 'Gagal memproses klaim atau signature tidak cocok'
				];
			}

		} catch (Exception $e) {
			// Handle any exceptions
			$response = [
				'status' => 'error',
				'message' => 'Terjadi kesalahan: ' . $e->getMessage()
			];
			log_message('error', 'Error in newKlaim: ' . $e->getMessage());
		}

		// Send JSON response
		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($response));
	}
// 	{
//     "metadata": {
//         "method": "new_claim"
//     },
//     "data": {
//         "nomor_kartu": "0000097208276",
//         "nomor_sep": "UJICOBA6",
//         "nomor_rm": "A002122",
//         "nama_pasien": "PASIEN UJICOBA IDRG 2",
//         "tgl_lahir": "2000-01-01 02:00:00",
//         "gender": "2"
//     }
// }
	public function klaimBaru($params = array()) {
		$data = [
				"nomor_kartu" => $params["nomor_kartu"],
				"nomor_sep" => $params["nomor_sep"],
				"nomor_rm" => $params["nomr"],
				"nama_pasien" => $params["nama"],
				"tgl_lahir" => $params["tgl_lahir"],
				"gender" => $params["gender"]
		];
		$request = array(
			"metadata" => array(
				"method" => "new_claim"
			),
			"data" => $data
		);
		
		$data = json_encode($request);		
		$data = $this->custom_encrypt->encrypt($data, $data, $this->encrypt_config["key"]);	
		$result = $this->sendRequest("POST", $data, "application/json");
		$result = $this->custom_encrypt->decrypt($result, $this->encrypt_config["key"]);
		
		return json_decode($result);
	}




	public function updateDataPasien($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "update_patient",
				"nomor_rm" => $data["nomor_rm"]
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->encrypt_config["key"]);
		
		$result = $this->sendRequest("POST", $data, "application/json");
		return json_decode($this->custom_encrypt->decrypt($result, $this->encrypt_config["key"]));
	}

	public function updateDataKlaim($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "set_claim_data",
				"nomor_sep" => $data["nomor_sep"]
			),
			"data" => $data
		);
		
		$data = json_encode($request);		
		$data = $this->custom_encrypt->encrypt($data, $this->encrypt_config["key"]);
		return json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->encrypt_config["key"])); 
	}

	public function grouping($data = array()) {
		$stage = 1;
		$request = array(
			"metadata" => array(
				"method" => "grouper",
				"stage" => $stage,
				"grouper" => "idrg"
			),
			"data" => $data
		);
		
		if(isset($data["special_cmg"])) {
			$stage = 2;
			$request["metadata"]["stage"] = $stage;
		}
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->config["key"]);
		
		return json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"]));
	}
	
		
	public function finalKlaim($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "idrg_grouper_final"
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->config["key"]);
		
		return json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"])); 
	}
		
	public function reEditKlaim($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "reedit_claim"
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->config["key"]);
		return json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"])); 
	}

	public function kirimKlaim($data = array()) {
		$request = array(
			"metadata" => array(
				"method" => "send_claim"
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->config["key"]);
		return json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"])); 
	}
	
	public function batalKlaim($data = array()) {
		$nopen = $data["nopen"];
		$hasilGrouping = $this->hasilGrouping->load(array("NOPEN" => $nopen));
		
		unset($data["nopen"]);
		unset($data["tipe"]);
		
		if(count($hasilGrouping) > 0) {
			$this->hasilGrouping->hapus(array("NOPEN" => $nopen));
		}
		
		$request = array(
			"metadata" => array(
				"method" => "delete_claim"
			),
			"data" => $data
		);
		
		$data = json_encode($request);
		$data = $this->custom_encrypt->encrypt($data, $this->config["key"]);
		$result = json_decode($this->custom_encrypt->decrypt($this->sendRequest("POST", $data, "application/json"), $this->config["key"])); 		
		return $result;
	}

	function grouperIDRG($data = array()) {
		$data = is_array($data) ? $data : (array) $data;
		$response = array();
		//$hasilGrouping = $this->hasilGrouping->load(array("NOPEN" => $data["nopen"]));
		
		$result = array(
			"metaData" => array(
				"code" => "500",
				"message" => "Gagal Grouping",
			),
			"response" => array(
				"Grouper" => array(
					"Drug" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0
					),	
					"Investigation" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0	
					),	
					"Procedure" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0	
					),	
					"Prosthesis" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0
					),	
					"SubAcute" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0
					),
					"Chronic" => array(
						"Deskripsi" => null,
						"Kode" => "None",
						"Tarif" => 0
					),
					"deskripsi" => "",
					"kodeInacbg" => "",
					"noSep" => $data["no_sep"],
					"tarifGruper" => 0,
					"totalTarif" => 0,
					"DC_KEMKES" => 0,
					"DC_BPJS" => 0
				),
				"kirimKlaim" => array(
					"DC_KEMKES" => 0,
					"DC_BPJS" => 0
				)
			)
		);		
		/* buat klaim baru */
		if(count($hasilGrouping) == 0) {
			$klaimBaru = $this->klaimBaru(array(
				"nomor_kartu" => $data["no_peserta"],
				"nomor_sep" => $data["no_sep"],
				"nomor_rm" => $data["norm"],
				"nama_pasien" => $data["nm_pasien"],
				"tgl_lahir" => $data["tgl_lahir"],
				"gender" => $data["jns_kelamin"]
			));
			
			$response["klaimBaru"] = $klaimBaru;
		} else {
			/* jika sebelumnya final maka reFinalKlaim */
			if($hasilGrouping[0]["STATUS"] == 1) {
				$reFinalKlaim = $this->reEditKlaim(array(
					"nomor_sep" => $data["no_sep"]
				));
				$response["reFinalKlaim"] = $reFinalKlaim;
			}
			
			
			/* update data pasien */
			$updateDataPasien = $this->updateDataPasien(array(
				"nomor_kartu" => $data["no_peserta"],
				"nomor_rm" => $data["norm"],
				"nama_pasien" => $data["nm_pasien"],
				"tgl_lahir" => $data["tgl_lahir"],
				"gender" => $data["jns_kelamin"]
			));
			
			$response["updateDataPasien"] = $updateDataPasien;
		}
				
		//file_put_contents("grouper.txt", json_encode($response["updateDataPasien"]));
		$diags = array();
		$notIsSetCount = 0;
		for($i = 1; $i <= 30; $i++) {
			$diag = "diag".$i;
			if(isset($data[$diag])) $diags[] = $data[$diag];
			else $notIsSetCount++;
			
			if($notIsSetCount > 1) break;
		}
		$diags = implode("#", $diags);
		
		$procs = array();
		$notIsSetCount = 0;
		for($i = 1; $i <= 30; $i++) {
			$proc = "proc".$i;
			if(isset($data[$proc])) $procs[] = $data[$proc];
			else $notIsSetCount++;
			
			if($notIsSetCount > 1) break;
		}
		$procs = implode("#", $procs);
		
		$diagsv6 = array();
		$jmldV6 = count($data["diagsv6"]);
		for($i = 0; $i <= $jmldV6; $i++) {
			if(isset($data["diagsv6"][$i]['KODE'])) $diagsv6[] = $data["diagsv6"][$i]['KODE'];
		}
		$diagsv6 = implode("#", $diagsv6);
		
		$procsv6 = array();
		$jmlpV6 = count($data["procsv6"]);
		for($i = 0; $i <= $jmlpV6; $i++) {
			if(isset($data["procsv6"][$i]['KODE'])) $procsv6[] = $data["procsv6"][$i]['KODE'];
		}
		$procsv6 = implode("#", $procsv6);

		/* step 3 update klaim */
		$updateDataKlaim = $this->updateDataKlaim(array(
			"nomor_sep" => $data["no_sep"],
			"tgl_masuk" => $data["tgl_masuk"],
			"tgl_pulang" => $data["tgl_keluar"],
			"cara_masuk" => "gp",
			"jenis_rawat" => $data["jns_perawatan"],
			"kelas_rawat" => $data["kls_perawatan"],
			"birth_weight" => $data["berat_lahir"],
			"discharge_status" => $data["cara_keluar"],
			//"diagnosa" => $diags,
			//"procedure" => $procs == '' ? '#' : $procs,
			//"diagnosa_inagrouper" => $diagsv6 == '' ? '#' : $diagsv6,
			//"procedure_inagrouper" => $procsv6 == '' ? '#' : $procsv6,
			"adl_sub_acute" => $data["adl_sub_acute"],
			"adl_chronic" => $data["adl_chronic"],
			"tarif_rs" => $data["tarif_rs"],
			"tarif_poli_eks" => $data["tarif_poli_eks"],
			"nama_dokter" => $data["dpjp"],
			"icu_indikator" => isset($data["icu_indikator"]) ? $data["icu_indikator"] : 0,
			"icu_los" => isset($data["icu_los"]) ? isset($data["icu_los"]) : 0,
			"ventilator_hour" => isset($data["ventilator_hour"]) ? $data["ventilator_hour"] : 0,
			"kode_tarif" => $this->config["kode_tarif"],
			"payor_id" => "3",
			"payor_cd" => "JKN",
			"coder_nik" => $data["user_nik"]
		));
		
		$response["updateDataKlaim"] = $updateDataKlaim;
		if($response["updateDataKlaim"]->metadata->code != 200) {
			$result["metaData"]["code"] = $response["updateDataKlaim"]->metadata->code;
			$result["metaData"]["message"] = $response["updateDataKlaim"]->metadata->message;
			
			return json_decode(json_encode($result));
		}
		/* step 4 set diagnosa dan prosedur */
		$setdiagnosa = $this->diagnosaSet(array(
						"nomor_sep" => $data["no_sep"],
						"diagnosa" => $diagsv6
						));
		$response["setDiagnosa"] = $setdiagnosa;
		
		$setprocedur = $this->procedureSet(array(
							"nomor_sep" => $data["no_sep"],
							"procedure" => $procsv6
						));
		$response["setProcedur"] = $setprocedur;		

		/* grouping stage 1 */
		$dataGrouping = array(
			"nomor_sep" => $data["no_sep"]
		);
		$grouping = $this->grouping($dataGrouping);
		$response["grouping_stage_1"] = $grouping;
		
		$scmgs = array();			
		if($response["grouping_stage_1"]->metadata->code == 200) {
			$cmgs = isset($response["grouping_stage_1"]->special_cmg_option) ? (count($response["grouping_stage_1"]->special_cmg_option) > 0 ? $response["grouping_stage_1"]->special_cmg_option : false) : false;
			$specdr = $data["spec_dr"];
			$data["spec_dr"] = null;
			if($cmgs) {
				foreach($cmgs as $cmg) {
					$scmgs[] = $cmg->code;
					if($cmg->type == "Special Procedure") $data["spec_proc"] = $cmg->code;
					if($cmg->type == "Special Prosthesis") $data["spec_prosth"] = $cmg->code;
					if($cmg->type == "Special Investigation") $data["spec_inv"] = $cmg->code;
					if($cmg->type == "Special Drugs") $data["spec_dr"] = $cmg->code;
					if($cmg->type == "Special SubAcute") $data["adl_sub_acute"] = $cmg->code;
					if($cmg->type == "Special Chronic") $data["adl_chronic"] = $cmg->code;
				}													
			}
			if($data["spec_dr"] == null) {
				$data["spec_dr"] = $specdr;
				if($specdr != '') $scmgs[] = $specdr;
			}
			
			if(count($scmgs) > 0) {
				$dataGrouping["special_cmg"] = implode("#", $scmgs);
				
				/* grouping stage 2 */
				$grouping = $this->grouping($dataGrouping);
				$response["grouping_stage_2"] = $grouping;
			}
		}			
		
		if($data["status"]) {				
			/* final grouping */
			$finalKlaim = $this->finalKlaim(array(
				"nomor_sep" => $data["no_sep"],
				"coder_nik" => $data["user_nik"]
			));
			$response["finalKlaim"] = $finalKlaim;
		}
		
		/* step 7 kirim klaim */
		if($data["kirim"]) {
			$kirimKlaim = $this->kirimKlaimIndividual(array(
				"nomor_sep" => $data["no_sep"]
			));
			$response["kirimKlaim"] = $kirimKlaim;
		}			
		
		$dcKEMKES = $dcBPJS = 0;
		
		if(isset($grouping)) {
			$result["metaData"]["code"] = $grouping->metadata->code;
			$result["metaData"]["message"] = $grouping->metadata->message;
			
			$total = 0;
			
			$cbg = $grouping->response->cbg;
			//$result["response"]["Grouper"]["kodeInacbg"] = $cbg->code;
			$result["response"]["Grouper"]["kodeInacbg"] = $cbg->code;
			$result["response"]["Grouper"]["deskripsi"] = $cbg->description;
			$total = $result["response"]["Grouper"]["tarifGruper"] = $cbg->tariff;

			//inagrouper
			//$respInaGrouper = $grouping->response_inagrouper;
			$respInaGrouper = $grouping->response_idrg;
			$result["response"]["Grouper"]["mdc_number"] = $respInaGrouper->mdc_number;
			$result["response"]["Grouper"]["mdc_description"] = $respInaGrouper->mdc_description;
			$result["response"]["Grouper"]["drg_code"] = $respInaGrouper->drg_code;
			$result["response"]["Grouper"]["drg_description"] = $respInaGrouper->drg_description;

			if(isset($grouping->response->sub_acute)) {
				$sa = $grouping->response->sub_acute;
				$result["response"]["Grouper"]["SubAcute"]["Kode"] = $sa->code;
				$result["response"]["Grouper"]["SubAcute"]["Deskripsi"] = $sa->description;
				$result["response"]["Grouper"]["SubAcute"]["Tarif"] = $sa->tariff;
			}
			
			if(isset($grouping->response->chronic)) {
				$chr = $grouping->response->chronic;
				$result["response"]["Grouper"]["Chronic"]["Kode"] = $chr->code;
				$result["response"]["Grouper"]["Chronic"]["Deskripsi"] = $chr->description;
				$result["response"]["Grouper"]["Chronic"]["Tarif"] = $chr->tariff;
			}
							
			if(isset($grouping->response->special_cmg)) {
				foreach($grouping->response->special_cmg as $cmg) {
					if($cmg->type == "Special Drug") {
						$result["response"]["Grouper"]["Drug"]["Kode"] = $cmg->code;
						$result["response"]["Grouper"]["Drug"]["Deskripsi"] = $cmg->description;
						$result["response"]["Grouper"]["Drug"]["Tarif"] = $cmg->tariff;
					} 
					if($cmg->type == "Special Prosthesis") {
						$result["response"]["Grouper"]["Prosthesis"]["Kode"] = $cmg->code;
						$result["response"]["Grouper"]["Prosthesis"]["Deskripsi"] = $cmg->description;
						$result["response"]["Grouper"]["Prosthesis"]["Tarif"] = $cmg->tariff;
					} 
					if($cmg->type == "Special Procedure") {
						$result["response"]["Grouper"]["Procedure"]["Kode"] = $cmg->code;
						$result["response"]["Grouper"]["Procedure"]["Deskripsi"] = $cmg->description;
						$result["response"]["Grouper"]["Procedure"]["Tarif"] = $cmg->tariff;
					} 
					if($cmg->type == "Special Investigation") {
						$result["response"]["Grouper"]["Investigation"]["Kode"] = $cmg->code;
						$result["response"]["Grouper"]["Investigation"]["Deskripsi"] = $cmg->description;
						$result["response"]["Grouper"]["Investigation"]["Tarif"] = $cmg->tariff;
					}
				}
			}
		}
		
		if(isset($response["kirimKlaim"])) {
			$list = array();
			if($response["kirimKlaim"]->metadata->code == 200) {
				$list = $response["kirimKlaim"]->response->data;				
			}
			if($list) {
				foreach($list as $l) {
					$dcKEMKES = $l->kemkes_dc_status == 'sent' ? 1 : 0;
					$dcBPJS = $l->bpjs_dc_status == 'sent' ? 1 : 0;
					$result["kirimKlaim"]["DC_KEMKES"] = $dcKEMKES;
					$result["kirimKlaim"]["DC_BPJS"] = $dcBPJS;
				}
			}
		}
		file_put_contents("grouper.txt", json_encode($response));
		$result = json_decode(json_encode($result));		
		$grouper = $result->response->Grouper;
		if($result->metaData->code == 200) {
			$tot = $grouper->Procedure->Tarif + $grouper->Drug->Tarif + $grouper->Investigation->Tarif + $grouper->Prosthesis->Tarif + $grouper->SubAcute->Tarif + $grouper->Chronic->Tarif;
			$grouper->totalTarif = $grouper->tarifGruper + $tot;
		}

		//print_r($response);
		$this->hasilGrouping->simpan(array(
			'NOPEN' => $data['nopen']
			, 'NOSEP' => $data['no_sep']
			, 'CODECBG' => '-'//$grouper->kodeInacbg
			, 'TARIFCBG' => '-' //$grouper->tarifGruper
			, 'TARIFSP' => '-'//$grouper->Procedure->Tarif
			, 'TARIFSR' => '-'//$grouper->Prosthesis->Tarif
			, 'TARIFSI' => '-'//$grouper->Investigation->Tarif
			, 'TARIFSD' => '-'//$grouper->Drug->Tarif
			, 'TARIFSA' => '-' //$grouper->SubAcute->Tarif
			, 'TARIFSC' => '-'//$grouper->Chronic->Tarif
			, 'TARIFKLS1' => 0
			, 'TARIFKLS2' => 0
			, 'TARIFKLS3' => 0
			, 'TOTALTARIF' => $grouper->totalTarif
			, 'TARIFRS' => 
			$data['tarif_rs']['alkes']+
			$data['tarif_rs']['bmhp']+
			$data['tarif_rs']['kamar']+
			$data['tarif_rs']['keperawatan']+
			$data['tarif_rs']['konsultasi']+
			$data['tarif_rs']['laboratorium']+
			$data['tarif_rs']['obat']+
			$data['tarif_rs']['obat_kronis']+
			$data['tarif_rs']['obat_kemoterapi']+
			$data['tarif_rs']['pelayanan_darah']+
			$data['tarif_rs']['penunjang']+
			$data['tarif_rs']['prosedur_bedah']+
			$data['tarif_rs']['prosedur_non_bedah']+
			$data['tarif_rs']['radiologi']+
			$data['tarif_rs']['rawat_intensif']+
			$data['tarif_rs']['rehabilitasi']+
			$data['tarif_rs']['sewa_alat']+
			$data['tarif_rs']['tenaga_ahli']
			, 'UNUSR' => $grouper->Prosthesis->Kode
			, 'UNUSI' => $grouper->Investigation->Kode
			, 'UNUSP' => $grouper->Procedure->Kode
			, 'UNUSD' => $grouper->Drug->Kode
			, 'UNUSA' => $grouper->SubAcute->Kode
			, 'UNUSC' => $grouper->Chronic->Kode
			, 'mdc_number' => $grouper->mdc_number
			, 'mdc_description' => $grouper->mdc_description
			, 'drg_code' => $grouper->drg_code
			, 'drg_description' => $grouper->drg_description
			, 'TANGGAL' => new \Zend\Db\Sql\Expression('NOW()')
			, 'USER' => $data["user"]
			, 'STATUS' => ($data['status'] ? "1" : "0")
			, 'TIPE' => $data["tipe"]
			, 'DC_KEMKES' => $dcKEMKES
			, 'DC_BPJS' => $dcBPJS
			, 'RESPONSE' => json_encode($response)
		));
		echo json_encode($result);

		//return $result;
	}

    public function getTesting(){     
                echo "GET :";
                echo "<br/><br/>";
                echo "RESPONSE : testing";
                echo "<br/><br/>";
                echo "RESULT :";      
    }




}
