<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class IHS extends CI_Controller {
    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('logged_in') == FALSE ){
          redirect('Login');
        }
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('IHSModel');
    }

  public function index()
  {
    // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Organization/10000187';

    // $headers = array(
    //   "Accept: application/json",
    //   "Authorization: Bearer ".$this->session->userdata('token')." ",
    // );

    // $cURL = curl_init();
    // curl_setopt($cURL, CURLOPT_URL,$url);
    // curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    // curl_setopt($cURL, CURLOPT_HTTPHEADER, $headers);
    // curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    // curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    // $response = curl_exec($cURL);
    // curl_close($cURL);
    // $res = json_decode($response);

    $data = array(
      // 'parameterLab' => $this->IHSModel->parameterLab(),
      // 'parameterLabDone' => $this->IHSModel->parameterLabDone(),
      // 'parameterLabTanpa' => $this->IHSModel->parameterLabTanpa(),
    );
    
    // if(isset($res->active)){
      $this->load->view('dashboard', $data);
    // } else {
    //   $this->getToken();
    // }
  }

  public function getToken()
  {
    // get token
    $postDataArray = [
      'client_id' => 'fs2BJRXk1OgoRCIkZA6UPsAGdvv2lwPS0TI76WCXWxtyAHz7','client_secret' => '2VeU4CziJdKZxnfHLn7aw6kktOC4EAnVHKVlUIy3ZQetvjAyCfq6czKDjklfdNFk'
      ];
     
    $data = http_build_query($postDataArray);
     
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    $token = $obj1->access_token;
    $this->session->set_userdata('token', $token);

    $this->index();
  }

  public function getPatientByNIK()
  {
    $nik = $this->input->post('nik');

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.$nik.' ';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);

    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo $response;
    }
  }

  public function getPatientByIHS()
  {
    $id_ihs = $this->input->post('id_ihs');

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient/'.$id_ihs.' ';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      // "Authorization: Bearer Ivkc9RRhyUpNccUe6gCZQ3hM1Z35"
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);

    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo $response;
    }
  }

  public function getPractitionerByNIK()
  {
    $nik = $this->input->post('nikPractitioner');

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Practitioner?identifier=https://fhir.kemkes.go.id/id/nik|'.$nik.' ';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);

    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo $response;
    }
  }

  public function getPractitionerByNIKID()
  {
    $id_ihs = $this->input->post('nikPractitionerID');

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Practitioner/'.$id_ihs.' ';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);

    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo $response;
    }
  }

  public function postEncounter()
  {
    $data = $this->IHSModel->getDataEncounter();

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|3174102810931001';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo 'ID IHS Pasien : ' . $res->entry[0]->resource->id;
    }
  }

  public function mappingSnomed()
  {
    $data = array(
      'dataSnomed' => $this->IHSModel->dataSnomed(),
      );
    $this->load->view('mappingsnomed/index', $data);
  }

  public function mappingLoinc()
  {
    $data = array(
      'dataLoinc' => $this->IHSModel->dataLoinc(),
      );
    $this->load->view('mappingloinc/index', $data);
  }

  public function mappingTindakanLoinc()
  {
    $data = array(
      'dataTindakanLoinc' => $this->IHSModel->dataTindakanLoinc(),
      );
    $this->load->view('mappingTindakanLoinc/index', $data);
  }

  public function mappingTindakanPa()
  {
    $data = array(
      'tindakanPA' => $this->IHSModel->tindakanPA(),
      'dataTindakanPa' => $this->IHSModel->dataTindakanPa(),
      );
    $this->load->view('mappingTindakanPa/index', $data);
  }

  public function cariSnomed()
  {
    $id = $this->input->post('id');
    $nama = $this->input->post('nama');
    $data = array(
      'id' => $id,
      'nama' => $nama,
      'dataSnomedSpecimen' => $this->IHSModel->dataSnomedSpecimen(),
      );
    $this->load->view('mappingsnomed/cariSnomed', $data);
  }

  public function cariTindakanLoinc()
  {
    $id = $this->input->post('id');
    $nama = $this->input->post('nama');
    $data = array(
      'id' => $id,
      'nama' => $nama,
      'dataCariTindakanLoinc' => $this->IHSModel->dataLoincMaster(),
      );
    $this->load->view('mappingTindakanLoinc/cariTindakanLoinc', $data);
  }

  public function simpanMappingTindakanPa()
  {
    $post = $this->input->post();
    $data = array(
      'ID_TINDAKAN' => $post['pilihTindakanPa'],
      'JENIS'       => $post['pilihJenis'],
      'STATUS'      => 1,
    );
    $this->db->insert('remun_medis.tb_mapping_tindakan_pa', $data);
  }

  public function ubahStatusMappingTindakanPa()
  {
    $post = $this->input->post();
    $data = array(
      'STATUS'      => $post['cek'],
    );
    $this->db->where('tb_mapping_tindakan_pa.ID', $post['id']);
    $this->db->update('remun_medis.tb_mapping_tindakan_pa', $data);
  }

  public function cariLoinc()
  {
    $id = $this->input->post('id');
    $nama = $this->input->post('nama');
    $data = array(
      'id' => $id,
      'nama' => $nama,
      // 'dataLoincMaster' => $this->IHSModel->dataLoincMaster(),
      );
    $this->load->view('mappingloinc/cariLoinc', $data);
  }

  function getDataLoinc(){
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));
  
    $listLoinc = $this->IHSModel->datatableLoinc();
    $id = $this->input->post('id');
    $data = array();
    $no = $_POST['start'];
    foreach ($listLoinc as $LI) {
    $no++;
    $button = '<td class="text-center">
    <button type="button" class="btn btn-primary btn-sm ambilLoinc" data-id="'.$id.'" data-code="'.$LI->code.'" data-display="'.$LI->display.'" data-dismiss="modal" data-toggle="modal"><i class="fas fa-save"></i></button>
    </td>';

    $data[] = array(
      $LI->kategori,
      $LI->nama_pemeriksaan,
      $LI->permintaan,
      $LI->spesimen,
      $LI->tipe_hasil_pemeriksaan,
      $LI->satuan,
      $LI->metode_analisis,
      $LI->code,
      $LI->display,
      $LI->component,
      $LI->property,
      $LI->timing,
      $LI->system,
      $LI->scale,
      $LI->method,
      $LI->unit_of_measure,
      $button,
    );
      
    }
  
    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $this->IHSModel->total_count(),
      "recordsFiltered" => $this->IHSModel->filter_count(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  function getDataTindakanLoinc(){
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));
  
    $listLoinc = $this->IHSModel->datatableLoinc();
    $id = $this->input->post('id');
    $data = array();
    $no = $_POST['start'];
    foreach ($listLoinc as $LI) {
    $no++;
    $button = '<td class="text-center">
    <button type="button" class="btn btn-primary btn-sm ambilTindakanLoinc" data-id="'.$id.'" data-code="'.$LI->code.'" data-display="'.$LI->display.'" data-dismiss="modal" data-toggle="modal"><i class="fas fa-save"></i></button>
    </td>';

    $data[] = array(
      $LI->kategori,
      $LI->nama_pemeriksaan,
      $LI->permintaan,
      $LI->spesimen,
      $LI->tipe_hasil_pemeriksaan,
      $LI->satuan,
      $LI->metode_analisis,
      $LI->code,
      $LI->display,
      $LI->component,
      $LI->property,
      $LI->timing,
      $LI->system,
      $LI->scale,
      $LI->method,
      $LI->unit_of_measure,
      $button,
    );
      
    }
  
    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $this->IHSModel->total_count(),
      "recordsFiltered" => $this->IHSModel->filter_count(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function ambilSnomed()
  {
    $id = $this->input->post('id');
    $code = $this->input->post('code');
    $data = array(
      'ID_TINDAKAN' => $id,
      'CODE' => $code,
      'STATUS' => 1,
      );
    $this->db->replace('ihs.tb_mapping_snomed_specimen', $data);
  }

  public function ambilLoinc()
  {
    $id = $this->input->post('id');
    $code = $this->input->post('code');
    $data = array(
      'LOINC' => $code,
      );
    $this->db->where('tb_mapping_lab_parameter.ID', $id);
    $this->db->update('ihs.tb_mapping_lab_parameter', $data);
  }

  public function ambilTindakanLoinc()
  {
    $id = $this->input->post('id');
    $code = $this->input->post('code');
    $display = $this->input->post('display');
    $data = array(
      'ID_TINDAKAN' => $id,
      'CODE' => $code,
      'DISPLAY' => $display,
      );
    $this->db->replace('ihs.tb_mapping_loinc_specimen', $data);
  }

public function saveMappingLoinc()
{
  $id_param = $this->input->post('id');
  $param = $this->input->post('param');
  $kode_loinc = $this->input->post('kode');
  $tindakan = $this->input->post('tindakan');
  $indeks = $this->input->post('indeks');
  $nilai = $this->input->post('nilai');
  $satuan = $this->input->post('satuan');

  $data = array(
    'tindakan' => $tindakan,
    'id_param' => $id_param,
    'parameter' => $param,
    'indeks' => $indeks,
    'nilai' => $nilai,
    'satuan' => $satuan,
    'kode_loinc' => $kode_loinc,
  );

  $this->db->trans_begin();
  
  $this->db->insert('ihs.tb_master_lab', $data);
  if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
  } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
  }
}

public function editMappingLoinc()
{
  $id = $this->input->post('id');
  $kode_loinc = $this->input->post('kode');

  $data = array(
    'kode_loinc' => $kode_loinc
  );

  $this->db->trans_begin();
  
  $this->db->where('ihs.tb_master_lab.id', $id);
  $this->db->update('ihs.tb_master_lab', $data);
  if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
  } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
  }
}

public function saveMappingLoincTanpa()
{
  $id_param = $this->input->post('id');
  $param = $this->input->post('param');
  $kode_loinc = $this->input->post('kode');
  $tindakan = $this->input->post('tindakan');
  $indeks = $this->input->post('indeks');

  $data = array(
    'tindakan' => $tindakan,
    'id_param' => $id_param,
    'parameter' => $param,
    'indeks' => $indeks,
    'kode_loinc' => $kode_loinc,
  );

  $this->db->trans_begin();
  
  $this->db->insert('ihs.tb_master_lab', $data);
  if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
  } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
  }
}

public function postBundlePatient()
{
  $dataKunjungan = $this->IHSModel->dataKunjungan()->result_array();
  $countKunjungan = $this->IHSModel->dataKunjungan()->num_rows();
  $i=1;
  foreach($dataKunjungan as $cek){
    // get id ihs pasien
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.str_replace(' ', '', $cek["KTP_PASIEN"]).' ';
    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $res = json_decode($response);
    $hasilPasien = $res->total;
    if($hasilPasien == 1 || $hasilPasien == 2){
      $idIHSPasien = $res->entry[0]->resource->id;
      $namaPasien = $res->entry[0]->resource->name[0]->text;
    }

    // get id ihs dokter
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Practitioner?identifier=https://fhir.kemkes.go.id/id/nik|'.$cek["KTP_DOKTER"].' ';
    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $res = json_decode($response);
    $hasilDokter = $res->total;
    // echo $cek["KTP_DOKTER"] . ' PINDAH<br>';
    if($hasilDokter == 1 || $hasilDokter == 2){
      $idIHSDokter = $res->entry[0]->resource->id;
      $namaIHSDokter = $res->entry[0]->resource->name[0]->text;
    }
    
    if(($hasilPasien == 1 || $hasilPasien == 2) && ($hasilDokter == 1 || $hasilDokter == 2)){
      $filterKunjungan[] = array(
        'idIHSPasien' => $idIHSPasien,
        'namaPasien' => $namaPasien,
        'idIHSDokter' => $idIHSDokter,
        'namaIHSDokter' => $namaIHSDokter,
        'TGLREG' => $cek['TGLREG'],
        'TGLKELUAR' => $cek['TGLKELUAR'],
        'ID_RUANGAN' => $cek['ID_RUANGAN'],
        'NOPEN' => $cek['NOPEN']
      );
      // echo $idIHSPasien . '-' . $namaPasien . '|' . $idIHSDokter . '-' . $namaIHSDokter . '|' . $i . '<br>';
    }
    $i++;
  }

  $data = '{
  "resourceType": "Bundle",
  "type": "transaction",
  "entry": [';
    $i = 1;
    $hitungTindakan = 1;

  foreach($filterKunjungan as $data1){
    

    //echo "<pre>".print_r($data1)."</pre>";
    //exit();
    // get Encounter
    $getTokenEncounter = $this->guidv4();
    $idIHSPasien = $data1['idIHSPasien'];
    $namaPasien = $data1['namaPasien'];
    $idIHSDokter = $data1['idIHSDokter'];
    $namaIHSDokter = $data1['namaIHSDokter'];

    // Tanggal Reg
    $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
    $tglReg = $date->format(DATE_ATOM);

    // Tanggal Keluar
    $date = date_create_from_format('Y-m-d H:i:s', $data1['TGLKELUAR']);
    $tglKeluar = $date->format(DATE_ATOM);

    $kirimKeluar[] = array(
      'getTokenEncounter' => $getTokenEncounter
    );

    // get ID Location from database
    $getIdLocation = $this->IHSModel->getIdLocation($data1['ID_RUANGAN']);

    // Insert encounter
    $simpanTbEncounter = array(
      'nopen'       => $data1['NOPEN'],
      'uuid'       => $getTokenEncounter,
    );
    // echo "<pre>"; print_r($simpanTbEncounter); echo "</pre>"; exit();
    $this->db->insert('ihs.tb_encounter', $simpanTbEncounter);

    $data .= '{
      "fullUrl": "urn:uuid:'.$getTokenEncounter.'",
      "resource": {
        "resourceType": "Encounter",
        "status": "finished",
        "class": {
          "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
          "code": "AMB",
          "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/'.$idIHSPasien.'",
          "display": "'.$namaPasien.'"
        },
        "participant": [
          {
            "type": [
              {
                "coding": [
                  {
                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                    "code": "ATND",
                    "display": "attender"
                  }
                ]
              }
            ],
            "individual": {
              "reference": "Practitioner/'.$idIHSDokter.'",
              "display": "'.$namaIHSDokter.'"
            }
          }
        ],
        "period": {
          "start": "'.$tglReg.'",
          "end": "'.$tglKeluar.'"
        },
        "location": [
          {
            "location": {
              "reference": "Location/'.$getIdLocation['ID_IHS_LOCATION'].'",
              "display": "'.$getIdLocation['KET_IHS_LOCATION'].'"
            }
          }
        ],';

        $dataTindakan = $this->IHSModel->dataTindakan($data1['NOPEN'])->result_array();
        $countTindakan = $this->IHSModel->dataTindakan($data1['NOPEN'])->num_rows();
        $j = 1;
        $data .= '"diagnosis": [';
        foreach($dataTindakan as $data2){  
          $getTokenCondition = $this->guidv4();
          $kirimKeluarCondition[] = array(
            'getTokenEncounter' => $getTokenEncounter,
            'getTokenCondition' => $getTokenCondition,
            'getNopen' => $data2['NOPEN'],
            'DIAGNOSA' => $data2['DIAGNOSA'],
            'DIAGNOSA_DESKRIPSI' => $data2['DIAGNOSA_DESKRIPSI'],
            'idIHSPasien' => $idIHSPasien,
            'namaPasien' => $namaPasien,
            'tglReg' => $tglReg,
          );
          // echo $getTokenEncounter . ' - ' . $getTokenCondition . ' - ' . $data2['NOPEN'];
          $simpanTbCondition = array(
            'uuid_enc'       => $getTokenCondition,
            'nopen'       => $data2['NOPEN'],
          );
          $this->db->insert('ihs.tb_condition', $simpanTbCondition);

          $data .='{
            "condition": {
              "reference": "urn:uuid:'.$getTokenCondition.'",
              "display": "'.$data2['DIAGNOSA_DESKRIPSI'].'"
            },
            "use": {
              "coding": [
                {
                  "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
                  "code": "DD",
                  "display": "Discharge diagnosis"
                }
              ]
            },
            "rank": '.$data2['JENIS'].'
          }';
          if($j < $countTindakan){
            $data .= ',';
          }
          $j++;
        }
        $data .= '],';

        $data .= '"statusHistory": [
          {
            "status": "arrived",
            "period": {
              "start": "'.$tglReg.'",
              "end": "'.$tglKeluar.'"
            }
          },
          {
            "status": "finished",
            "period": {
              "start": "'.$tglKeluar.'",
              "end": "'.$tglKeluar.'"
            }
          }
        ],
        "serviceProvider": {
          "reference":"Organization/10000187"
        },
        "identifier": [
          {
            "system": "http://sys-ids.kemkes.go.id/encounter/10000187",
            "value": "P20240001"
          }
        ]
      },
      "request": {
        "method": "POST",
        "url": "Encounter"
      }
    }';
    if($i < $dataTindakan){
      $data .= ',';
    }
    $i++;
  }
// $data .= ',';
$i=1;
foreach($kirimKeluarCondition as $con){
    $data .= '{
      "fullUrl": "urn:uuid:'.$con['getTokenCondition'].'",
      "resource": {
        "resourceType": "Condition",
        "clinicalStatus": {
          "coding": [
            {
              "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
              "code": "active",
              "display": "Active"
            }
          ]
        },
        "category": [
          {
            "coding": [
              {
                "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                "code": "encounter-diagnosis",
                "display": "Encounter Diagnosis"
              }
            ]
          }
        ],
        "code": {
          "coding": [
            {
              "system": "http://hl7.org/fhir/sid/icd-10",
              "code": "'.$con['DIAGNOSA'].'",
              "display": "'.$con['DIAGNOSA_DESKRIPSI'].'"
            }
          ]
        },
        "subject": {
          "reference": "Patient/'.$con['idIHSPasien'].'",
          "display": "'.$con['namaPasien'].'"
        },
        "encounter": {
          "reference": "urn:uuid:'.$con['getTokenEncounter'].'",
          "display": "Kunjungan '.$con['namaPasien'].' pada tanggal '.$con['tglReg'].'"
        }
      },
      "request": {
        "method": "POST",
        "url": "Condition"
      }
    }';
    if($i < count($kirimKeluarCondition)){
      $data .= ',';
    }
    $i++;
  }
  $data .= ']
}';

echo $data;

  $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo '<pre>'.$response."</pre>";

      // Insert bundle log
      $simpanLogBundle = array(
        'log'       => $data,
        'response'       => $response,
      );
      $this->db->insert('ihs.tb_log_bundle', $simpanLogBundle);      
    }
    
      $i=0;
    foreach($kirimKeluar as $upt){
      // echo $res->entry[$i]->response->resourceID . '---' . $upt['getTokenEncounter'] . '<br>';
      $ubahTbEncounter = array(
        'encounter'       => $res->entry[$i]->response->resourceID,
      );
      $this->db->where('tb_encounter.uuid', $upt['getTokenEncounter']);
      $this->db->update('ihs.tb_encounter', $ubahTbEncounter);
      $i++;
    }

    $j = 0;
    for($i=count($kirimKeluar); $i<count($kirimKeluarCondition)+count($kirimKeluar); $i++){
      if($res->entry[$i]->response->resourceType == "Condition"){
        $ubahTbCondition = array(
          'condition'       => $res->entry[$i]->response->resourceID,
        );
        $this->db->where('tb_condition.uuid_enc', $kirimKeluarCondition[$j]['getTokenCondition']);
        $this->db->update('ihs.tb_condition', $ubahTbCondition);
        $j++;
      }
    }
  }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
}

  public function postLocation()
  {
    $name = $this->input->post('name');
    $description = $this->input->post('description');
    $category = $this->input->post('category');
    $labelcat = $this->input->post('labelcat');
    $partof = $this->input->post('partof');
    $labelpartof = $this->input->post('labelpartof');

    if($this->input->post('ruangan')) {
      $ruangan = $this->input->post('ruangan');
      $description = $this->input->post('ruangan');
    } else {
      $ruangan = $this->input->post('name');
      $description = $this->input->post('description');
    }

    if($category == 'si') {
      $data = '{
        "resourceType": "Location",
        "status": "active",
        "name": "'.$ruangan.'", 
        "description": "'.$description.'", 
        "mode": "instance",
        "physicalType": {
            "coding": [
                {
            "code": "'.$category.'",
            "display": "'.$labelcat.'"
                }
            ]
        },
        "position": { 
          "longitude": -6.18653445319947,
          "latitude":  106.79803366039877
        },
        "managingOrganization": {
            "reference": "Organization/10000187"
            }
      }';
    } else {
      $data = '{
        "resourceType": "Location",
        "status": "active",
        "name": "'.$name.'", 
        "description": "'.$description.'", 
        "mode": "instance",
        "physicalType": {
            "coding": [
                {
                  "system": "http://terminology.hl7.org/CodeSystem/location-physical-type",
            "code": "'.$category.'",
            "display": "'.$labelcat.'"
                }
            ]
        },
        "managingOrganization": {
            "reference": "Organization/10000187"
        },
        "partOf": {
          "reference": "Location/'.$partof.'",
          "display": "'.$labelpartof.'"
        }
      }';
    }
    
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location';
    // echo '<pre>'.print_r($data)."</pre>";
      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$this->session->userdata('token')." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      // echo $response;
      $res = json_decode($response);
      if(isset($res->fault)){
        $this->getToken();
      } else {
        echo 'Status Code : ' . $httpcode . '<br>';
        // exit();
        // echo '<pre>'.print_r($data)."</pre>";
        // echo $response;
        $data = array(
          'id_ihs' => $res->id,
          'description' => $res->description,
          'physicaltype' => $category,
          'id_ruangan' => $this->input->post('id_ruangan'),
          'partof' => $partof
        );

        $this->db->trans_begin();
        
        $this->db->insert('ihs.tb_location', $data);
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        // echo '<pre>'.print_r($res).'</pre>';
      }
  }

  public function getLocation()
  {
    $id_ihs = $this->input->post('id_ihs');

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Location/'.$id_ihs.' ';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    // echo $response;
    $res = json_decode($response);

    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo $response;
    }
  }

  public function postBundleCondition()
  {
    $data = '{
      "resourceType": "Bundle",
      "type": "transaction",
      "entry": [';

      $getDiagnosa = $this->IHSModel->getDiagnosa()->result_array();
          $countGetDiagnosa = $this->IHSModel->getDiagnosa()->num_rows();
          
          $i = 1;
          foreach($getDiagnosa as $diagnosa):
              // get Encounter
              $getTokenCondition = $this->guidv4();

              // echo "<pre>".print_r($diagnosa)."</pre>";

              // Get id ihs pasien
              $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.$diagnosa["KTP_PASIEN"].' ';
                $cURL = curl_init();
                curl_setopt($cURL, CURLOPT_URL,$url);
                curl_setopt($cURL, CURLOPT_HEADER,false);
                curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
                curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
                  "Content-Type: application/json",
                  "Authorization: Bearer ".$this->session->userdata('token')." "
                ));
                curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
                $response = curl_exec($cURL);
                curl_close($cURL);
                $res = json_decode($response);
                $idIHSPasien = $res->entry[0]->resource->id;
                $namaPasien = $res->entry[0]->resource->name[0]->text;

                // Insert condition
                $simpanTbCondition = array(
                  'nopen'       => $diagnosa['NOPEN']
                );
                $this->db->insert('ihs.tb_condition', $simpanTbCondition);

                $data .= '{
                  "fullUrl": "urn:uuid:'.$getTokenCondition.'",
                  "resource": {
                    "resourceType": "Condition",
                    "clinicalStatus": {
                      "coding": [
                        {
                          "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                          "code": "active",
                          "display": "Active"
                        }
                      ]
                    },
                    "category": [
                      {
                        "coding": [
                          {
                            "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                            "code": "encounter-diagnosis",
                            "display": "Encounter Diagnosis"
                          }
                        ]
                      }
                    ],
                    "code": {
                      "coding": [
                        {
                          "system": "http://hl7.org/fhir/sid/icd-10",
                          "code": "'.$diagnosa['DIAGNOSA'].'",
                          "display": "'.$diagnosa['DIAGNOSA_DESKRIPSI'].'"
                        }
                      ]
                    },
                    "subject": {
                      "reference": "Patient/'.$idIHSPasien.'",
                      "display": "'.$namaPasien.'"
                    },
                    "encounter": {
                      "reference": "Encounter/'.$diagnosa['encounter'].'",
                      "display": "Kunjungan '.$namaPasien. ', '.$diagnosa['TGLREG'].'"
                    }
                  },
                  "request": {
                    "method": "POST",
                    "url": "Condition"
                  }
                }';
                if($i < $countGetDiagnosa){
                  $data .= ',';
                }
                $i++;
              endforeach;
              $data .= ']
            }';

              // echo '<pre>'.print_r($diagnosa).'<br></pre>';

        
      
    // echo '<pre>'.print_r($data).'</pre>';
    

    
      



  $url = 'https://fhir-poc.dto.kemkes.go.id/v1';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);
    if(isset($res->fault)){
      $this->getToken();
    } else {
      echo 'Status Code : ' . $httpcode . '<br>';
      echo '<pre>'.$response."</pre>";
    }

    // Update condition
    foreach($res->entry as $responCondition){
      $ubahTbCondition = array(
        'condition'       => $responCondition->response->resourceID,
      );
      $this->db->where('tb_condition.condition', NULL);
      $this->db->order_by('tb_condition.id', 'ASC');
      $this->db->limit(1);
      $this->db->update('ihs.tb_condition', $ubahTbCondition);
    }
  }

  public function getListEncounter()
  {
    $nomr = $this->input->post('nomr');

    $getEncounter = $this->IHSModel->getEncounter($nomr);

    foreach($getEncounter as $getEncounter):

      // echo "<pre>".print_r($getEncounter)."</pre>";
      // Get encounter
      $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Encounter/'.$getEncounter['encounter'].' ';
      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$this->session->userdata('token')." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      curl_close($cURL);
      $res = json_decode($response);

      echo "<table>";
      echo    "<tr>";
      echo    "   <th>Class</th>";
      echo    "   <th>Location</th>";
      echo    "   <th>Practitioner</th>";
      echo    "   <th>Start</th>";
      echo    "   <th>End</th>";
      echo    "   <th>Patient Name</th>";
      echo    "</tr>";
      echo    "<tr>";
      echo        "<td>".$res->class->display."</td>";
      echo        "<td>".$res->location[0]->location->display."</td>";
      echo        "<td>".$res->participant[0]->individual->display."</td>";
      echo        "<td>".$res->period->start."</td>";
      echo        "<td>".$res->period->end."</td>";
      echo        "<td>".$res->subject->display."</td>";
      echo    "</tr>";
      echo "</table><br>";

      // Get condition

      echo "<table>";
        echo    "<tr>";
        echo    "   <th>ICD</th>";
        echo    "   <th>Display</th>";
        echo    "</tr>";

        foreach($res->diagnosis as $diagnosis):
          
          $url_con = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Condition/'.substr($diagnosis->condition->reference,10).' ';
          $cURLCon = curl_init();
          curl_setopt($cURLCon, CURLOPT_URL,$url_con);
          curl_setopt($cURLCon, CURLOPT_HEADER,false);
          curl_setopt($cURLCon, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURLCon, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$this->session->userdata('token')." "
          ));
          curl_setopt($cURLCon, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURLCon, CURLOPT_CONNECTTIMEOUT,10);
          $responsecon = curl_exec($cURLCon);
          curl_close($cURLCon);
          $rescon = json_decode($responsecon);

          
          echo    "<tr>";
          echo        "<td>".$rescon->code->coding[0]->code."</td>";
          echo        "<td>".$rescon->code->coding[0]->display."</td>";
          echo    "</tr>";
        endforeach;

        echo "</table>";



      
      // echo "<pre>".print_r($res->diagnosis)."</pre>";
      foreach($res->diagnosis as $diagnosis):
        // echo "<pre>".print_r(substr($diagnosis->condition->reference,10))."</pre>";
      endforeach;
      // echo "<pre>".print_r($res->id)."</pre>";
      // echo "<pre>".print_r($res)."</pre>";
    endforeach;
  }

//   public function postBundlePatient()
// {
//   $data = '{
//     "resourceType": "Bundle",
//     "type": "transaction",
//     "entry": [';

//     // Encounter Pertama
//         $data .='{
//         "fullUrl": "urn:uuid:780cb23c-03c4-4441-93a6-ec540afd7af8",
//         "resource": {
//           "resourceType": "Encounter",
//           "status": "finished",
//           "class": {
//             "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
//             "code": "AMB",
//             "display": "ambulatory"
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "Angga"
//           },
//           "participant": [
//             {
//               "type": [
//                 {
//                   "coding": [
//                     {
//                       "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
//                       "code": "ATND",
//                       "display": "attender"
//                     }
//                   ]
//                 }
//               ],
//               "individual": {
//                 "reference": "Practitioner/**********",
//                 "display": "Dr. Hasan, S.Kom, Spok, M.Kom, S.Ikom"
//               }
//             }
//           ],
//           "period": {
//             "start": "2022-06-14T07:00:00+07:00",
//             "end": "2022-06-14T09:00:00+07:00"
//           },
//           "location": [
//             {
//               "location": {
//                 "reference": "Location/d0c26dc6-5a4c-40b6-86a9-cc7c3e164196",
//                 "display": "Poliklinik Onkologi 1, Rawat Jalan"
//               }
//             }
//           ],
//           "diagnosis": [
//             {
//               "condition": {
//                 "reference": "Condition/46c85a6f-25b1-4353-84ec-97d6cd392f3f",
//                 "display": "Stroke Iskemik dan Hipertensi"
//               },
//               "use": {
//                 "coding": [
//                   {
//                     "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
//                     "code": "DD",
//                     "display": "Discharge diagnosis"
//                   }
//                 ]
//               },
//               "rank": 1
//             },
//             {
//               "condition": {
//                 "reference": "Condition/cb99e13f-6cee-48d2-8c78-91ac5f0d748d",
//                 "display": "I10 Essential (primary) hypertension"
//               },
//               "use": {
//                 "coding": [
//                   {
//                     "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
//                     "code": "DD",
//                     "display": "Discharge diagnosis"
//                   }
//                 ]
//               },
//               "rank": 2
//             }
//           ],
//           "statusHistory": [
//             {
//               "status": "arrived",
//               "period": {
//                 "start": "2022-06-14T07:00:00+07:00",
//                 "end": "2022-06-14T08:00:00+07:00"
//               }
//             },
//             {
//               "status": "in-progress",
//               "period": {
//                 "start": "2022-06-14T08:00:00+07:00",
//                 "end": "2022-06-14T09:00:00+07:00"
//               }
//             },
//             {
//               "status": "finished",
//               "period": {
//                 "start": "2022-06-14T09:00:00+07:00",
//                 "end": "2022-06-14T09:00:00+07:00"
//               }
//             }
//           ],
//           "serviceProvider": {
//             "reference":"Organization/10000187"
//           },
//           "identifier": [
//             {
//               "system": "http://sys-ids.kemkes.go.id/encounter/1000004",
//               "value": "P20240001"
//             }
//           ]
//         },
//         "request": {
//           "method": "POST",
//           "url": "Encounter"
//         }
//       },';

//       // Encounter Kedua
//       $data .='{
//         "fullUrl": "urn:uuid:724a781e-aa7b-47f4-b19d-6befe52f8eb7",
//         "resource": {
//           "resourceType": "Encounter",
//           "status": "finished",
//           "class": {
//             "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
//             "code": "AMB",
//             "display": "ambulatory"
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "HASAN"
//           },
//           "participant": [
//             {
//               "type": [
//                 {
//                   "coding": [
//                     {
//                       "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
//                       "code": "ATND",
//                       "display": "attender"
//                     }
//                   ]
//                 }
//               ],
//               "individual": {
//                 "reference": "Practitioner/**********",
//                 "display": "Dr. Angga, S.Kom, Spok, M.Kom, S.Ikom"
//               }
//             }
//           ],
//           "period": {
//             "start": "2022-06-14T07:00:00+07:00",
//             "end": "2022-06-14T09:00:00+07:00"
//           },
//           "location": [
//             {
//               "location": {
//                 "reference": "Location/d0c26dc6-5a4c-40b6-86a9-cc7c3e164196",
//                 "display": "Poliklinik Onkologi 1, Rawat Jalan"
//               }
//             }
//           ],
//           "diagnosis": [
//             {
//               "condition": {
//                 "reference": "Condition/ae48c6b8-2cb5-48fb-83fc-35b9fe761632",
//                 "display": "Stroke Iskemik dan Hipertensi"
//               },
//               "use": {
//                 "coding": [
//                   {
//                     "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
//                     "code": "DD",
//                     "display": "Discharge diagnosis"
//                   }
//                 ]
//               },
//               "rank": 1
//             },
//             {
//               "condition": {
//                 "reference": "Condition/517ce34a-a688-4bf5-be7a-05894195f314",
//                 "display": "I10 Essential (primary) hypertension"
//               },
//               "use": {
//                 "coding": [
//                   {
//                     "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
//                     "code": "DD",
//                     "display": "Discharge diagnosis"
//                   }
//                 ]
//               },
//               "rank": 2
//             }
//           ],
//           "statusHistory": [
//             {
//               "status": "arrived",
//               "period": {
//                 "start": "2022-06-14T07:00:00+07:00",
//                 "end": "2022-06-14T08:00:00+07:00"
//               }
//             },
//             {
//               "status": "in-progress",
//               "period": {
//                 "start": "2022-06-14T08:00:00+07:00",
//                 "end": "2022-06-14T09:00:00+07:00"
//               }
//             },
//             {
//               "status": "finished",
//               "period": {
//                 "start": "2022-06-14T09:00:00+07:00",
//                 "end": "2022-06-14T09:00:00+07:00"
//               }
//             }
//           ],
//           "serviceProvider": {
//             "reference":"Organization/10000187"
//           },
//           "identifier": [
//             {
//               "system": "http://sys-ids.kemkes.go.id/encounter/1000004",
//               "value": "P20240001"
//             }
//           ]
//         },
//         "request": {
//           "method": "POST",
//           "url": "Encounter"
//         }
//       },';

//       // Condition Pertama untuk Encounter Pertama
//       $data .='{
//         "fullUrl": "urn:uuid:46c85a6f-25b1-4353-84ec-97d6cd392f3f",
//         "resource": {
//           "resourceType": "Condition",
//           "clinicalStatus": {
//             "coding": [
//               {
//                 "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
//                 "code": "active",
//                 "display": "Active"
//               }
//             ]
//           },
//           "category": [
//             {
//               "coding": [
//                 {
//                   "system": "http://terminology.hl7.org/CodeSystem/condition-category",
//                   "code": "encounter-diagnosis",
//                   "display": "Encounter Diagnosis"
//                 }
//               ]
//             }
//           ],
//           "code": {
//             "coding": [
//               {
//                 "system": "http://hl7.org/fhir/sid/icd-10",
//                 "code": "I63.9",
//                 "display": "Cerebral infarction, unspecified"
//               }
//             ]
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "Hasan"
//           },
//           "encounter": {
//             "reference": "Encounter/780cb23c-03c4-4441-93a6-ec540afd7af8",
//             "display": "Kunjungan Hasan di hari Jumat, 1 Juli 2022"
//           }
//         },
//         "request": {
//           "method": "POST",
//           "url": "Condition"
//         }
//       },';

//       // Condition Kedua Untuk Encounter Pertama
//       $data .='{
//         "fullUrl": "urn:uuid:cb99e13f-6cee-48d2-8c78-91ac5f0d748d",
//         "resource": {
//           "resourceType": "Condition",
//           "clinicalStatus": {
//             "coding": [
//               {
//                 "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
//                 "code": "active",
//                 "display": "Active"
//               }
//             ]
//           },
//           "category": [
//             {
//               "coding": [
//                 {
//                   "system": "http://terminology.hl7.org/CodeSystem/condition-category",
//                   "code": "encounter-diagnosis",
//                   "display": "Encounter Diagnosis"
//                 }
//               ]
//             }
//           ],
//           "code": {
//             "coding": [
//               {
//                 "system": "http://hl7.org/fhir/sid/icd-10",
//                 "code": "I10",
//                 "display": "Essential (primary) hypertension"
//               }
//             ]
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "Hasan"
//           },
//           "encounter": {
//             "reference": "Encounter/780cb23c-03c4-4441-93a6-ec540afd7af8",
//             "display": "Kunjungan Hasan di hari Sabtu, 2 Juli 2022"
//           }
//         },
//         "request": {
//           "method": "POST",
//           "url": "Condition"
//         }
//       },';

//       // Condition Pertama Untuk Encounter Kedua
//       $data.='{
//         "fullUrl": "urn:uuid:ae48c6b8-2cb5-48fb-83fc-35b9fe761632",
//         "resource": {
//           "resourceType": "Condition",
//           "clinicalStatus": {
//             "coding": [
//               {
//                 "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
//                 "code": "active",
//                 "display": "Active"
//               }
//             ]
//           },
//           "category": [
//             {
//               "coding": [
//                 {
//                   "system": "http://terminology.hl7.org/CodeSystem/condition-category",
//                   "code": "encounter-diagnosis",
//                   "display": "Encounter Diagnosis"
//                 }
//               ]
//             }
//           ],
//           "code": {
//             "coding": [
//               {
//                 "system": "http://hl7.org/fhir/sid/icd-10",
//                 "code": "I63.9",
//                 "display": "Cerebral infarction, unspecified"
//               }
//             ]
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "Hasan"
//           },
//           "encounter": {
//             "reference": "Encounter/724a781e-aa7b-47f4-b19d-6befe52f8eb7",
//             "display": "Kunjungan Hasan di hari Jumat, 1 Juli 2022"
//           }
//         },
//         "request": {
//           "method": "POST",
//           "url": "Condition"
//         }
//       },';

//       // Condition Kedua Untuk Encounter Kedua
//       $data.='{
//         "fullUrl": "urn:uuid:517ce34a-a688-4bf5-be7a-05894195f314",
//         "resource": {
//           "resourceType": "Condition",
//           "clinicalStatus": {
//             "coding": [
//               {
//                 "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
//                 "code": "active",
//                 "display": "Active"
//               }
//             ]
//           },
//           "category": [
//             {
//               "coding": [
//                 {
//                   "system": "http://terminology.hl7.org/CodeSystem/condition-category",
//                   "code": "encounter-diagnosis",
//                   "display": "Encounter Diagnosis"
//                 }
//               ]
//             }
//           ],
//           "code": {
//             "coding": [
//               {
//                 "system": "http://hl7.org/fhir/sid/icd-10",
//                 "code": "I10",
//                 "display": "Essential (primary) hypertension"
//               }
//             ]
//           },
//           "subject": {
//             "reference": "Patient/P02079906980",
//             "display": "Hasan"
//           },
//           "encounter": {
//             "reference": "Encounter/724a781e-aa7b-47f4-b19d-6befe52f8eb7",
//             "display": "Kunjungan Hasan di hari Sabtu, 2 Juli 2022"
//           }
//         },
//         "request": {
//           "method": "POST",
//           "url": "Condition"
//         }
//       }
      
//     ]
//   }';

//   $url = 'https://fhir-poc.dto.kemkes.go.id/v1';

//     $cURL = curl_init();
//     curl_setopt($cURL, CURLOPT_URL,$url);
//     curl_setopt($cURL, CURLOPT_HEADER,false);
//     curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
//     curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
//       "Content-Type: application/json",
//       "Authorization: Bearer ".$this->session->userdata('token')." "
//     ));
//     curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
//     curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
//     curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
//     curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
//     $response = curl_exec($cURL);
//     $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
//     curl_close($cURL);
//     // echo $response;
//     $res = json_decode($response);
//     if(isset($res->fault)){
//       $this->getToken();
//     } else {
//       echo 'Status Code : ' . $httpcode . '<br>';
//       echo '<pre>'.$response."</pre>";
//     }
//   }
}


  // public function getPatientByNameBirthplace()
  // {
  //   $name = $this->input->post('name');
  //   $birthplace = $this->input->post('birthplace');

  //   $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Patient?given='.$name.'&birthplace='.$birthplace.' ';

  //   $cURL = curl_init();
  //   curl_setopt($cURL, CURLOPT_URL,$url);
  //   curl_setopt($cURL, CURLOPT_HEADER,false);
  //   curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
  //   curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
  //     "Content-Type: application/json",
  //     "Authorization: Bearer ".$this->session->userdata('token')." "
  //   ));
  //   curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
  //   curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
  //   $response = curl_exec($cURL);
  //   curl_close($cURL);
  //   echo $response;
  //   $res = json_decode($response);

  //   if(isset($res->entry)){
  //     // echo $this->session->userdata('token');
  //     return $data;
  //   } else {
  //     $this->getToken();
  //   }
  // }
// $res->entry[0]->resource->id

  



