<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatRANAPModel extends CI_Model {

	public function Encounter()
	{
		$query = $this->db->query("CALL ihs.EncounterIGD()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

    function getNakes()
	{
		$query = "SELECT
                    peg.NIP,
                    peg.NAMA_LENGKAP,
                    peg.KTP,
                    peng.ID ID_SIMPEL
                FROM
                    ihs.tb_pegawai peg 
                    LEFT JOIN aplikasi.pengguna peng ON peng.NIP = peg.NIP AND peng.STATUS = 1
                WHERE
                    peg.JENIS_TENAGA NOT IN ( ?, ? ) 
                    AND peg.STATUS_AKTIF = 1 
                    AND peg.KTP != 0
                    AND peg.NIP NOT IN (
                        select dok.NIP_SIMPEG from ihs.tb_dokter dok 
                    )
                    AND peg.KTP NOT IN (
                        select n.NIK from ihs.tb_nakes n 
                    )";
		$bind = $this->db->query($query, array(332, 336));
		return $bind;
	}

    function dataKamarRanap()
	{
		$query = "SELECT
                    loc.id_ihs IHS_PARTOF_RUANGAN,
                    rk.ID ID_RUANG_KAMAR,
                    loc.id_ruangan ID_RUANGAN,
                    loc.description NAMA_RUANGAN,
                    rk.KAMAR NAMA_KAMAR,
                    CASE
                    WHEN rk.KELAS = 3 THEN '1'
                    WHEN rk.KELAS = 2 THEN '2'
                    WHEN rk.KELAS = 1 THEN '3'
                    WHEN rk.KELAS = 36 THEN 'vip'
                    WHEN rk.KELAS = 35 THEN 'vvip'
                    WHEN rk.KELAS IN (48,49,50,51) THEN 'eksekutif'
                    ELSE
                        'reguler'
                    END ID_KELAS_IHS,
                    CASE
                    WHEN rk.KELAS = 3 THEN 'Kelas 1'
                    WHEN rk.KELAS = 2 THEN 'Kelas 2'
                    WHEN rk.KELAS = 1 THEN 'Kelas 3'
                    WHEN rk.KELAS = 36 THEN 'Kelas VIP'
                    WHEN rk.KELAS = 35 THEN 'Kelas VVIP'
                    WHEN rk.KELAS IN (48,49,50,51) THEN 'Kelas Eksekutif'
                    ELSE
                        'Kelas Reguler'
                    END NAMA_KELAS_IHS 
                
                FROM
                    master.ruang_kamar rk
                    LEFT JOIN ihs.tb_location loc ON loc.id_ruangan = rk.RUANGAN AND loc.status = 1
                    LEFT JOIN master.referensi r ON r.ID = rk.KELAS AND r.JENIS = 19 AND r.STATUS = 1
                WHERE rk.STATUS = 1 AND rk.ID != 319 AND rk.ID NOT IN (select lo.id_kamar from ihs.tb_location lo where lo.status = 1)
                ORDER BY loc.description ASC, NAMA_KELAS_IHS ASC";
		$bind = $this->db->query($query);
		return $bind;
	}

    function dataBedRanap()
	{
		$query = "SELECT
                    loc.id_ihs ID_PARTOF,
                    loc.id_ruangan ID_RUANGAN,
                    (SELECT l.description NAMA_RUANGAN FROM ihs.tb_location l WHERE l.id_kamar IS NULL AND l.id_ruangan = loc.id_ruangan) NAMA_RUANGAN,
                    loc.id_kamar ID_KAMAR,
                    loc.description NAMA_KAMAR,
                    loc.id_bed ID_BED_IHS,
                    CASE
                    WHEN rk.KELAS = 3 THEN '1'
                    WHEN rk.KELAS = 2 THEN '2'
                    WHEN rk.KELAS = 1 THEN '3'
                    WHEN rk.KELAS = 36 THEN 'vip'
                    WHEN rk.KELAS = 35 THEN 'vvip'
                    WHEN rk.KELAS IN (48,49,50,51) THEN 'eksekutif'
                    ELSE
                            'reguler'
                    END ID_KELAS_IHS,
                    CASE
                    WHEN rk.KELAS = 3 THEN 'Kelas 1'
                    WHEN rk.KELAS = 2 THEN 'Kelas 2'
                    WHEN rk.KELAS = 1 THEN 'Kelas 3'
                    WHEN rk.KELAS = 36 THEN 'Kelas VIP'
                    WHEN rk.KELAS = 35 THEN 'Kelas VVIP'
                    WHEN rk.KELAS IN (48,49,50,51) THEN 'Kelas Eksekutif'
                    ELSE
                            'Kelas Reguler'
                    END NAMA_KELAS_IHS ,
                    rkt.ID ID_BED,
                    rkt.TEMPAT_TIDUR NAMA_BED
                    FROM
                        master.ruang_kamar_tidur rkt
                        LEFT JOIN master.ruang_kamar rk ON rk.ID = rkt.RUANG_KAMAR AND rk.STATUS = 1
                        LEFT JOIN ihs.tb_location loc ON loc.id_kamar = rkt.RUANG_KAMAR AND loc.STATUS = 1
                    WHERE
                    rkt.ID NOT IN (select lo.id_bed from ihs.tb_location lo where lo.STATUS = 1 and lo.physicaltype = 'bd')
                    AND rkt.STATUS IN (1,2,3,5)
                    AND loc.partof IS NOT NULL
                    AND loc.id_bed IS NULL
                ORDER BY loc.description ASC, NAMA_KELAS_IHS ASC";
		$bind = $this->db->query($query);
		return $bind;
	}

    function sendServiceRequest()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_ranap dsrr
				WHERE dsrr.NOPEN NOT IN (select es.nopen from ihs.tb_service_request_ranap es where es.status = 1 
                AND es.jenis = 'MASUK RANAP') AND dsrr.STATUS = 1 
				AND (dsrr.HTTPCODE IS NULL)
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

    function sendEncounter()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_encounter_ranap der
				WHERE der.NOPEN NOT IN (select es.nopen from ihs.tb_encounter es where es.status = 1 
				AND es.jenis = 3) AND der.STATUS = 1 
				AND (der.HTTPCODE IS NULL)
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

    // function sendTingkatKesadaran()
	// {
	// 	$query = "SELECT
	// 				* 
	// 			FROM
	// 				data_ihs.data_careplan_ranap dcr
	// 			WHERE dcr.NOPEN NOT IN (select tcr.nopen from ihs.tb_careplan_ranap tcr where tcr.status = 1) AND dcr.STATUS = 1 
	// 			AND (dcr.HTTPCODE IS NULL OR dcr.HTTPCODE != 201)
	// 			LIMIT 100";
	// 	$bind = $this->db->query($query);
	// 	return $bind;
	// }

    function sendCarePlan()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_careplan_ranap dcr
				WHERE dcr.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

    function dataEncouterRanap()
	{
		$query = "SELECT ps.ID_IHS ID_IHS_PASIEN
                , pas.NAMA NAMA_PASIEN
                , en.encounter ENCOUNTER
                , dok.ID_IHS ID_IHS_DOKTER
                , master.getNamaLengkapPegawai(dok.NIP_SIMPEL) NAMA_DOKTER
                , nak.ID_IHS ID_IHS_PERAWAT
                , nak.NAMA NAMA_PERAWAT
                , md.KODE ICD10
                , (SELECT mr.STR
                        FROM master.mrconso mr
                        WHERE mr.CODE = TRIM(md.KODE) AND mr.SAB='ICD10_1998' AND TTY IN ('PX', 'PT') AND md.NOPEN = df.NOMOR
                    LIMIT 1) DESKRIPSI_ICD10
                , CASE
                    WHEN en.jenis = 1 THEN 'Rawat Jalan'
                    WHEN en.jenis = 2 THEN 'IGD'
                    END JENIS
                , pr.created_at CREATED_AT
                , df.NOMOR NOPEN
                
                FROM pendaftaran.pendaftaran df
                
                LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
                LEFT JOIN medis.tb_permintaan_rawat pr ON pr.kunjungan = kun.NOMOR
                LEFT JOIN ihs.tb_encounter en ON en.nopen = df.NOMOR
                LEFT JOIN ihs.tb_dokter dok ON dok.ID_IHS = en.id_ihs_practitioner
                LEFT JOIN ihs.tb_pasien ps ON ps.ID_IHS = en.id_ihs_pasien
                LEFT JOIN master.pasien pas ON pas.NORM = ps.NORM
                LEFT JOIN ihs.tb_nakes nak ON nak.ID_SIMPEL = pr.oleh
                LEFT JOIN medicalrecord.diagnosa md ON md.NOPEN = df.NOMOR AND md.STATUS = 1 AND md.VERSI = ? AND md.ID IS NOT NULL
                
                WHERE ps.ID_IHS IS NOT NULL
                AND pr.id IS NOT NULL
                AND en.encounter IS NOT NULL
                AND en.jenis IN (?, ?)
                AND md.KODE IS NOT NULL
                AND nak.ID_IHS IS NOT NULL
                AND kun.MASUK BETWEEN (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -100 DAY),' 00:00:00')) 
                AND (CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -100 DAY),' 23:59:59'))
                
                GROUP BY en.nopen";
		$bind = $this->db->query($query, array(5,1,2));
		return $bind;
	}
}
