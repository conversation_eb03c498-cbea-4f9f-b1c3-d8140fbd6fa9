<h1>Pengiriman Data Terkait Obat (Medication Dispense) Bius</h1>

<div class="row">
	<div class="col-6" style="background-color: #FED8B1;">
		<h4>Log : </h4>
		<?php 
$data = '{
    "resourceType": "MedicationDispense",
    "identifier": [
        {
            "system": "http://sys-ids.kemkes.go.id/prescription/*********",
            "use": "official",
            "value": "**********-6"
        },
        {
            "system": "http://sys-ids.kemkes.go.id/prescription-item/*********",
            "use": "official",
            "value": "**********-7"
        }
    ],
    "status": "completed",
    "category": {
        "coding": [
            {
                "system": "http://terminology.hl7.org/fhir/CodeSystem/medicationdispense-category",
                "code": "inpatient",
                "display": "Inpatient"
            }
        ]
    },
    "medicationReference": {
        "reference": "Medication/a96a34d9-7726-495e-ba78-99e325d2dddf"
    },
    "subject": {
        "reference": "Patient/P02185434517",
        "display": "IRSYAD FADLURRAHMAN"
    },
    "context": {
        "reference": "Encounter/4e9e6834-8eaa-4eb3-8076-1220cf60bb0b"
    },
    "performer": [
        {
            "actor": {
                "reference": "Practitioner/**********",
                "display": "NANI SUTARNI"
            }
        }
    ],
    "location": {
        "reference": "Location/ef46adb5-ce86-48e1-b11c-dc2681d62ba1",
        "display": "Instalasi Bedah Pusat"
    },
    "authorizingPrescription": [
        {
            "reference": "MedicationRequest/8093d64e-308a-41ce-abd5-8a0d2c0748f5"
        }
    ],
    "quantity": {
        "value": 1,
        "unit": "Ampule - unit of product usage",
        "system": "http://snomed.info/sct",
        "code": "*********"
    },
    "daysSupply": {
        "value": 1,
        "unit": "Day",
        "system": "http://unitsofmeasure.org",
        "code": "d"
    },
    "whenPrepared": "2022-11-08T12:50:00+00:00",
    "whenHandedOver": "2022-11-08T12:50:00+00:00",
    "dosageInstruction": [
        {
            "sequence": 1,
            "text": "1 dosis selama operasi",
            "additionalInstruction": [
                {
                    "text": "1 dosis selama operasi"
                }
            ],
            "patientInstruction": "1 dosis selama operasi",
            "timing": {
                "repeat": {
                    "frequency": 1,
                    "period": 2,
                    "periodUnit": "h"
                }
            },
            "doseAndRate": [
                {
                    "type": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type",
                                "code": "ordered",
                                "display": "Ordered"
                            }
                        ]
                    },
                    "doseQuantity": {
                        "value": 10,
                        "system": "http://unitsofmeasure.org",
                        "code": "mg/mL"
                    }
                }
            ]
        }
    ]
}';
				echo '<pre>' . $data . '</pre>';
			?>
	</div>
	<div class="col-6" style="background-color: #8FB31D;">
		<h4>Response : </h4>
		<?php 
$data = '{
    "authorizingPrescription": [
        {
            "reference": "MedicationRequest/8093d64e-308a-41ce-abd5-8a0d2c0748f5"
        }
    ],
    "category": {
        "coding": [
            {
                "code": "inpatient",
                "display": "Inpatient",
                "system": "http://terminology.hl7.org/fhir/CodeSystem/medicationdispense-category"
            }
        ]
    },
    "context": {
        "reference": "Encounter/4e9e6834-8eaa-4eb3-8076-1220cf60bb0b"
    },
    "daysSupply": {
        "code": "d",
        "system": "http://unitsofmeasure.org",
        "unit": "Day",
        "value": 1
    },
    "dosageInstruction": [
        {
            "additionalInstruction": [
                {
                    "text": "1 dosis selama operasi"
                }
            ],
            "doseAndRate": [
                {
                    "doseQuantity": {
                        "code": "mg/mL",
                        "system": "http://unitsofmeasure.org",
                        "value": 10
                    },
                    "type": {
                        "coding": [
                            {
                                "code": "ordered",
                                "display": "Ordered",
                                "system": "http://terminology.hl7.org/CodeSystem/dose-rate-type"
                            }
                        ]
                    }
                }
            ],
            "patientInstruction": "1 dosis selama operasi",
            "sequence": 1,
            "text": "1 dosis selama operasi",
            "timing": {
                "repeat": {
                    "frequency": 1,
                    "period": 2,
                    "periodUnit": "h"
                }
            }
        }
    ],
    "id": "54eed9d7-ffd2-4334-816d-fdf64eed786c",
    "identifier": [
        {
            "system": "http://sys-ids.kemkes.go.id/prescription/*********",
            "use": "official",
            "value": "**********-6"
        },
        {
            "system": "http://sys-ids.kemkes.go.id/prescription-item/*********",
            "use": "official",
            "value": "**********-7"
        }
    ],
    "location": {
        "display": "Instalasi Bedah Pusat",
        "reference": "Location/ef46adb5-ce86-48e1-b11c-dc2681d62ba1"
    },
    "medicationReference": {
        "reference": "Medication/a96a34d9-7726-495e-ba78-99e325d2dddf"
    },
    "meta": {
        "lastUpdated": "2024-01-18T07:12:45.019564+00:00",
        "versionId": "MTcwNTU2MTk2NTAxOTU2NDAwMA"
    },
    "performer": [
        {
            "actor": {
                "display": "NANI SUTARNI",
                "reference": "Practitioner/**********"
            }
        }
    ],
    "quantity": {
        "code": "*********",
        "system": "http://snomed.info/sct",
        "unit": "Ampule - unit of product usage",
        "value": 1
    },
    "resourceType": "MedicationDispense",
    "status": "completed",
    "subject": {
        "display": "IRSYAD FADLURRAHMAN",
        "reference": "Patient/P02185434517"
    },
    "whenHandedOver": "2022-11-08T12:50:00+00:00",
    "whenPrepared": "2022-11-08T12:50:00+00:00"
}';
				echo '<pre>' . $data . '</pre>';
			?>
	</div>
</div>