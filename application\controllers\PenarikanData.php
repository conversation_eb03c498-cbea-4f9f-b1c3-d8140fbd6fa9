<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PenarikanData extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('PenarikanDataModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  public function cekDataMeninggal()
  {
    $token = $this->getToken();
    echo $token;
  }
}
