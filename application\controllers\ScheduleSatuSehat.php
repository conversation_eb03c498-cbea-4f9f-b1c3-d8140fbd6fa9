<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ScheduleSatuSehat extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('IHSModel');
    $this->load->model('SatuSehatModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'VqmdfGG7hYcmObx4L0tHM2TrPVVYn9tK0XEDKOWlIaETTv8L','client_secret' => 'R02tdGsDhBiYqALBGlRvFZ4PQTZm82A6CA4H44wFp1NywzHMVKNmMQPdqfBGZPBl'
    ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  public function getTokenDev()
  {
    // START GET TOKEN
    $postDataArray = [
      'client_id' => 'fs2BJRXk1OgoRCIkZA6UPsAGdvv2lwPS0TI76WCXWxtyAHz7','client_secret' => '2VeU4CziJdKZxnfHLn7aw6kktOC4EAnVHKVlUIy3ZQetvjAyCfq6czKDjklfdNFk'
    ];

    $data = http_build_query($postDataArray);

    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/oauth2/v1/accesstoken?grant_type=client_credentials';

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_POST, true);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $obj1 = json_decode($response);
    return $obj1->access_token;
    // END GET TOKEN
  }

  public function hapusIsiTable()
  {
    // Hapus ihs.tb_log_encounter
    $this->SatuSehatModel->hapusIsiTable(1);

    // Hapus ....
    // $this->SatuSehatModel->hapusIsiTable(2);
  }

  public function inputIdIhsPasien()
  {
    // START GET ID IHS PASIEN
    $dataPasien = $this->SatuSehatModel->cariNikPasien();

    if(!empty($dataPasien)){
      foreach($dataPasien as $dp){
        $token = $this->getToken();
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.$dp['NIKBENAR'].'';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        $data = array(
          'NORM' => $dp['NORM_PASIEN'],
          'NIK' => $dp['NIKBENAR'],
          'ID_IHS' => isset($res->entry[0]->resource->id) ? $res->entry[0]->resource->id : NULL,
          'FLAG' => 1,
        );
        if($httpcode == "200"){
          if($dp['STATUS'] == 1){
            $this->db->where('tb_pasien.NORM', $dp['NORM_PASIEN']);
            $this->db->update('ihs.tb_pasien', $data);
          }else if($dp['STATUS'] == 2){
            $this->db->insert('ihs.tb_pasien', $data);
          }
        }
        echo $dp['NIKBENAR'] . ' - ' . $res->entry[0]->resource->id . '<br>';
      }
    }
    // END GET ID IHS PASIEN
  }

  public function cariPasienYangGagalGetIdIhsPasien()
  {
    $dataGet = $this->SatuSehatModel->cariPasienYangGagalGetIdIhsPasien();
      $token = $this->getToken();
    foreach($dataGet as $dp){
      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Patient';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $dp['LOG_MPI']);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if(isset($res->success)){
        if($res->success == true){
          $idIhsMPI = isset($res->data->patient_id) ? $res->data->patient_id : NULL;
          $simpanMPI = array(
            'ID_IHS'         => $idIhsMPI,
            'FLAG_MPI'         => 1,
            'RESPONSE_MPI'         => $response,
            'JENIS_CEK'         => 1,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Patient Resource Exists'){
          $idIhsMPI = isset($res->data->resourceID) ? $res->data->resourceID : NULL;
          $simpanMPI = array(
            'ID_IHS'         => $idIhsMPI,
            'FLAG_MPI'         => 1,
            'RESPONSE_MPI'         => $response,
            'JENIS_CEK'         => 1,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Citizen Not Exists'){
          $simpanMPI = array(
            'FLAG_MPI'         => 2,
            'RESPONSE_MPI'         => $response,
            'JENIS_CEK'         => 1,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Citizen Data Not Match'){
          $simpanMPI = array(
            'FLAG_MPI'         => 3,
            'RESPONSE_MPI'         => $response,
            'JENIS_CEK'         => 1,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }
      }else{
        $simpanMPI = array(
            'FLAG_MPI'         => 4,
            'RESPONSE_MPI'         => $response,
            'JENIS_CEK'         => 1,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);
      }
    }
  }

  public function inputIdIhsPractitioner()
  {
    // START GET ID IHS PRACTITIONER
    $dataPractitioner = $this->SatuSehatModel->cariNikPractitioner();

    if(!empty($dataPractitioner)){
      foreach($dataPractitioner as $dp){
        $token = $this->getToken();
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Practitioner?identifier=https://fhir.kemkes.go.id/id/nik|'.$dp['KTP'].'';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        $data = array(
          'ID_DOKTER' => $dp['ID'],
          'NIP_SIMPEL' => $dp['NIP_SIMPEL'],
          'NIP_SIMPEG' => $dp['NIP_SIMPEG'],
          'NIK' => $dp['KTP'],
          'FLAG' => 1,
          'ID_IHS' => isset($res->entry[0]->resource->id) ? $res->entry[0]->resource->id : NULL,
        );
        if($httpcode == "200"){
          if($dp['JENIS'] == 2){
            $this->db->where('tb_dokter.ID_DOKTER', $dp['ID']);
            $this->db->update('ihs.tb_dokter', $data);
          }else if($dp['JENIS'] == 1){
            $this->db->insert('ihs.tb_dokter', $data);
          }
        }
        echo $dp['KTP'] . ' - ' . $res->entry[0]->resource->id . '<br>';
      }
    }

    // END GET ID IHS PRACTITIONER
  }

  public function index()
  {
    $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Organization/10000187';

    $headers = array(
      "Accept: application/json",
      "Authorization: Bearer ".$this->session->userdata('token')." ",
    );

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    curl_close($cURL);
    $res = json_decode($response);

    $data = array(
      'response' => $res,
      'getPartOf' => $this->IHSModel->getPartOf(),
      'getRuangan' => $this->IHSModel->getRuangan(),
      'parameterLab' => $this->IHSModel->parameterLab(),
      'parameterLabDone' => $this->IHSModel->parameterLabDone(),
      'parameterLabTanpa' => $this->IHSModel->parameterLabTanpa(),
      'getMappingLoinc' => $this->IHSModel->getMappingLoinc()
    );
    
    if(isset($res->active)){
      $this->load->view('dashboard', $data);
    } else {
      $this->getToken();
    }
  }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
  }

  function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomString;
  }
//-------------------------------------------FASE 1----------------------------------------------------------
  // public function postEncounter()
  // {
  //   $dataKunjungan = $this->SatuSehatModel->Encounter();

  //   foreach($dataKunjungan as $data1){
  //     $token = $this->getToken();
  //     $namaPasien = trim(preg_replace('/\s+/', ' ', $data1['NAMAPASIEN']));
  //     // Tanggal Reg
  //     $datereg = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
  //     $tglReg = $datereg->format(DATE_ATOM);

  //     $data = '{
  //       "resourceType": "Encounter",
  //       "status": "arrived",
  //       "class": {
  //           "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
  //           "code": "AMB",
  //           "display": "ambulatory"
  //       },
  //       "subject": {
  //         "reference": "Patient/'.$data1['ID_IHS_KTP_PASIEN'].'",
  //         "display": "'.$namaPasien.'"
  //       },
  //       "participant": [
  //         {
  //             "type": [
  //                 {
  //                     "coding": [
  //                         {
  //                             "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
  //                             "code": "ATND",
  //                             "display": "attender"
  //                         }
  //                     ]
  //                 }
  //             ],
  //             "individual": {
  //                 "reference": "Practitioner/'.$data1['ID_IHS_KTP_DOKTER'].'",
  //                 "display": "'.$data1['DPJP'].'"
  //             }
  //         }
  //       ],
  //       "period": {
  //           "start": "'.$tglReg.'" ';
  //     $data .= '
  //       },
  //       "location": [
  //           {
  //               "location": {
  //                   "reference": "Location/'.$data1['IDLOCATION'].'",
  //                   "display": "'.$data1['UNITPELAYANAN'].'"
  //               }
  //           }
  //       ],
  //       "statusHistory": [
  //           {
  //               "status": "arrived",
  //               "period": {
  //                   "start": "'.$tglReg.'",
  //                   "end": "'.$tglReg.'"
  //               }
  //           }';
  //   $data .= '
  //       ],
  //       "serviceProvider": {
  //           "reference": "Organization/100025609"
  //       },
  //       "identifier": [
  //           {
  //               "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
  //               "value": "'.$data1['NOPEN'].'"
  //           }
  //       ]
  //     }';
    

  //     echo '<pre>' . $data . '</pre><br>';

  //     $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter';

  //     $cURL = curl_init();
  //     curl_setopt($cURL, CURLOPT_URL,$url);
  //     curl_setopt($cURL, CURLOPT_HEADER,false);
  //     curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
  //     curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
  //       "Content-Type: application/json",
  //       "Authorization: Bearer ".$token." "
  //     ));
  //     curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
  //     curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
  //     curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
  //     curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
  //     $response = curl_exec($cURL);
  //     $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
  //     curl_close($cURL);
  //     $res = json_decode($response);
  //     // echo $httpcode;
  //     if($httpcode == "201"){
  //       // echo 'Status Code : ' . $httpcode . '<br>';
  //       // echo '<pre>'.$response."</pre>";
  //       echo $res->id . ' - ' . $data1['NOPEN'] . '<br>';

  //       $simpanEncounter = array(
  //         'id_ihs_pasien' => $data1['ID_IHS_KTP_PASIEN'],
  //         'id_ihs_practitioner' => $data1['ID_IHS_KTP_DOKTER'],
  //         'encounter' => $res->id,
  //         'nopen' => $data1['NOPEN'],
  //         'jenis' => 1,
  //       );

  //       $this->db->insert('ihs.tb_encounter', $simpanEncounter);    
  //     }
  //       $simpanLogEncounter = array(
  //         'log'       => $data,
  //         'httpcode'       => $httpcode,
  //         'id_encounter' => $res->id,
  //         'response'       => $response,
  //         'jenis'       => 'POST',
  //       );
  //       $this->db->insert('ihs.tb_log_encounter', $simpanLogEncounter);
  //   }
  // }

  public function postEncounter()
  {
    $dataKunjungan = $this->SatuSehatModel->Encounter();

    foreach($dataKunjungan as $data1){
      $token = $this->getToken();
      $namaPasien = trim(preg_replace('/\s+/', ' ', $data1['NAMAPASIEN']));
      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
        "resourceType": "Encounter",
        "status": "arrived",
        "class": {
            "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
            "code": "AMB",
            "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/'.$data1['ID_IHS_KTP_PASIEN'].'",
          "display": "'.$namaPasien.'"
        },
        "participant": [
          {
              "type": [
                  {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                              "code": "ATND",
                              "display": "attender"
                          }
                      ]
                  }
              ],
              "individual": {
                  "reference": "Practitioner/'.$data1['ID_IHS_KTP_DOKTER'].'",
                  "display": "'.$data1['DPJP'].'"
              }
          }
        ],
        "period": {
            "start": "'.$tglReg.'" ';
      $data .= '
        },
        "location": [
            {
                "location": {
                    "reference": "Location/'.$data1['IDLOCATION'].'",
                    "display": "'.$data1['UNITPELAYANAN'].'"
                }
            }
        ],
        "statusHistory": [
            {
                "status": "arrived",
                "period": {
                    "start": "'.$tglReg.'",
                    "end": "'.$tglReg.'"
                }
            }';
    $data .= '
        ],
        "serviceProvider": {
            "reference": "Organization/100025609"
        },
        "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
                "value": "'.$data1['NOPEN'].'"
            }
        ]
      }';
    

      echo '<pre>' . $data . '</pre><br>';

      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);
      // echo $httpcode;
      if($httpcode == "201"){
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";
        // echo $res->id . ' - ' . $data1['NOPEN'] . '<br>';

        $simpanEncounter = array(
          'id_ihs_pasien' => $data1['ID_IHS_KTP_PASIEN'],
          'id_ihs_practitioner' => $data1['ID_IHS_KTP_DOKTER'],
          'encounter' => $res->id,
          'nopen' => $data1['NOPEN'],
          'jenis' => 1,
        );

        $this->db->insert('ihs.tb_encounter', $simpanEncounter);    
      }elseif($res->issue[0]->code == "duplicate"){
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter?identifier=http://sys-ids.kemkes.go.id/encounter/100025609|'.$data1['NOPEN'].'';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
      // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

        $simpanEncounter = array(
          'id_ihs_pasien' => $data1['ID_IHS_KTP_PASIEN'],
          'id_ihs_practitioner' => $data1['ID_IHS_KTP_DOKTER'],
          'encounter' => $res->entry[0]->resource->id,
          'nopen' => $data1['NOPEN'],
          'jenis' => 1,
        );

        $this->db->insert('ihs.tb_encounter', $simpanEncounter);
      }elseif($res->issue[0]->details->text == "reference_not_found"){
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Patient?identifier=https://fhir.kemkes.go.id/id/nik|'.$data1['NIK_PASIEN'].'';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
      // curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if(isset($res->entry[0]->resource->id)){
        $ubahIdIHSPasien = array(
          'ID_IHS' => $res->entry[0]->resource->id,
        );
        $this->db->where('tb_pasien.NORM', $data1['NORM_PASIEN']);
        $this->db->update('ihs.tb_pasien', $ubahIdIHSPasien);
      }

      }
        $simpanLogEncounter = array(
          'log'       => $data,
          'httpcode'       => $httpcode,
          'id_encounter' => isset($res->id) ? $res->id : $res->entry[0]->resource->id,
          'response'       => $response,
          'jenis'       => 'POST',
        );
        $this->db->insert('ihs.tb_log_encounter', $simpanLogEncounter);
    }
  }

  public function masterPasienIndex()
  {
    $dataPasien = $this->SatuSehatModel->dataPasien();
    foreach($dataPasien as $dp){
      $token = $this->getToken();
      $namaPasien_1 = trim(preg_replace('/\s+/', ' ', $dp['NAMA_PASIEN']));
      $namaPasien = str_replace('\\', '', str_replace('"', "'", $namaPasien_1));
      $data = '{
       "resourceType": "Patient",
       "identifier": [
       {
         "use": "official",
         "system": "https://fhir.kemkes.go.id/id/nik",
         "value": "'.$dp['NIK'].'"
       }
       ],
       "name": [
       {
         "use": "official",
         "text": "'.$namaPasien.'"
       }
       ],
       "birthDate": "'.$dp['TANGGAL_LAHIR'].'",
       "multipleBirthInteger": 0,
       "gender": "'.$dp['JENIS_KELAMIN'].'",
       "address": [
       {
         "use": "home",
         "line": [
         "'.str_replace('\\', '', $dp['ALAMAT']).'"
         ],
         "city": "' .$dp['KOTA']. '",
         "postalCode": "12345",
         "country": "ID",
         "extension": [
         {
           "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/administrativeCode",
           "extension": [
           {
             "url": "province",
             "valueCode": "'.$dp['KODE_PROVINSI'].'"
             },
             {
               "url": "city",
               "valueCode": "'.$dp['KODE_KOTA'].'"
               },
               {
                 "url": "district",
                 "valueCode": "'.$dp['KODE_KECAMATAN'].'"
                 },
                 {
                   "url": "village",
                   "valueCode": "'.$dp['KODE_KELURAHAN'].'"
                   },
                   {
                     "url": "rt",
                     "valueCode": "'.$dp['RT'].'"
                     },
                     {
                       "url": "rw",
                       "valueCode": "'.$dp['RW'].'"
                     }
                     ]
                   }
                   ]
                 }
                 ],
                 "extension": [
                 {
                   "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/birthPlace",
                   "valueAddress": {
                    "city": "'.$dp['TEMPAT_LAHIR'].'",
                    "country": "ID"
                  }
                  },
                  {
                   "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/citizenshipStatus",
                   "valueCode": "WNI"
                 }
                 ]
               }';
               // echo '<pre>' . $data . '<pre><br>';
               // $url = 'https://api-satusehat-dev.kemkes.go.id/fhir-r4/v1/Patient';
               $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Patient';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if(isset($res->success)){
        if($res->success == true){
          $idIhsMPI = isset($res->data->patient_id) ? $res->data->patient_id : NULL;
          $simpanMPI = array(
            'ID_IHS'         => $idIhsMPI,
            'FLAG_MPI'         => 1,
            'LOG_MPI'         => $data,
            'RESPONSE_MPI'         => $response,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Patient Resource Exists'){
          $idIhsMPI = isset($res->data->resourceID) ? $res->data->resourceID : NULL;
          $simpanMPI = array(
            'ID_IHS'         => $idIhsMPI,
            'FLAG_MPI'         => 1,
            'LOG_MPI'         => $data,
            'RESPONSE_MPI'         => $response,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Citizen Not Exists'){
          $simpanMPI = array(
            'FLAG_MPI'         => 2,
            'LOG_MPI'         => $data,
            'RESPONSE_MPI'         => $response,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }else if($res->success == false && $res->message == 'Citizen Data Not Match'){
          $simpanMPI = array(
            'FLAG_MPI'         => 3,
            'LOG_MPI'         => $data,
            'RESPONSE_MPI'         => $response,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);  
        }
      }else{
          $simpanMPI = array(
            'FLAG_MPI'         => 4,
            'LOG_MPI'         => $data,
            'RESPONSE_MPI'         => $response,
          );
          $this->db->where('tb_pasien.NIK', $dp['NIK']);
          $this->db->update('ihs.tb_pasien', $simpanMPI);
      }
     
      // echo '<pre>' . $data . '||<pre><br>';
      // echo '<pre>'.$response."</pre><br>";
      // echo '<hr>';
    }
  }

  public function postEncounterDataLama()
  {
    $dataKunjungan = $this->SatuSehatModel->EncounterDataLama();

    foreach($dataKunjungan as $data1){
      $token = $this->getToken();
      $namaPasien = trim(preg_replace('/\s+/', ' ', $data1['NAMAPASIEN']));
      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $data1['TGLREG']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
        "resourceType": "Encounter",
        "status": "arrived",
        "class": {
            "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
            "code": "AMB",
            "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/'.$data1['ID_IHS_KTP_PASIEN'].'",
          "display": "'.$namaPasien.'"
        },
        "participant": [
          {
              "type": [
                  {
                      "coding": [
                          {
                              "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                              "code": "ATND",
                              "display": "attender"
                          }
                      ]
                  }
              ],
              "individual": {
                  "reference": "Practitioner/'.$data1['ID_IHS_KTP_DOKTER'].'",
                  "display": "'.$data1['DPJP'].'"
              }
          }
        ],
        "period": {
            "start": "'.$tglReg.'" ';
      $data .= '
        },
        "location": [
            {
                "location": {
                    "reference": "Location/'.$data1['IDLOCATION'].'",
                    "display": "'.$data1['UNITPELAYANAN'].'"
                }
            }
        ],
        "statusHistory": [
            {
                "status": "arrived",
                "period": {
                    "start": "'.$tglReg.'",
                    "end": "'.$tglReg.'"
                }
            }';
    $data .= '
        ],
        "serviceProvider": {
            "reference": "Organization/100025609"
        },
        "identifier": [
            {
                "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
                "value": "'.$data1['NOPEN'].'"
            }
        ]
      }';
    

      // echo '<pre>' . $data . '</pre><br>';

      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter';

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if($httpcode == "201"){
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";
        echo $res->id . ' - ' . $data1['NOPEN'] . '<br>';

        $simpanEncounter = array(
          'id_ihs_pasien' => $data1['ID_IHS_KTP_PASIEN'],
          'id_ihs_practitioner' => $data1['ID_IHS_KTP_DOKTER'],
          'encounter' => $res->id,
          'nopen' => $data1['NOPEN'],
          'jenis' => 1,
        );

        $this->db->insert('ihs.tb_encounter', $simpanEncounter);    
      }
        $simpanLogEncounter = array(
          'log'       => $data,
          'httpcode'       => $httpcode,
          'id_encounter' => $res->id,
          'response'       => $response,
          'jenis'       => 'POST',
        );
        $this->db->insert('ihs.tb_log_encounter', $simpanLogEncounter);
    }
  }

  public function putEncounter()
  {

    $putEncounter = $this->SatuSehatModel->finishEncounter();
    foreach($putEncounter as $putEnc){
      $token = $this->getToken();
    $status = 1;

      // Tanggal Reg
      $datereg = date_create_from_format('Y-m-d H:i:s', $putEnc['TGLREG']);
      $tglReg = $datereg->format(DATE_ATOM);

      // Tanggal Keluar
      $datakeluar = date_create_from_format('Y-m-d H:i:s', $putEnc['TGLKELUAR']);
      $tglKeluar = $datakeluar->format(DATE_ATOM);

      $data = '{
        "resourceType": "Encounter",
        "id": "' . $putEnc['IDENCOUNTER'] . '",
        "identifier": [
          {
            "system": "http://sys-ids.kemkes.go.id/encounter/100025609",
            "value": "' . $putEnc['NOPEN'] . '"
          }
        ],
        "status": "finished",
        "class": {
          "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
          "code": "AMB",
          "display": "ambulatory"
        },
        "subject": {
          "reference": "Patient/'.$putEnc['ID_IHS_PASIEN'].'",
          "display": "' . $putEnc['PASIEN'] . '"
        },
        "participant": [
          {
            "type": [
              {
                "coding": [
                  {
                    "system": "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
                    "code": "ATND",
                    "display": "attender"
                  }
                ]
              }
            ],
            "individual": {
              "reference": "Practitioner/'.$putEnc['ID_IHS_PRACTITIONER'].'",
              "display": "' . $putEnc['DPJP'] . '"
            }
          }
        ],
        "period": {
          "start": "' . $tglReg . '",
          "end": "' . $tglKeluar . '"
        },
        "location": [
          {
            "location": {
              "reference": "Location/'.$putEnc['IDLOCATION'].'",
              "display": "'.$putEnc['RUANGAN'].'"
            }
          }
        ],';

        $condition = $this->SatuSehatModel->condition($putEnc['NOPEN']);
        if($condition->num_rows() > 0){
          $data .='"diagnosis": [';
          $status = 2;
          foreach($condition->result_array() as $con){
            echo $putEnc['PASIEN'] . ' - ' . $putEnc['NOPEN'] . ' - ' . $con['JENIS'] . ' - ' . $con['DIAGNOSA_DESKRIPSI'] . '<br>';
            $data .='{
                "condition": {
                  "reference": "Condition/'.$con['IDCONDITION'].'",
                  "display": "'.$con['DIAGNOSA_DESKRIPSI'].'"
                },
                "use": {
                  "coding": [
                    {
                      "system": "http://terminology.hl7.org/CodeSystem/diagnosis-role",
                      "code": "DD",
                      "display": "Discharge diagnosis"
                    }
                  ]
                },
                "rank": '.$con['JENIS'].'
              },';
          }
          $data .='],';
        }
          echo '<hr>';

        $data .='"statusHistory": [
          {
            "status": "arrived",
            "period": {
              "start": "' . $tglReg . '",
              "end": "' . $tglReg . '"
            }
          },
          {
            "status": "finished",
            "period": {
              "start": "' . $tglKeluar . '",
              "end": "' . $tglKeluar . '"
            }
          }
        ],
        "serviceProvider": {
          "reference":"Organization/100025609"
        }
      }';

      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Encounter/'.$putEnc["IDENCOUNTER"].'';

    $data = str_replace(',]', ']', $data);

    $cURL = curl_init();
    curl_setopt($cURL, CURLOPT_URL,$url);
    curl_setopt($cURL, CURLOPT_HEADER,false);
    curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
    curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/json",
      "Authorization: Bearer ".$token." "
    ));
    curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'PUT');
    curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
    curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
    $response = curl_exec($cURL);
    $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
    curl_close($cURL);
    $res = json_decode($response);

    if($httpcode == "200"){
      // echo 'Status Code : ' . $httpcode . '<br>';
      // echo '<pre>'.$response."</pre>";
      // echo $putEnc['NOPEN'] . '<br>';
      // echo $putEnc['IDENCOUNTER'] . ' - ' . $putEnc['NOPEN'] . '<br>';

      $ubahEncounter = array(
        'status'         => $status,
      );
      $this->db->where('tb_encounter.nopen', $putEnc['NOPEN']);
      $this->db->update('ihs.tb_encounter', $ubahEncounter);      
    }
    $simpanLog = array(
      'id_encounter'   => $putEnc['IDENCOUNTER'],
      'log'            => $data,
      'response'       => $response,
      'httpcode'       => $httpcode,
      'jenis'          => 'PUT',
    );
    $this->db->insert('ihs.tb_log_encounter', $simpanLog);
    }
  }

  public function postCondition()
  {

    $pendaftaran = $this->SatuSehatModel->cekEncounter();
    foreach($pendaftaran as $pendaf){
      $token = $this->getToken();
        $data = '{
            "resourceType": "Condition",
            "clinicalStatus": {
                "coding": [
                  {
                      "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
                      "code": "active",
                      "display": "Active"
                  }
                ]
            },
            "category": [
                {
                  "coding": [
                      {
                        "system": "http://terminology.hl7.org/CodeSystem/condition-category",
                        "code": "encounter-diagnosis",
                        "display": "Encounter Diagnosis"
                      }
                  ]
                }
            ],
            "code": {
                "coding": [
                  {
                      "system": "http://hl7.org/fhir/sid/icd-10",
                      "code": "'.$pendaf['DIAGNOSA'].'",
                      "display": "'.$pendaf['DIAGNOSA_DESKRIPSI'].'"
                  }
                ]
            },
            "subject": {
                "reference": "Patient/' . $pendaf['ID_IHS_PASIEN'] . '",
                "display": "' . $pendaf['NAMAPASIEN'] . '"
            },
            "encounter": {
                "reference": "Encounter/' . $pendaf['IDENCOUNTER'] . '",
                "display": "insert condition"
            }
          }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Condition';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        if($httpcode == "201"){
          // echo 'Status Code : ' . $httpcode . '<br>';
          // echo '<pre>'.$response."</pre>";
          echo $res->id . ' - ' . $pendaf['NOPEN'] . '<br>';
          $simpanCondition = array(
            'id_encounter'        => $pendaf['IDENCOUNTER'],
            'id_condition'        => $res->id,
            'id_diagnosa'         => $pendaf['ID_DIAGNOSA'],
            'jenis'               => $pendaf['JENIS'],
            'diagnosa_deskripsi'  => $pendaf['DIAGNOSA_DESKRIPSI'],
            'kode_diagnosa'       => $pendaf['DIAGNOSA'],
            'nopen'               => $pendaf['NOPEN'],
            'status'              => 1,
          );
          $this->db->insert('ihs.tb_condition', $simpanCondition);      
        }
        $simpanLog = array(
          'id_encounter'   => $pendaf['IDENCOUNTER'],
          'id_condition'   => $res->id,
          'log'            => $data,
          'response'       => $response,
          'http_code'      => $httpcode,
        );
        $this->db->insert('ihs.tb_log_condition', $simpanLog);
    }
  }
  //-------------------------------------------------END FASE 1-----------------------------------------------

  //----------------------------------------------------FASE 2------------------------------------------------
  public function postObservation()
  {
    $cekObservation = $this->SatuSehatModel->cekObservation();

    if(!empty($cekObservation)){
      foreach($cekObservation as $cekObservation) {
        $observation = $this->SatuSehatModel->observation($cekObservation['NOPEN']);
        echo '----------------------------------------------------<br><pre>';
        if(!empty($observation)){
          $token = $this->getToken();
          $tgl = date_create_from_format('Y-m-d H:i:s', $observation['created_at']);
          $tglReg = $tgl->format(DATE_ATOM);
        // START NADI
        if(isset($observation['nadi'])) {
        $datanadi = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8867-4",
                      "display": "Heart rate"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/'.$observation['ID_IHS'].'"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['IDENCOUNTER'].'",
              "display": "Pemeriksaan Nadi"
          },
          "effectiveDateTime": "'.$tglReg.'",
          "valueQuantity": {
              "value": '.$observation['nadi'].',
              "unit": "beats/minute",
              "system": "http://unitsofmeasure.org",
              "code": "/min"
          }
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datanadi);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // echo 'NADI - ' . $datanadi;
        if($httpcode == "201"){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>";
          echo $observation['nopen'] . ' - NADI<br>';
          $simpanNadi = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['IDENCOUNTER'],
            'kategori' => "nadi"
          );
          $this->db->insert('ihs.tb_observation', $simpanNadi);      
        }

        $simpanLog = array(
          'id_encounter'   => $cekObservation['IDENCOUNTER'],
          'id_observation' => $res->id,
          'log'            => $datanadi,
          'response'       => $response,
          'http_code'      => $httpcode,
          'kategori'       => 'NADI',
        );
        $this->db->insert('ihs.tb_log_observation', $simpanLog);
      }
        // END NADI

        // START PERNAPASAN
        if(isset($observation['pernapasan'])) {
          // $token = $this->getToken();
        $datapernapasan = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "9279-1",
                      "display": "Respiratory rate"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/'.$observation['ID_IHS'].'"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['IDENCOUNTER'].'",
              "display": "Pemeriksaan Pernapasan"
          },
          "effectiveDateTime": "'.$tglReg.'",
          "valueQuantity": {
              "value": '.$observation['pernapasan'].',
              "unit": "breaths/minute",
              "system": "http://unitsofmeasure.org",
              "code": "/min"
          }
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datapernapasan);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
         // echo  'PERNAPASAN - ' . $datapernapasan;
        if($httpcode == "201"){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>";
          echo $observation['nopen'] . ' - PERNAPASAN<br>';

          $simpanPernapasan = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['IDENCOUNTER'],
            'kategori' => "pernapasan"
          );
          $this->db->insert('ihs.tb_observation', $simpanPernapasan);      
        }

        $simpanLog = array(
          'id_encounter'   => $cekObservation['IDENCOUNTER'],
          'id_observation' => $res->id,
          'log'            => $datapernapasan,
          'response'       => $response,
          'http_code'      => $httpcode,
          'kategori'       => 'PERNAPASAN',
        );
        $this->db->insert('ihs.tb_log_observation', $simpanLog);
      } 
        // END PERNAPASAN 

      // START TEKANAN DARAH (Sistolik)
      if(isset($observation['tekanan_darah'])) {
        // $token = $this->getToken();
        $datasistole = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8480-6",
                      "display": "Systolic blood pressure"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/'.$observation['ID_IHS'].'"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['IDENCOUNTER'].'",
              "display": "Pemeriksaan Sistolik"
          },
          "effectiveDateTime": "'.$tglReg.'",
          "valueQuantity": {
              "value": '.$observation['tekanan_darah'].',
              "unit": "mm[Hg]",
              "system": "http://unitsofmeasure.org",
              "code": "mm[Hg]"
          }
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasistole);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // echo 'SISTOLIK - ' . $datasistole;
        if($httpcode == "201"){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>";
          echo $observation['nopen'] . ' - SISTOLIK<br>';

          $simpanSistole = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['IDENCOUNTER'],
            'kategori' => "sistole"
          );
          $this->db->insert('ihs.tb_observation', $simpanSistole);     
        }

        $simpanLog = array(
          'id_encounter'   => $cekObservation['IDENCOUNTER'],
          'id_observation' => $res->id,
          'log'            => $datasistole,
          'response'       => $response,
          'http_code'      => $httpcode,
          'kategori'       => 'SISTOLIK',
        );
        $this->db->insert('ihs.tb_log_observation', $simpanLog);

      }
      // END TEKANAN DARAH (Sistolik)

      // START TEKANAN DARAH (Diastolik)
      if(isset($observation['per_tekanan_darah'])) {
        // $token = $this->getToken();
        $datadiastol = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8462-4",
                      "display": "Diastolic blood pressure"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/'.$observation['ID_IHS'].'"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['IDENCOUNTER'].'",
              "display": "Pemeriksaan Diastolik"
          },
          "effectiveDateTime": "'.$tglReg.'",
          "valueQuantity": {
              "value": '.$observation['per_tekanan_darah'].',
              "unit": "mm[Hg]",
              "system": "http://unitsofmeasure.org",
              "code": "mm[Hg]"
          }
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datadiastol);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // echo 'DIASTOLIK - ' . $datadiastol;
        if($httpcode == "201"){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>";
          echo $observation['nopen'] . ' - DIASTOLIK<br>';

          $simpanDiastol = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['IDENCOUNTER'],
            'kategori' => "diastol"
          );
          $this->db->insert('ihs.tb_observation', $simpanDiastol);    
        }

        $simpanLog = array(
          'id_encounter'   => $cekObservation['IDENCOUNTER'],
          'id_observation' => $res->id,
          'log'            => $datadiastol,
          'response'       => $response,
          'http_code'      => $httpcode,
          'kategori'       => 'DIASTOLIK',
        );
        $this->db->insert('ihs.tb_log_observation', $simpanLog);
      }
      // END TEKANAN DARAH (Diastolik)

      // START SUHU
      if(isset($observation['suhu'])) {
        // $token = $this->getToken();
        $datasuhu = '{
          "resourceType": "Observation",
          "status": "final",
          "category": [
              {
                  "coding": [
                      {
                          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                          "code": "vital-signs",
                          "display": "Vital Signs"
                      }
                  ]
              }
          ],
          "code": {
              "coding": [
                  {
                      "system": "http://loinc.org",
                      "code": "8310-5",
                      "display": "Body temperature"
                  }
              ]
          },
          "subject": {
              "reference": "Patient/'.$observation['ID_IHS'].'"
          },
          "encounter": {
              "reference": "Encounter/'.$cekObservation['IDENCOUNTER'].'",
              "display": "Pemeriksaan Suhu Tubuh"
          },
          "effectiveDateTime": "'.$tglReg.'",
          "valueQuantity": {
              "value": '.$observation['suhu'].',
              "unit": "C",
              "system": "http://unitsofmeasure.org",
              "code": "Cel"
          }
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $datasuhu);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // echo 'SUHU - ' . $datasuhu;
        if($httpcode == "201"){
          echo 'Status Code : ' . $httpcode . '<br>';
          echo '<pre>'.$response."</pre>";
          echo $observation['nopen'] . ' - SUHU<br>';

          $simpanSuhu = array(
            'nopen' => $observation['nopen'],
            'observation' => $res->id,
            'id_encounter' => $cekObservation['IDENCOUNTER'],
            'kategori' => "suhu"
          );
          $this->db->insert('ihs.tb_observation', $simpanSuhu);    
        }

        $simpanLog = array(
          'id_encounter'   => $cekObservation['IDENCOUNTER'],
          'id_observation' => $res->id,
          'log'            => $datasuhu,
          'response'       => $response,
          'http_code'      => $httpcode,
          'kategori'       => 'SUHU',
        );
        $this->db->insert('ihs.tb_log_observation', $simpanLog);

      }
      // END SUHU
      }else{
        // $simpanKosong = array(
        //   'nopen' => $cekObservation['NOPEN'],
        //   'observation' => 'tidak ada data - ' . $cekObservation['NOPEN'],
        //   'id_encounter' => $cekObservation['IDENCOUNTER'],
        //   'kategori' => "tidak ada data",
        //   'status' => "0",
        // );
        // $this->db->insert('ihs.tb_observation', $simpanKosong);
    }
    }
    }
  }

  public function postComposition()
  {
    $composition = $this->SatuSehatModel->composition();

      foreach($composition as $composition) {
        $datereg = date_create_from_format('Y-m-d H:i:s', $composition['TANGGAL']);
        $tglReg = $datereg->format(DATE_ATOM);

        $token = $this->getToken();
        $data = '{
          "resourceType": "Composition",
          "identifier": {
            "system": "http://sys-ids.kemkes.go.id/composition/100025609",
            "value": "'.$composition['NOPEN'].'"
          },
          "status": "final",
          "type": {
            "coding": [ 
              {
                "system": "http://loinc.org",
                "code": "18842-5",
                "display": "Discharge summary"
              } 
            ]
          },
          "subject": {
            "reference": "Patient/'.$composition['ID_IHS_PASIEN'].'",
            "display": "'.$composition['NAMA_PASIEN'].'"
          },
          "encounter": {
            "reference": "Encounter/'.$composition['ID_ENCOUNTER'].'",
            "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
          },
          "date": "'.$tglReg.'",
          "author": [
            {
              "reference": "Practitioner/'.$composition['ID_IHS_DOKTER'].'",
              "display": "'.$composition['NAMA_DOKTER'].'"
            } 
          ],
          "title": "Resume Medis Rawat Jalan",
          "custodian": {
            "reference": "Organization/100025609"
          },
          "section": [
            {
              "code": {
                "coding": [
                  {
                    "system": "http://loinc.org",
                    "code": "42344-2",
                    "display": "Discharge diet (narrative)"
                  } 
                ]
              }, 
              "text": {
                "status": "additional",';
        if(isset($composition['JENIS_DIET'])){
          $data .= '"div": "'.$composition['DIET'].', '.$composition['JENIS_DIET'].'" ';
        } else {
          $data .= '"div": "'.$composition['DIET'].'"';
        }
        $data .='
              }
            } 
          ]
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Composition';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        echo '<pre>'.$data."</pre>";
          if($httpcode == "201"){
            // echo 'Status Code : ' . $httpcode . '<br>';
            // echo '<pre>'.$data."</pre>";
            // echo $composition['NOPEN'] . '<br>';

            $simpancomposition = array(
              'nopen' => $composition['NOPEN'],
              'composition' => $res->id,
              'id_encounter' => $composition['ID_ENCOUNTER']
            );
            $this->db->insert('ihs.tb_composition', $simpancomposition);      
          }
          $simpanLog = array(
            'id_encounter'   => $composition['ID_ENCOUNTER'],
            'id_composition' => $res->id,
            'log'            => $data,
            'response'       => $response,
            'http_code'      => $httpcode,
          );
          $this->db->insert('ihs.tb_log_composition', $simpanLog);
      }
  }

  public function postProcedure()
  {
      $procedure = $this->SatuSehatModel->procedure();
        foreach($procedure as $proc){
          $token = $this->getToken();
          $data = '{
            "resourceType": "Procedure",
            "status": "completed",
            "code": {
                "coding": [
                    {
                        "system": "http://hl7.org/fhir/sid/icd-9-cm",
                        "code": "'.$proc['KODE'].'",
                        "display": "'.$proc['DESKRIPSI'].'"
                    }
                ]
            },
            "subject": {
                "reference": "Patient/'.$proc['IDPASIEN'].'",
                "display": "'.$proc['NAMAPASIEN'].'"
            },
            "encounter": {
                "reference": "Encounter/'.$proc['IDENCOUNTER'].'",
                "display": "Insert Procedure"
            }
          }';
        echo '<pre>' . $data . '</pre><br>';

      // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Procedure'; //DEV
      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Procedure'; //PROD
      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if($httpcode == "201"){
        echo $res->id . ' - ' . $proc['IDPROSEDUR'];
        $simpanProcedure = array(
          'id_procedure_ihs'   => $res->id,
          'nopen'              => $proc['NOPEN'],
          'id_encounter'       => $proc['IDENCOUNTER'],
          'prosedur'           => $proc['IDPROSEDUR'],
          'status'             => 1,
        );
        $this->db->insert('ihs.tb_procedure', $simpanProcedure);
      }

      $simpanLog = array(
        'id_encounter'   => $proc['IDENCOUNTER'],
        'id_procedure'   => $res->id,
        'log'            => $data,
        'response'       => $response,
        'http_code'      => $httpcode,
      );
      $this->db->insert('ihs.tb_log_procedure', $simpanLog);
      }
  }
  //--------------------------------------------END FASE 2-----------------------------------------------------

  //-----------------------------------------------FASE 3------------------------------------------------------
  public function postMedicationRequest()
  {

    $medicationDokter = $this->SatuSehatModel->medicationDokter();
    foreach($medicationDokter as $dataMed){
      $token = $this->getToken();
      $datereg = date_create_from_format('Y-m-d H:i:s', $dataMed['TANGGAL_RESEP']);
      $tglReg = $datereg->format(DATE_ATOM);

      $data = '{
          "resourceType": "Medication",
          "meta": {
              "profile": [
                  "https://fhir.kemkes.go.id/r4/StructureDefinition/Medication"
              ]
          },
          "identifier": [
              {
                  "system": "http://sys-ids.kemkes.go.id/medication/100025609",
                  "use": "official",
                  "value": "' . $dataMed['ID_OBAT'] . '"
              }
          ],
          "code" : {
           "coding" : [
              { 
                  "system" : "http://sys-ids.kemkes.go.id/kfa" ,
                  "code" : "'.$dataMed['KODE_KFA'].'" ,
                  "display" : "'.$dataMed['NAMA_OBAT'].'" 
              } 
            ] 
          },
          "status": "'.$dataMed['KET_STATUS'].'",
          "manufacturer": {
            "reference": "Organization/100025609"
          },            
          "form": {
              "coding": [
                  {
                      "system": "http://terminology.kemkes.go.id/CodeSystem/medication-form",
                      "code": "'.$dataMed['CODE_SEDIAAN_SATU_SEHAT'].'",
                      "display": "'.$dataMed['SEDIAAN_SATUSEHAT'].'"
                  }
              ]
          },
          "extension": [
              {
                  "url": "https://fhir.kemkes.go.id/r4/StructureDefinition/MedicationType",
                  "valueCodeableConcept": {
                      "coding": [
                          {
                              "system": "http://terminology.kemkes.go.id/CodeSystem/medication-type",
                              "code": "'.$dataMed['KODE_RACIKAN'].'",
                              "display": "'.$dataMed['KODE_RACIKAN_KET'].'"
                          }
                      ]
                  }
              }
              ]
        }';

        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Medication';
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        $simpanLogMedication = array(
          'log'       => $data,
          'http_code'       => $httpcode,
          'id_encounter' => $dataMed['ID_ENCOUNTER'],
          'id_medication' => $res->id,
          'response'       => $response,
          'jenis'       => "REQUEST",
        );
        $this->db->insert('ihs.tb_log_medication', $simpanLogMedication);

        if($httpcode == "201"){
          $data1 = '{
             "resourceType": "MedicationRequest",
             "status": "completed",
             "intent": "order",
             "category": [
                 {
                     "coding": [
                         {
                             "system": "http://terminology.hl7.org/CodeSystem/medicationrequest-category",
                             "code": "outpatient",
                             "display": "Outpatient"
                         }
                     ]
                 }
             ],
             "medicationReference": {
                 "reference": "Medication/'.$res->id.'",
                 "display": "Medication Request"
             },
             "subject": {
                 "reference": "Patient/'.$dataMed['ID_IHS_PASIEN'].'",
                 "display": "'.$dataMed['NAMA_PASIEN'].'"
             },
             "encounter": {
                 "reference": "Encounter/'.$dataMed['ID_ENCOUNTER'].'"
             },
             "authoredOn": "'.$tglReg.'",
             "requester": {
                 "reference": "Practitioner/'.$dataMed['ID_PRACTITIONER'].'",
                 "display": "'.$dataMed['CREATED_BY'].'"
             },
             "dosageInstruction": [
                 {
                     "sequence": 1,
                     "text": "'.$dataMed['ATURAN_PAKAI_TEXT'].'",
                     "timing": {
                         "repeat": {
                             "frequency": 1,
                             "period": 1,
                             "periodUnit": "d"
                         }
                     },
                     "route": {
                         "coding": [
                             {
                                 "system": "http://www.whocc.no/atc",
                                 "code": "O",
                                 "display": "Oral"
                             }
                         ]
                     },
                     "doseAndRate": [
                         {
                             "doseQuantity": {
                                 "value": '.$dataMed['JUMLAH_OBAT_INT2'].',
                                 "unit": "'.$dataMed['SEDIAAN_SATUSEHAT'].'",
                                 "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                                 "code": "'.$dataMed['SEDIAAN_SATUSEHAT'].'"
                             }
                         }
                     ]
                 }
             ],
             "dispenseRequest": {
                 "quantity": {
                     "value": '.$dataMed['JUMLAH_OBAT_INT2'].',
                     "unit": "'.$dataMed['SEDIAAN_SATUSEHAT'].'",
                     "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                     "code": "'.$dataMed['SEDIAAN_SATUSEHAT'].'"
                 }
             }
          }';

          $url1 = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/MedicationRequest';

          $cURL1 = curl_init();
        curl_setopt($cURL1, CURLOPT_URL,$url1);
        curl_setopt($cURL1, CURLOPT_HEADER,false);
        curl_setopt($cURL1, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL1, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL1, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL1, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL1, CURLOPT_POSTFIELDS, $data1);
        curl_setopt($cURL1, CURLOPT_CONNECTTIMEOUT,10);
        $response1 = curl_exec($cURL1);
        $httpcode1 = curl_getinfo($cURL1, CURLINFO_HTTP_CODE);
        curl_close($cURL1);
        $res1 = json_decode($response1);
        // echo '<pre>' . $data1 . '</pre><br>';

        if($httpcode1 == "201"){
          $simpanMedication = array(
              'id_medication_child' => $res1->id,
              'id_encounter' => $dataMed['ID_ENCOUNTER'],
              'id_medication' => $res->id,
              'id_obat' => $dataMed['ID_OBAT'],
              'nopen' => $dataMed['NOPEN'],
              'nokun' => $dataMed['NOKUN'],
              'jenis' => "REQUEST",
              'status' => 1,
            );

          $this->db->insert('ihs.tb_medication', $simpanMedication);
        }

        $ubahLogMedication = array(
          'id_medication_child' => $res1->id,
          'log_child'       => $data1,
          'response_child'       => $response1,
          'jenis'       => "REQUEST",
          'http_code_child' => $httpcode1,
        );

        $this->db->where('id_medication',  $res->id);
        $this->db->update('ihs.tb_log_medication', $ubahLogMedication);
        }
    }
  }

  public function postMedicationDispense()
  {
    $medicationDispense = $this->SatuSehatModel->medicationDispense();
    foreach($medicationDispense as $dMD){
      $token = $this->getToken();
      $dataMedication = '{
          "resourceType": "Medication",
          "meta": {
              "profile": [
                  "https://fhir.kemkes.go.id/r4/StructureDefinition/Medication"
              ]
          },';
          if($dMD['JENIS_RACIKAN'] == "Non-Racikan"){
            $dataMedication.='"identifier": [
                {
                    "system": "http://sys-ids.kemkes.go.id/medication/100025609",
                    "use": "official",
                    "value": "'.$dMD['ORDER_ID'].'"
                }
              ],';
              if($dMD['KFA_CODE3'] != '-' && $dMD['KFA_CODE2'] != '-'){
            $dataMedication .='"code": {
              "coding": [
                  {
                      "system": "http://sys-ids.kemkes.go.id/kfa",';
                      if($dMD['KFA_CODE3'] != '-'){
                        $dataMedication .='"code": "'.$dMD['KFA_CODE3'].'",';
                      }else{
                        $dataMedication .='"code": "'.$dMD['KFA_CODE2'].'",';
                      }
                      $dataMedication .='"display": "'.$dMD['NAMA_OBAT'].'"
                  }
              ]
          },';
        }
          }
          $dataMedication .='"status": "active",
          "manufacturer": {
              "reference": "Organization/100025609"
          },
          "form": {
              "coding": [
                  {
                      "system": "http://terminology.kemkes.go.id/CodeSystem/medication-form",
                      "code": "'.$dMD['CODE_SEDIAAN_SATU_SEHAT'].'",
                      "display": "'.$dMD['SEDIAAN_SATUSEHAT'].'"
                  }
              ]
          },';
          if($dMD['KFA_CODE1'] != '-' && $dMD['ID_SATUAN'] == 48){
            $dataMedication .='"ingredient": [
              {
                "itemCodeableConcept": {
                      "coding": [
                          {
                              "system": "http://sys-ids.kemkes.go.id/kfa",
                              "code": "'.$dMD['KFA_CODE1'].'",
                              "display": "'.$dMD['NAMA_OBAT'].'"
                          }
                      ]
                  },
                  "isActive": true,';
                  
                if($dMD['ID_SATUAN'] == 48){
                  $dataMedication .='"strength": {
                      "numerator": {
                          "value": '.$dMD['DOSIS_TERKECIL'].',
                          "system": "http://unitsofmeasure.org",
                          "code": "mg"
                      },
                      "denominator": {
                          "value": '.$dMD['JUMLAH_KONVERSI'].',
                          "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                          "code": "TAB"
                      }
                  }';
                }else{
                  $dataMedication .='"strength": {
                      "numerator": {
                          "value": '.$dMD['DOSIS_TERKECIL'].',
                          "system": "http://unitsofmeasure.org",
                          "code": "'.$dMD['NAMA_SATUAN_OBAT'].'"
                      },
                      "denominator": {
                          "value": '.$dMD['JUMLAH_KONVERSI'].',
                          "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                          "code": "'.$dMD['NAMA_KONVERSI'].'"
                      }
                  }';
                }
              $dataMedication .='}
          ],';
          }

          $dataMedication .='"extension": [
              {
                  "url": "http://fhir.kemkes.go.id/r4/StructureDefinition/MedicationType",
                  "valueCodeableConcept": {
                      "coding": [
                          {
                              "system": "http://terminology.kemkes.go.id/CodeSystem/medication-type",
                              ';
                              if($dMD['JENIS_RACIKAN'] == 'Non-Racikan') {
        $dataMedication .='
                              "code": "NC",
                              "display": "Non-compound"
                              ';
                            } else {
        $dataMedication .='
                              "code": "SD",
                              "display": "Gives of such doses"
                              ';
                            }
        $dataMedication .='
                          }
                      ]
                  }
              }
          ]
        }';
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Medication';

        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $dataMedication);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);

        $simpanLogMedication = array(
          'log'       => $dataMedication,
          'http_code'       => $httpcode,
          'id_encounter' => $dMD['IDENCOUNTER'],
          'id_medication' => $res->id,
          'response'       => $response,
          'jenis'       => "DISPENSE",
        );
        $this->db->insert('ihs.tb_log_medication', $simpanLogMedication);

        if($httpcode == "201"){
          $dataDispense = '{
            "resourceType": "MedicationDispense",
            "status": "completed",
            "category": {
                "coding": [
                    {
                        "system": "http://terminology.hl7.org/fhir/CodeSystem/medicationdispense-category",
                        "code": "outpatient",
                        "display": "Outpatient"
                    }
                ]
            },
            "medicationReference": {
                "reference": "Medication/'.$res->id.'",
                "display": "'.$dMD['NAMA_OBAT'].'"
            },
            "subject": {
                "reference": "Patient/'.$dMD['ID_IHS_PASIEN'].'",
                "display": "'.$dMD['NAMA_PASIEN'].'"
            },
            "context": {
                "reference": "Encounter/'.$dMD['IDENCOUNTER'].'"
            },
            "location": {
                "reference": "Location/'.$dMD['ID_IHS_RUANGAN'].'",
                "display": "'.$dMD['NAMA_IHS_RUANGAN'].'"
            },
            "quantity": {
                "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                "code": "TAB",
                "value": '.$dMD['JUMLAH_OBAT'].'
            },
            "dosageInstruction": [
                {
                    "sequence": 1,
                    "text": "'.$dMD['ATURAN_PAKAI_TEXT'].'",
                    "timing": {
                        "repeat": {
                            "frequency": 1,
                            "period": 1,
                            "periodUnit": "d"
                        }
                    },
                    "route": {
                         "coding": [
                             {
                                 "system": "http://www.whocc.no/atc",
                                 "code": "O",
                                 "display": "Oral"
                             }
                         ]
                     },
                    "doseAndRate": [
                        {
                            "doseQuantity": {
                                "value": '.$dMD['DOSIS_TERKECIL'].',
                                "unit": "TAB",
                                "system": "http://terminology.hl7.org/CodeSystem/v3-orderableDrugForm",
                                "code": "TAB"
                            }
                        }
                    ]
                }
            ]
          }';

          $url2 = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/MedicationDispense';

          $cURL2 = curl_init();
          curl_setopt($cURL2, CURLOPT_URL,$url2);
          curl_setopt($cURL2, CURLOPT_HEADER,false);
          curl_setopt($cURL2, CURLOPT_RETURNTRANSFER,true);
          curl_setopt($cURL2, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
          ));
          curl_setopt($cURL2, CURLOPT_SSL_VERIFYPEER, false);
          curl_setopt($cURL2, CURLOPT_CUSTOMREQUEST, 'POST');
          curl_setopt($cURL2, CURLOPT_POSTFIELDS, $dataDispense);
          curl_setopt($cURL2, CURLOPT_CONNECTTIMEOUT,10);
          $response2 = curl_exec($cURL2);
          $httpcode2 = curl_getinfo($cURL2, CURLINFO_HTTP_CODE);
          curl_close($cURL2);
          $res2 = json_decode($response2);

          if($httpcode2 == "201"){
          $simpanMedication = array(
              'id_medication_child' => $res2->id,
              'id_encounter' => $dMD['IDENCOUNTER'],
              'id_medication' => $res->id,
              'id_farmasi' => $dMD['IDFARMASI'],
              'nopen' => $dMD['NOPEN'],
              'nokun' => $dMD['NOKUN'],
              'jenis' => "DISPENSE",
              'status' => 1,
            );

          $this->db->insert('ihs.tb_medication', $simpanMedication);
        }

        $ubahLogMedication = array(
          'id_medication_child' => $res2->id,
          'log_child'       => $dataDispense,
          'response_child'       => $response2,
          'jenis'       => "DISPENSE",
          'http_code_child' => $httpcode2,
        );

        $this->db->where('id_medication',  $res->id);
        $this->db->update('ihs.tb_log_medication', $ubahLogMedication);

        }
    }
  }
  //--------------------------------------------END FASE 3-----------------------------------------------------

  //-----------------------------------------------FASE 4------------------------------------------------------
  // public function postServiceRequestLab(){
  //   $cekServiceRequest = $this->SatuSehatModel->cekServiceRequestLab();
  //   foreach($cekServiceRequest as $dataSer) {
  //     echo "<pre> [LAB]";print_r($dataSer);echo "</pre>";
      
  //     $serviceRequest = $this->SatuSehatModel->serviceRequest($dataSer['NOPEN']);
  //     foreach($serviceRequest as $serviceRequest) {

  //       $data = '{
  //         "resourceType": "ServiceRequest",
  //         "status": "active",
  //         "intent": "original-order",
  //         "code": {
  //           "coding": [
  //             {
  //             "system": "http://loinc.org",
  //             "code": "'.$serviceRequest['LOINC'].'",
  //             "display": "'.$serviceRequest['TINDAKAN'].'"
  //             }
  //           ],
  //           "text": "'.$serviceRequest['TINDAKAN'].'"
  //         },
  //         "subject": {
  //           "reference": "Patient/'.$dataSer['ID_IHS_PATIENT'].'"
  //         },
  //         "encounter": {
  //           "reference": "Encounter/'.$dataSer['ENCOUNTER'].'",
  //           "display": "Insert ServiceRequest"
  //         }
  //       }';
  //       echo "<pre> [SR]";print_r($data);echo "</pre>";

  //       $data1 = '{
  //         "resourceType": "Specimen",
  //         "type": {
  //           "coding": [
  //             {
  //               "system": "http://snomed.info/sct",
  //               "code": "45710003",
  //               "display": "'.$serviceRequest['SPECIMEN_DESK'].'"
  //             }
  //           ]
  //         },
  //         "subject": {
  //           "reference": "Patient/'.$dataSer['ID_IHS_PATIENT'].'",
  //           "display": "'.$dataSer['NAMA_PASIEN'].'"
  //         },
  //         "request": [
  //           {
  //             "reference": "ServiceRequest/'.$this->guidv4().'"
  //           }
  //         ]
  //       }';
  //       echo "<pre> [SPEC]";print_r($data1);echo "</pre>";
  //     }

  //     $observationDiagnosticReport = $this->SatuSehatModel->observationDiagnosticReport($dataSer['NOPEN']);
  //     foreach($observationDiagnosticReport as $observationDiagnosticReport){

  //       $data2 = '{
  //         "resourceType": "Observation",
  //         "identifier": [
  //           {
  //               "system": "http://sys-ids.kemkes.go.id/observation/100025609",
  //               "value": "'.$dataSer['NOPEN'].'"
  //           }
  //         ],
  //         "status": "final",
  //         "category": [
  //           {
  //             "coding": [
  //               {
  //                   "system":"http://terminology.hl7.org/CodeSystem/observation-category",
  //                   "code": "laboratory",
  //                   "display": "Laboratory"
  //               } 
  //             ]
  //           }
  //         ],
  //         "code": {
  //           "coding": [
  //             {
  //                 "system": "http://loinc.org",
  //                 "code": "'.$observationDiagnosticReport['LOINC'].'",
  //                 "display": "'.$observationDiagnosticReport['TINDAKAN'].'"
  //             }
  //           ]
  //         },
  //         "subject": {
  //             "reference": "Patient/'.$dataSer['ID_IHS_PATIENT'].'"
  //         },
  //         "encounter": {
  //             "reference": "Encounter/'.$dataSer['ENCOUNTER'].'"
  //         },
  //         "effectiveDateTime": "2021-07-02",
  //         "issued": "2022-08-07T15:30:10+01:00",
  //         "performer": [
  //             {
  //                 "reference": "Practitioner/'.$dataSer['ID_IHS_PRACTITIONER'].'"
  //             }, {
  //                 "reference": "Organization/100025609"
  //             }
  //         ],
  //         "specimen": {
  //             "reference": "Specimen/'.$observationDiagnosticReport['ID_IHS_SPECIMEN'].'"
  //         },
  //         "basedOn": [
  //           {
  //               "reference": "ServiceRequest/'.$observationDiagnosticReport['ID_SERVICE_REQUEST'].'"
  //           }
  //         ],
  //         "valueCodeableConcept": {
  //             "coding": [
  //               {
  //                   "system": "http://snomed.info/sct",
  //                   "code": "260347006",
  //                   "display": "'.$observationDiagnosticReport['HASIL'].'"
  //               } 
  //             ]
  //         },
  //         "referenceRange": [
  //           {
  //               "text" : "'.$observationDiagnosticReport['HASIL'].'"
  //           }
  //         ]
  //       }';
  //       echo "<pre> [OBS]";print_r($data2);echo "</pre>";
  //     }
  //   }
  // }

  public function postServiceRequestLab(){
    $dataLab = $this->SatuSehatModel->sendServiceRequestLab()->result_array();
    foreach($dataLab as $dl){
        $token = $this->getToken();
        date_default_timezone_set("UTC");
        $datelab = date_create_from_format('Y-m-d H:i:s', $dl['TGL_ORDER_LAB']);
        $tglLab = $datelab->format(DATE_ATOM);
        $data = '{
                  "resourceType": "ServiceRequest",
                  "identifier": [
                      {
                          "system": "http://sys-ids.kemkes.go.id/servicerequest/100025609",
                          "value": "'.$dl['ID_HASIL_LAB'].'"
                      }
                  ],
                  "status": "completed",
                  "intent": "order",
                  "priority": "routine",
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://snomed.info/sct",
                                  "code": "108252007",
                                  "display": "Laboratory procedure"
                              }
                          ]
                      }
                  ],
                  "code": {
                      "coding": [
                          {
                              "system": "http://loinc.org",
                              "code": "'.$dl['KODE_LOINC'].'",
                              "display": "'.$dl['DESK_LOINC'].'"
                          }
                      ]
                  },
                  "subject": {
                      "reference": "Patient/'.$dl['ID_IHS_PASIEN'].'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$dl['ENCOUNTER'].'"
                  },
                  "occurrenceDateTime": "'.$tglLab.'",
                  "authoredOn": "'.$tglLab.'",
                  "requester": {
                      "reference": "Practitioner/'.$dl['ID_IHS_DOKTER_REQUEST'].'",
                      "display": "'.$dl['NAMA_DOKTER_REQUEST'].'"
                  },
                  "performer": [
                      {
                          "reference": "Practitioner/'.$dl['ID_IHS_PERAWAT'].'",
                          "display": "'.$dl['NAMA_PERAWAT'].'"
                      }
                  ]
              }';
            // echo '<pre>' . $data . '</pre><br>';

        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //DEV
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //PROD
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        if($httpcode == "201"){
            $simpanServiceReq = array(
            'id_identifier'      => $dl['ID_HASIL_LAB'],
            'nopen'              => $dl['NOPEN'],
            'id_encounter'       => $dl['ENCOUNTER'],
            'id_ihs'             => $res->id,
            'jenis'              => 'LAB',
            );
            $this->db->insert('ihs.tb_service_request_rajal', $simpanServiceReq);
            // echo '<pre>'.print_r($simpanServiceReq)."</pre>";
        }

        date_default_timezone_set('Asia/Jakarta');
        $dataServiceRequestLabRAJAL = array (
            'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        );

        $this->db->where('ID_HASIL_LAB', $dl['ID_HASIL_LAB']);
        $this->db->update('data_ihs.data_service_request_lab_rajal', $dataServiceRequestLabRAJAL);

        $simpanLog = array(
            'id_identifier'   => $dl['ID_HASIL_LAB'],
            'nopen'          => $dl['NOPEN'],
            'id_encounter'   => $dl['ENCOUNTER'],
            'id_ihs'         => $res->id,
            'log'            => $data,
            'response'       => $response,
            'http_code'      => $httpcode,
            'jenis'          => 'LAB',
        );
        $this->db->insert('ihs.tb_log_service_request', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
    }
    // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
  }

  public function postSpecimenLab(){
    $dataLab = $this->SatuSehatModel->sendSpecimenLab()->result_array();
    foreach($dataLab as $dl){
        $token = $this->getToken();
        date_default_timezone_set("UTC");
        $datelabambil = date_create_from_format('Y-m-d H:i:s', $dl['TGL_DIAMBIL_SAMPEL']);
        $tglAmbilSampel = $datelabambil->format(DATE_ATOM);
        $datelabterima = date_create_from_format('Y-m-d H:i:s', $dl['TGL_TERIMA_SAMPEL']);
        $tglTerimaSampel = $datelabterima->format(DATE_ATOM);
        $data = '{
                  "resourceType": "Specimen",
                  "identifier": [
                      {
                          "system": "http://sys-ids.kemkes.go.id/specimen/100025609",
                          "value": "'.$dl['ID_HASIL_LAB'].'",
                          "assigner": {
                              "reference": "Organization/100025609"
                          }
                      }
                  ],
                  "status": "available",
                  "type": {
                      "coding": [
                          {
                              "system": "http://snomed.info/sct",
                              "code": "'.$dl['KODE_SNOMED'].'",
                              "display": "'.$dl['DESK_SNOMED'].'"
                          }
                      ]
                  },
                  "collection": {
                      "method": {
                          "coding": [
                              {
                                  "system": "http://snomed.info/sct",
                                  "code": "'.$dl['KODE_SNOMED_COLLECTION'].'",
                                  "display": "'.$dl['DESK_SNOMED_COLLECTION'].'"
                              }
                          ]
                      },
                      "collectedDateTime": "'.$tglAmbilSampel.'"
                  },
                  "subject": {
                      "reference": "Patient/'.$dl['ID_IHS_PASIEN'].'",
                      "display": "'.$dl['NAMA_PASIEN'].'"
                  },
                  "request": [
                      {
                          "reference": "ServiceRequest/'.$dl['ID_SERVICE_REQUEST'].'"
                      }
                  ],
                  "receivedTime": "'.$tglTerimaSampel.'"
              }';
            // echo '<pre>' . $data . '</pre><br>';

        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Specimen'; //DEV
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Specimen'; //PROD
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        if($httpcode == "201"){
            $simpanSpecimen = array(
            'id_identifier'      => $dl['ID_HASIL_LAB'],
            'nopen'              => $dl['NOPEN'],
            'id_encounter'       => $dl['ENCOUNTER'],
            'id_service_request' => $dl['ID_SERVICE_REQUEST'],
            'id_ihs'             => $res->id,
            'jenis'              => 'LAB',
            );
            $this->db->insert('ihs.tb_specimen_rajal', $simpanSpecimen);
            // echo '<pre>'.print_r($simpanSpecimen)."</pre>";
        }

        date_default_timezone_set('Asia/Jakarta');
        $dataSpecimenLabRAJAL = array (
            'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        );

        $this->db->where('ID_HASIL_LAB', $dl['ID_HASIL_LAB']);
        $this->db->update('data_ihs.data_specimen_lab_rajal', $dataSpecimenLabRAJAL);

        $simpanLog = array(
            'id_identifier'       => $dl['ID_HASIL_LAB'],
            'nopen'               => $dl['NOPEN'],
            'id_encounter'        => $dl['ENCOUNTER'],
            'id_service_request'  => $dl['ID_SERVICE_REQUEST'],
            'id_ihs'              => $res->id,
            'log'                 => $data,
            'response'            => $response,
            'http_code'           => $httpcode,
            'jenis'               => 'LAB',
        );
        $this->db->insert('ihs.tb_log_specimen', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
    }
    // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
  }
  //--------------------------------------------END FASE 4-----------------------------------------------------

  //-----------------------------------------------FASE 5------------------------------------------------------
  function getACSN(){
    $dataRad = $this->SatuSehatModel->getPacs()->result_array();
    foreach($dataRad as $dr){
      $cek = $this->SatuSehatModel->cekPacs($dr['ID_TINDAKAN_MEDIS']);
      if($cek->num_rows() > 0){
        $acsn = $cek->row_array();
        $dataACSN = array (
          'ACSN' => $acsn['accession_no'],
          'DCM' => $acsn['filepath'],
          'STATUS' => '2'
        );

       
      }else{
        $dataACSN = array (
          'STATUS' => '0'
        );
      }
      $this->db->where('ID_TINDAKAN_MEDIS', $dr['ID_TINDAKAN_MEDIS']);
      $this->db->update('data_ihs.data_service_request_rad_rajal', $dataACSN);
    }
  }

  public function postServiceRequestRad(){
    $dataRad = $this->SatuSehatModel->sendServiceRequestRad()->result_array();
    foreach($dataRad as $dr){
        $token = $this->getToken();
        date_default_timezone_set("UTC");
        $dateradorder = date_create_from_format('Y-m-d H:i:s', $dr['TGL_KIRIM_ORDER']);
        $tglOrder = $dateradorder->format(DATE_ATOM);
        $dateradreceive = date_create_from_format('Y-m-d H:i:s', $dr['TGL_TERIMA_ORDER']);
        $tglTerima = $dateradreceive->format(DATE_ATOM);
        $data = '{
                  "resourceType": "ServiceRequest",
                  "identifier": [
                      {
                          "system": "http://sys-ids.kemkes.go.id/servicerequest/100025609",
                          "value": "'.$dr['ID_TINDAKAN_MEDIS'].'"
                      },
                      {
                          "use": "usual",
                          "type": {
                              "coding": [
                                  {
                                      "system": "http://terminology.hl7.org/CodeSystem/v2-0203",
                                      "code": "ACSN"
                                  }
                              ]
                          },
                          "system": "http://sys-ids.kemkes.go.id/acsn/100025609",
                          "value": "'.$dr['ACSN'].'"
                      }
                  ],
                  "status": "completed",
                  "intent": "original-order",
                  "priority": "routine",
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://snomed.info/sct",
                                  "code": "363679005",
                                  "display": "Imaging"
                              }
                          ]
                      }
                  ],
                  "code": {
                      "coding": [
                          {
                              "system": "http://loinc.org",
                              "code": "'.$dr['KODE_LOINC'].'",
                              "display": "'.$dr['DESK_LOINC'].'"
                          }
                      ]
                  },
                  "subject": {
                      "reference": "Patient/'.$dr['ID_IHS_PASIEN'].'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$dr['ENCOUNTER'].'"
                  },
                  "occurrenceDateTime": "'.$tglTerima.'",
                  "authoredOn": "'.$tglOrder.'",
                  "requester": {
                      "reference": "Practitioner/'.$dr['ID_IHS_DOKTER_REQUEST'].'",
                      "display": "'.$dr['NAMA_DOKTER_REQUEST'].'"
                  },
                  "performer": [
                      {
                          "reference": "Practitioner/'.$dr['ID_IHS_NAKES'].'",
                          "display": "'.$dr['NAMA_NAKES'].'"
                      }
                  ],
                  "bodySite": [
                      {
                          "coding": [
                              {
                                  "system": "http://snomed.info/sct",
                                  "code": "'.$dr['KODE_SNOMED'].'",
                                  "display": "'.$dr['DESK_SNOMED'].'"
                              }
                          ]
                      }
                  ]
              }';
            // echo '<pre>' . $data . '</pre><br>';

        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //DEV
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/ServiceRequest'; //PROD
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        if($httpcode == "201"){
            $simpanServiceReq = array(
            'id_identifier'       => $dr['ID_TINDAKAN_MEDIS'],
            'nopen'              => $dr['NOPEN'],
            'id_encounter'       => $dr['ENCOUNTER'],
            'id_ihs'             => $res->id,
            'jenis'              => 'RAD',
            );
            $this->db->insert('ihs.tb_service_request_rajal', $simpanServiceReq);
            // echo '<pre>'.print_r($simpanServiceReq)."</pre>";
        }

        date_default_timezone_set('Asia/Jakarta');
        $dataServiceRequestRadRAJAL = array (
            'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '3' : '2',
        );

        $this->db->where('ID_TINDAKAN_MEDIS', $dr['ID_TINDAKAN_MEDIS']);
        $this->db->update('data_ihs.data_service_request_rad_rajal', $dataServiceRequestRadRAJAL);

        $simpanLog = array(
            'id_identifier'   => $dr['ID_TINDAKAN_MEDIS'],
            'nopen'          => $dr['NOPEN'],
            'id_encounter'   => $dr['ENCOUNTER'],
            'id_ihs'         => $res->id,
            'log'            => $data,
            'response'       => $response,
            'http_code'      => $httpcode,
            'jenis'          => 'RAD',
        );
        $this->db->insert('ihs.tb_log_service_request', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";
            // }
    }
    // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
  }

  function sendDicom(){
    $dataRad = $this->SatuSehatModel->sendDicom()->result_array();
    foreach($dataRad as $dr){
      //echo substr_replace($dr['DCM'], '*', -8, 10) . "<br />\n";
      $filedicom = substr_replace($dr['DCM'], '*', -8, 10);
      $curl = curl_init();

      curl_setopt_array($curl, array(
        CURLOPT_URL => '192.168.7.196:3000/send',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
          "fileLocation" : "'.$filedicom.'"
        }',
        CURLOPT_HTTPHEADER => array(
          'Content-Type: application/json'
        ),
      ));

      $response = curl_exec($curl);

      curl_close($curl);
      $res = json_decode($response);
      // echo $response;

      // if($res->success == true){
        date_default_timezone_set('Asia/Jakarta');
        $dataDicom = array (
            'STATUS'      => ($res->success == true) ? '4' : '422',
        );
        $this->db->where('ID_TINDAKAN_MEDIS', $dr['ID_TINDAKAN_MEDIS']);
        $this->db->update('data_ihs.data_service_request_rad_rajal', $dataDicom);
        // echo '<pre>'.print_r($simpanServiceReq)."</pre>";
      // }
    
      $simpanLog = array(
        'id_tindakan_medis' => $dr['ID_TINDAKAN_MEDIS'],
        'nopen'             => $dr['NOPEN'],
        'acsn'              => $dr['ACSN'],
        'dcm'               => $dr['DCM'],
        'response'          => $response,
      );
      $this->db->insert('data_ihs.tb_log_send_dicom', $simpanLog);
    }
  }

  function getImagingStudy(){
    $dataRad = $this->SatuSehatModel->getImagingStudyRad()->result_array();
    foreach($dataRad as $dr){
      $token = $this->getToken();
      // echo '<pre>'.$dr["ACSN"]."</pre>"; 
      $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/ImagingStudy?identifier=http://sys-ids.kemkes.go.id/acsn/100025609|'.$dr["ACSN"].'';
      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
          "Content-Type: application/json",
          "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'GET');
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
      curl_close($cURL);
      $res = json_decode($response);

      if($httpcode == "200"){
        $simpanImagingStudy = array(
          'id_tindakan_medis'  => $dr['ID_TINDAKAN_MEDIS'],
          'nopen'              => $dr['NOPEN'],
          'encounter'          => $dr['ENCOUNTER'],
          'imaging_study'      => $res->entry[0]->resource->id,
        );
        $this->db->insert('ihs.tb_imaging_study', $simpanImagingStudy);
        // echo '<pre>'.print_r($simpanImagingStudy)."</pre>";
      }

      date_default_timezone_set('Asia/Jakarta');
      $idStudy = $res->entry[0]->resource->id;
      $dataImagingStudy = array (
          'IMG_STUDY'   => (isset($idStudy)) ? $idStudy : NULL,
          'STATUS'      => (isset($idStudy)) ? '5' : '3',
      );
      $this->db->where('ID_TINDAKAN_MEDIS', $dr['ID_TINDAKAN_MEDIS']);
      $this->db->update('data_ihs.data_service_request_rad_rajal', $dataImagingStudy);

      $simpanLog = array(
          'id_tindakan_medis'   => $dr['ID_TINDAKAN_MEDIS'],
          'nopen'          => $dr['NOPEN'],
          'encounter'   => $dr['ENCOUNTER'],
          'imaging_study'         => $res->entry[0]->resource->id,
          // 'log'            => $data,
          'response'       => $response,
          'http_code'      => $httpcode,
      );
      $this->db->insert('ihs.tb_log_imaging_study', $simpanLog);
    }
  }

  public function postObservationRad(){
    $dataObs = $this->SatuSehatModel->sendObservationRad()->result_array();
    foreach($dataObs as $do){
        $token = $this->getToken();
        date_default_timezone_set("UTC");
        $dateexp = date_create_from_format('Y-m-d H:i:s', $do['WAKTU_EKSPERTISE']);
        $tglEkspertise = $dateexp->format(DATE_ATOM);
        $ekspertise = trim(preg_replace('/\s+/', ' ', $do['EKSPERTISE']));
        $data = '{
                    "resourceType": "Observation",
                    "status": "final",
                    "category": [
                        {
                            "coding": [
                                {
                                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                                    "code": "imaging",
                                    "display": "Imaging"
                                }
                            ]
                        }
                    ],
                    "code": {
                        "coding": [
                            {
                                "system": "http://loinc.org",
                                "code": "'.$do['KODE_LOINC'].'",
                                "display": "'.$do['DESK_LOINC'].'"
                            }
                        ]
                    },
                    "subject": {
                        "reference": "Patient/'.$do['ID_IHS_PASIEN'].'",
                        "display": "'.$do['NAMA_PASIEN'].'"
                    },
                    "encounter": {
                        "reference": "Encounter/'.$do['ENCOUNTER'].'"
                    },
                    "effectiveDateTime": "'.$tglEkspertise.'",
                    "issued": "'.$tglEkspertise.'",
                    "performer": [
                        {
                            "reference": "Practitioner/'.$do['ID_IHS_DOKTER_PENGISI'].'",
                            "display": "'.$do['NAMA_DOKTER_PENGISI'].'"
                        }
                    ],
                    "valueString": "'.$ekspertise.'",
                    "basedOn" : [
                        {
                            "reference" : "ServiceRequest/'.$do['ID_SERVICE_REQUEST'].'"
                        }
                    ]
                }';
            // echo '<pre>' . $data . '</pre><br>';

        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/Observation'; //DEV
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/Observation'; //PROD
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        if($httpcode == "201"){
            $simpanObservation = array(
            'observation'     => $res->id,
            'nopen'           => $do['NOPEN'],
            'id_encounter'    => $do['ENCOUNTER'],
            'kategori'        => 'RADIOLOGI',
            'tipe'            => 'RADIOLOGI',
            );
            $this->db->insert('ihs.tb_observation_penunjang', $simpanObservation);
            // echo '<pre>'.print_r($simpanObservation)."</pre>";
        }

        date_default_timezone_set('Asia/Jakarta');
        $dataObservation = array (
            'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        );
        // echo '<pre>'.print_r($dataObservation)."</pre>";
        $this->db->where('ID_TINDAKAN_MEDIS', $do['ID_TINDAKAN_MEDIS']);
        $this->db->update('data_ihs.data_observation_radiologi', $dataObservation);

        $simpanLog = array(
            'nopen'            => $do['NOPEN'],
            'id_encounter'     => $do['ENCOUNTER'],
            'id_observation'   => $res->id,
            'log'              => $data,
            'response'         => $response,
            'http_code'        => $httpcode,
            'kategori'         => 'RADIOLOGI',
            'tipe'             => 'RADIOLOGI',
        );
        $this->db->insert('ihs.tb_log_observation_penunjang', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";exit();
            // }
    }
    // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
  }

  public function postDiagnosticReportRad(){
    $dataDR = $this->SatuSehatModel->sendDiagnosticReportRad()->result_array();
    foreach($dataDR as $dd){
        $token = $this->getToken();
        date_default_timezone_set("UTC");
        $datedr = date_create_from_format('Y-m-d H:i:s', $dd['WAKTU_EKSPERTISE']);
        $tglDiagnosticReport = $datedr->format(DATE_ATOM);
        // $ekspertise = trim(preg_replace('/\s+/', ' ', $dd['EKSPERTISE']));
        $data = '{
                  "resourceType": "DiagnosticReport",
                  "identifier": [
                      {
                          "system": "http://sys-ids.kemkes.go.id/diagnostic/100025609/rad",
                          "use": "official",
                          "value": "'.$dd['ID_TINDAKAN_MEDIS'].'"
                      }
                  ],
                  "status": "final",
                  "category": [
                      {
                          "coding": [
                              {
                                  "system": "http://terminology.hl7.org/CodeSystem/v2-0074",
                                  "code": "RAD",
                                  "display": "Radiology"
                              }
                          ]
                      }
                  ],
                  "code": {
                      "coding": [
                          {
                              "system": "http://loinc.org",
                              "code": "'.$dd['KODE_LOINC'].'",
                              "display": "'.$dd['DESK_LOINC'].'"
                          }
                      ]
                  },
                  "subject": {
                      "reference": "Patient/'.$dd['ID_IHS_PASIEN'].'"
                  },
                  "encounter": {
                      "reference": "Encounter/'.$dd['ENCOUNTER'].'"
                  },
                  "effectiveDateTime": "'.$tglDiagnosticReport.'",
                  "issued": "'.$tglDiagnosticReport.'",
                  "performer": [
                      {
                          "reference": "Practitioner/'.$dd['ID_IHS_DOKTER_PENGISI'].'"
                      },
                      {
                          "reference": "Organization/100025609"
                      }
                  ],
                  "imagingStudy": [
                      {
                          "reference": "ImagingStudy/'.$dd['ID_IMG_STUDY'].'"
                      }
                  ],
                  "result": [
                      {
                          "reference": "Observation/'.$dd['ID_OBSERVATION'].'"
                      }
                  ],
                  "basedOn": [
                      {
                          "reference": "ServiceRequest/'.$dd['ID_SERVICE_REQUEST'].'"
                      }
                  ]
              }';
            // echo '<pre>' . $data . '</pre>';

        // $url = 'https://api-satusehat-dev.dto.kemkes.go.id/fhir-r4/v1/DiagnosticReport'; //DEV
        $url = 'https://api-satusehat.kemkes.go.id/fhir-r4/v1/DiagnosticReport'; //PROD
        $cURL = curl_init();
        curl_setopt($cURL, CURLOPT_URL,$url);
        curl_setopt($cURL, CURLOPT_HEADER,false);
        curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer ".$token." "
        ));
        curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
        curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
        $response = curl_exec($cURL);
        $httpcode = curl_getinfo($cURL, CURLINFO_HTTP_CODE);
        curl_close($cURL);
        $res = json_decode($response);
        // if(isset($res->fault)){
        //   $this->getToken();
        // } else {
        // echo 'Status Code : ' . $httpcode . '<br>';
        // echo '<pre>'.$response."</pre>";  
        if($httpcode == "201"){
            $simpanDiagnosticReport = array(
            'diagnostic_report'     => $res->id,
            'id_tindakan_medis'     => $dd['ID_TINDAKAN_MEDIS'],
            'nopen'                 => $dd['NOPEN'],
            'id_encounter'          => $dd['ENCOUNTER'],
            'id_service_request'    => $dd['ID_SERVICE_REQUEST'],
            'kategori'              => 'RADIOLOGI',
            'tipe'                  => 'RADIOLOGI',
            );
            $this->db->insert('ihs.tb_diagnostic_report_penunjang', $simpanDiagnosticReport);
            // echo '<pre>'.print_r($simpanDiagnosticReport)."</pre>";
        }

        date_default_timezone_set('Asia/Jakarta');
        $dataDiagnosticReport = array (
            'ID_IHS'                       => ($httpcode == "201") ? $res->id : NULL,
            'HTTPCODE'                     => $httpcode,
            'CREATED_AT'                   => date('Y-m-d H:i:s'),
            'STATUS'                       => ($httpcode == "201") ? '2' : '1',
        );
        // echo '<pre>'.print_r($dataDiagnosticReport)."</pre>";
        $this->db->where('ID_TINDAKAN_MEDIS', $dd['ID_TINDAKAN_MEDIS']);
        $this->db->update('data_ihs.data_diagnostic_report_radiologi', $dataDiagnosticReport);

        $simpanLog = array(
            'id_tindakan_medis'      => $dd['NOPEN'],
            'id_encounter'           => $dd['ENCOUNTER'],
            'id_service_request'     => $dd['ID_SERVICE_REQUEST'],
            'id_diagnostic_report'   => $res->id,
            'log'                    => $data,
            'response'               => $response,
            'http_code'              => $httpcode,
            'kategori'               => 'RADIOLOGI',
            'tipe'                   => 'RADIOLOGI',
        );
        $this->db->insert('ihs.tb_log_diagnostic_report_penunjang', $simpanLog);
        // echo '<pre>'.print_r($simpanLog)."</pre>";exit();
            // }
    }
    // "display": "Kunjungan '.$composition['NAMA_PASIEN'].' pada tanggal '.date('d M Y', strtotime($composition['TANGGAL'])).'"
  }
  //--------------------------------------------END FASE 5-----------------------------------------------------

}
