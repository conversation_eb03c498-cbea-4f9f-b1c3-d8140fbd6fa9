<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Logout extends CI_Controller {

    public function index()
    {
        unset($_SESSION);
        $this->session->sess_destroy();
        redirect('Login');
    }

  //   public function check_session(){
  //   $session = $_SESSION;

  //   echo json_encode($session);
  // }

}

/* End of file Logout.php */
/* Location: ./application/controllers/ Logout.php */
