<div class="modal-header">
	<h5 class="modal-title">Cari Snomed dengan Nama Specimen : <span class="text-danger"><?=$nama;?></span></h5>
	<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<form id="formCariSnomed" autocomplete="off">
	<div class="modal-body">
		<div class="container-fluid">
			
			<div class="row mt-2">
				<div class="col-md-12">
						<table id="tableMappingSnomedAmbil" class="table table-striped table-bordered table-hover" width="100%">
							<thead>
								<tr>
									<th>CODE</th>
									<th>DISPLAY</th>
									<th>AMBIL</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach($dataSnomedSpecimen as $data){ ?>
									<tr>
										<td><?=$data['CODE'];?></td>
										<td><?=$data['DISPLAY'];?></td>
										<td class="text-center"><button type="button" class="btn btn-primary btn-sm ambilSnomed" data-id='<?=$id;?>' data-code="<?=$data['CODE'];?>" data-display="<?=$data['DISPLAY'];?>" data-dismiss="modal" data-toggle="modal"><i class="fas fa-save"></i></button></td>
									</tr>
								<?php } ?>
							</tbody>
							<tfoot>
								<tr>
									<th>CODE</th>
									<th>DISPLAY</th>
									<th>AMBIL</th>
								</tr>
							</tfoot>
						</table>
				</div>
			</div>

		</div>
	</div>
	<div class="modal-footer">
		<a href="#" class="btn btn-secondary" data-toggle="modal" data-dismiss="modal"><i class="fa fa-times-circle"></i> Tutup
		</a>
	</div>
</form>

<script>
	$(document).ready(function(){
		$('#tableMappingSnomedAmbil').DataTable({
			"ordering": false,
			"lengthMenu": [[5, 10, 20, -1], [5, 10, 20, 'Todos']],
		});

		$('#tableMappingSnomedAmbil').on('click', '.ambilSnomed', function () {
			var id = $(this).data('id');
			var code = $(this).data('code');
			var display = $(this).data('display');
			$.ajax({
				type  : 'POST',
				url   : '<?php echo base_url() ?>IHS/ambilSnomed',
				data  : {
					id:id,
					code:code,
				},
				success : function(data){
					$('.codeTextSnomed_' + id).text(code);
					$('.displayTextSnomed_' + id).text(display);
					$('.statusSnomed_' + id).text('SUDAH MAPPING');
					$('#barisMappingSnomed_' + id).attr('class', 'table-success');
					alertify.success('Data Berhasil Mapping');
				}
			});
			
		});

	});
</script>