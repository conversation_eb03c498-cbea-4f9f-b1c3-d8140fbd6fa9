<style>
	.pilihFilter{
		max-width: 100px;
	}
</style>

<div class="row mt-2">
	<div class="col-md text-center">
		<button type="button" class="btn btn-success refreshMenuMappingLoinc btn-block"><i class="fas fa-redo-alt"></i> Refresh</button>
	</div>
</div>

<div class="row mt-2">
	<div class="col-md-12">
			<div class="table-responsive">
				<table id="tableMappingLoinc" class="table table-striped table-bordered table-hover" width="100%">
				<thead>
					<tr>
						<th style="max-width: 10px;">NAMA PARAM</th>
						<th>KODE PARAM</th>
						<th>NAMA INSTRUMENT</th>
						<th>TINDAKAN SIMPEL</th>
						<th>NILAI</th>
						<th>SATUAN</th>
						<th>CODE LOINC</th>
						<th>DISPLAY</th>
						<th>STATUS</th>
						<th>CARI LOINC</th>
					</tr>
				</thead>
				<tbody>
					<?php foreach($dataLoinc as $data){ ?>
						<tr class="<?=isset($data['LOINC']) ? 'table-success' : 'table-danger'?>" id="barisMappingLoinc_<?=$data['ID'];?>">
							<td><?=$data['LIS_NAMA_TEST'];?></td>
							<td><?=$data['LIS_KODE_TEST'];?></td>
							<td><?=$data['LIS_NAMA_INSTRUMENT'];?></td>
							<td><?=$data['TINDAKAN_SIMPEL'];?></td>
							<td><?=$data['LIS_NILAI_NORMAL'];?></td>
							<td><?=$data['LIS_SATUAN'];?></td>
							<td>
								<span class="codeTextLoinc_<?=$data['ID'];?>"><?=isset($data['LOINC']) ? $data['LOINC'] : '-'?></span>
							</td>
							<td><span class="displayTextLoinc_<?=$data['ID'];?>"><?=isset($data['LOINC']) ? $data['DISPLAY'] : '-'?></span></td>
							<td><span class="statusLoinc_<?=$data['ID'];?>"><?=isset($data['LOINC']) ? 'SUDAH MAPPING' : 'BELUM MAPPING'?></span></td>
							<td class="text-center"><a href="#viewMappingLoinc" class="btn btn-primary btn-sm cariLoinc" data-toggle="modal" data-id='<?=$data['ID'];?>' data-nama="<?=$data['LIS_NAMA_TEST'];?>"><i class="fas fa-search"></i></a></td>
						</tr>
					<?php } ?>
				</tbody>
				<tfoot>
					<tr>
						<th>NAMA PARAM</th>
						<th>KODE PARAM</th>
						<th>NAMA INSTRUMENT</th>
						<th>TINDAKAN SIMPEL</th>
						<th>NILAI</th>
						<th>SATUAN</th>
						<th>CODE LOINC</th>
						<th>DISPLAY</th>
						<th>STATUS</th>
						<th>CARI LOINC</th>
					</tr>
				</tfoot>
			</table>
			</div>
	</div>
</div>

		<div id="viewMappingLoinc" class="modal fade" role="dialog">
			<div class="modal-dialog modal-xl">
				<div class="modal-content">
					<div id="hasilMappingLoinc">

					</div>
				</div>
			</div>
		</div>
		<script>
			$(document).ready(function(){

				$('#tableMappingLoinc').on('click', '.cariLoinc', function () {
					var id = $(this).data('id');
					var nama = $(this).data('nama');
					$.ajax({
						type  : 'POST',
						url   : '<?php echo base_url() ?>IHS/cariLoinc',
						data  : {
							id:id,
							nama:nama
						},
						success : function(data){
							$('#hasilMappingLoinc').html(data);
						}
					});
				});

				var dataLoinc1 = $('#tableMappingLoinc').DataTable({
					"ordering": false,
					"bLengthChange": false,
					initComplete: function () {
						this.api().columns([2]).every(function () {
							var column = this;
							var select = $('<select class="pilihFilter"><option value=""></option></select>')
							.appendTo($(column.footer()).empty())
							.on('change', function () {
								var val = $.fn.dataTable.util.escapeRegex(
									$(this).val()
									);

								column
								.search(val ? '^' + val + '$' : '', true, false)
								.draw();
							});

							column.data().unique().sort().each(function (d, j) {
								select.append('<option value="' + d + '">' + d + '</option>')
							});
						});

						this.api().columns([8]).every(function () {
							var column = this;
							var select2 = $('<select class="pilihFilter"><option value=""></option></select>')
							.appendTo($(column.footer()).empty())
							.on('change', function () {
								var val = $.fn.dataTable.util.escapeRegex(
									$(this).val()
									);

								column
								.search(val ? '^' + val + '$' : '', true, false)
								.draw();
							});

							select2.append('<option value="SUDAH MAPPING">SUDAH MAPPING</option>');
							select2.append('<option value="BELUM MAPPING">BELUM MAPPING</option>');
						});
					}
				});

				$('.refreshMenuMappingLoinc').on('click', function(){
					$('.mappingLoinc').trigger('click');
				});

			});
		</script>