<h1>Practitioner Menu</h1>
<ul class="nav nav-tabs">
  <li class="nav-item">
    <a class="nav-link active" href="#searchPractitioner" data-toggle="tab">Search NIK</a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="#searchPractitionerID" data-toggle="tab">Search Practitioner ID</a>
  </li>
</ul>

<div class="tab-content">
  <div class="tab-pane active" id="searchPractitioner">
    <form id="formGetPractitionerByNIK">
      <label><br>NIK</label>
      <div class="row col-md-12">
        <input type="text" name="nikPractitioner" placeholder="NIK" class="form-control col-md-3" required>
        <button type="submit" id="tombolPractitionerByNIK" style="margin-left: 15px;" class="btn btn-warning">Search</button>
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="hasilPractitionerByNIK"></pre>
  </div>
  <div class="tab-pane" id="searchPractitionerID">
    <form id="formGetPractitionerByNIKID">
      <label><br>NIK</label>
      <div class="row col-md-12">
        <input type="text" name="nikPractitionerID" placeholder="NIK" class="form-control col-md-3" required>
        <button type="submit" id="tombolPractitionerByNIKID" style="margin-left: 15px;" class="btn btn-warning">Search</button>
      </div>
      <br>
    </form>
    <label>Result :</label>
    <pre id="hasilPractitionerByNIKID"></pre>
  </div>
</div>
<script>
  $('#formGetPractitionerByNIK').submit(function (event) {
    form = $("#formGetPractitionerByNIK").serializeArray();
    $.ajax({
      url: '<?= base_url('IHS/getPractitionerByNIK') ?>',
      data: form,
      method: 'POST',
      success: function(res){
        $("#hasilPractitionerByNIK").html(res);
      }
    });
    event.preventDefault();
  });

  $('#formGetPractitionerByNIKID').submit(function (event) {
    form = $("#formGetPractitionerByNIKID").serializeArray();
    $.ajax({
      url: '<?= base_url('IHS/getPractitionerByNIKID') ?>',
      data: form,
      method: 'POST',
      success: function(res){
        $("#hasilPractitionerByNIKID").html(res);
      }
    });
    event.preventDefault();
  });
</script>