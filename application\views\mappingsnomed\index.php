<style>
	.pilihFilter{
		max-width: 100px;
	}
</style>

<div class="row mt-2">
	<div class="col-md text-center">
		<button type="button" class="btn btn-success refreshMenuMappingSnomed btn-block"><i class="fas fa-redo-alt"></i> Refresh</button>
	</div>
</div>

<div class="row mt-2">
	<div class="col-md-12">
			<div class="table-responsive">
				<table id="tableMappingSnomed" class="table table-striped table-bordered table-hover" width="100%">
				<thead>
					<tr>
						<th style="max-width: 10px;">NAMA</th>
						<th>PARENT</th>
						<th>CODE</th>
						<th>DISPLAY</th>
						<th>STATUS</th>
						<th>CARI SNOMED</th>
					</tr>
				</thead>
				<tbody>
					<?php foreach($dataSnomed as $data){ ?>
						<tr class="<?=isset($data['CODE']) ? 'table-success' : 'table-danger'?>" id="barisMappingSnomed_<?=$data['ID'];?>">
							<td><?=$data['NAMA'];?></td>
							<td><?=$data['PARENT'];?></td>
							<td>
								<span class="codeTextSnomed_<?=$data['ID'];?>"><?=isset($data['CODE']) ? $data['CODE'] : '-'?></span>
							</td>
							<td><span class="displayTextSnomed_<?=$data['ID'];?>"><?=isset($data['DISPLAY']) ? $data['DISPLAY'] : '-'?></span></td>
							<td><span class="statusSnomed_<?=$data['ID'];?>"><?=isset($data['CODE']) ? 'SUDAH MAPPING' : 'BELUM MAPPING'?></span></td>
							<td class="text-center"><a href="#viewMappingSnomed" class="btn btn-primary btn-sm cariSnomed" data-toggle="modal" data-id='<?=$data['ID'];?>' data-nama="<?=$data['NAMA'];?>"><i class="fas fa-search"></i></a></td>
						</tr>
					<?php } ?>
				</tbody>
				<tfoot>
					<tr>
						<th>NAMA</th>
						<th>PARENT</th>
						<th>CODE</th>
						<th>DISPLAY</th>
						<th>STATUS</th>
						<th>CARI SNOMED</th>
					</tr>
				</tfoot>
			</table>
			</div>
	</div>
</div>

		<div id="viewMappingSnomed" class="modal fade" role="dialog">
			<div class="modal-dialog modal-xl">
				<div class="modal-content">
					<div id="hasilMappingSnomed">

					</div>
				</div>
			</div>
		</div>
		<script>
			$(document).ready(function(){
				// $('#tableMappingSnomed').DataTable({
				// 	"ordering": false
				// });

				$('#tableMappingSnomed').on('click', '.cariSnomed', function () {
					var id = $(this).data('id');
					var nama = $(this).data('nama');
					$.ajax({
						type  : 'POST',
						url   : '<?php echo base_url() ?>IHS/cariSnomed',
						data  : {
							id:id,
							nama:nama
						},
						success : function(data){
							$('#hasilMappingSnomed').html(data);
						}
					});
				});

				var dataSnomed = $('#tableMappingSnomed').DataTable({
					"ordering": false,
					"bLengthChange": false,
					initComplete: function () {
						this.api().columns([1]).every(function () {
							var column = this;
							var select = $('<select class="pilihFilter"><option value=""></option></select>')
							.appendTo($(column.footer()).empty())
							.on('change', function () {
								var val = $.fn.dataTable.util.escapeRegex(
									$(this).val()
									);

								column
								.search(val ? '^' + val + '$' : '', true, false)
								.draw();
							});

							column.data().unique().sort().each(function (d, j) {
								select.append('<option value="' + d + '">' + d + '</option>')
							});
						});

						this.api().columns([4]).every(function () {
							var column = this;
							var select2 = $('<select class="pilihFilter"><option value=""></option></select>')
							.appendTo($(column.footer()).empty())
							.on('change', function () {
								var val = $.fn.dataTable.util.escapeRegex(
									$(this).val()
									);

								column
								.search(val ? '^' + val + '$' : '', true, false)
								.draw();
							});

							select2.append('<option value="SUDAH MAPPING">SUDAH MAPPING</option>');
							select2.append('<option value="BELUM MAPPING">BELUM MAPPING</option>');
						});
					}
				});

				// $(dataSnomed.columns(4).footer()).find('select').on('change', function () {
				// 	alert('coba');
				// });

				$('.refreshMenuMappingSnomed').on('click', function(){
					$('.mappingSnomed').trigger('click');
				});

			});
		</script>