<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON> extends CI_Controller {
  public function __construct()
  {
    parent::__construct();
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model('YankesModel');
  }

  public function getToken()
  {
    // START GET TOKEN
    $postDataArray = array(
      'userName' => '3174063','password' => '12345'
    );

    $data = http_build_query($postDataArray);

    $url = 'http://*************/fasyankes/rumahsakit/login';

    $ch = curl_init($url);
    # Setup request to send json via POST.
    $payload = json_encode($postDataArray);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload );
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
    # Return response instead of printing.
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
    # Send request.
    $result = curl_exec($ch);
    curl_close($ch);
    $obj1 = json_decode($result);
    // echo '<pre>' . $obj1->data->access_token . '</pre>';
    return $obj1->data->access_token;
    // END GET TOKEN
  }

  function guidv4($data = null) {
    // for($i=1; $i<=3; $i++){
      // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    // }
  }

  function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    return $randomString;
  }
//-------------------------------------------FASE 1----------------------------------------------------------
  public function postSurvey()
  {
    $dataSurvey = $this->YankesModel->Survey()->result_array();

    foreach($dataSurvey as $data1){
      $token = $this->getToken();
      $data = '{
            "noRekamMedis": "'.$data1['NORM'].'",
            "nama": "'.$data1['NAMA'].'",
            "tanggalLahir": "'.$data1['TGL_LAHIR'].'",
            "umur": '.$data1['UMUR'].',
            "jenisKelamin": "'.$data1['JENIS_KELAMIN'].'",
            "pendidikanTerakhir": "'.$data1['PENDIDIKAN_TERAKHIR'].'",
            "pekerjaanUtama": "'.$data1['PEKERJAAN_UTAMA'].'",
            "debitur": "'.$data1['DEBITUR'].'",
            "noHp": "'.$data1['NO_HP'].'",
            "rekomendasi": "'.$data1['REKOMEN_RS'].'",
            "unitPelayanan": "'.$data1['KODE_PELAYANAN'].'",
            "kritikSaran": "'.$data1['SARAN_KRITIK'].'",
            "data": [
            {
                "pertanyaanId": 1,
                "jawabanId": "'.$data1['TENTANG_ADMINISTRASI'].'"
            },
            {
                "pertanyaanId": 2,
                "jawabanId": "'.$data1['TENTANG_AKSES'].'"
            },
            {
                "pertanyaanId": 3,
                "jawabanId": "'.$data1['TENTANG_KECEPATAN_WAKTU'].'"
            },
            {
                "pertanyaanId": 4,
                "jawabanId": "'.$data1['TENTANG_KEWAJARAN_BIAYA'].'"
            },
            {
                "pertanyaanId": 5,
                "jawabanId": "'.$data1['TENTANG_KESESUAIAN_INFO'].'"
            },
            {
                "pertanyaanId": 6,
                "jawabanId": "'.$data1['TENTANG_KEMAMPUAN_PETUGAS'].'"
            },
            {
                "pertanyaanId": 7,
                "jawabanId": "'.$data1['TENTANG_SIKAP_PETUGAS'].'"
            },
            {
                "pertanyaanId": 8,
                "jawabanId": "'.$data1['TENTANG_PROSEDUR'].'"
            },
            {
                "pertanyaanId": 9,
                "jawabanId": "'.$data1['TENTANG_KUALITAS_TOILET'].'"
            }
            ]
          }';

      $url = 'http://*************/fasyankes/rumahsakit/surveikepuasanmasyarakat'; //DEV

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $data);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      curl_close($cURL);
      $res = json_decode($response);
      // echo '<pre>' . $response . '</pre>';
      // echo $httpcode;
      if($res->status == '1'){

        $simpanSurvey = array(
          'ID_FASYANKES' => $res->data->id,
          'ID_SURVEY_FK' => $data1['ID_SURVEY'],
          'STATUS'       => $res->status,
        );

        $this->db->insert('keluhan_pasien.tb_survey_fasyankes', $simpanSurvey);    
      }
        $simpanLogSurvey = array(
          'ID_SURVEY'    => $data1['ID_SURVEY'],
          'MESSAGE'      => $response,
          'STATUS'       => $res->status,
        );
        $this->db->insert('keluhan_pasien.tb_log_survey', $simpanLogSurvey);
    }
  }

  public function postKeluhan()
  {
    $dataKeluhan = $this->YankesModel->Keluhan()->result_array();
    // $token = $this->getToken();
    foreach($dataKeluhan as $data1){
      $token = $this->getToken();
      $dataKeluhanAdmin = $this->YankesModel->KeluhanAdministrasi($data1['ID'])->result_array();
      $dataKeluhanPetugasMedis = $this->YankesModel->KeluhanPetugasMedis($data1['ID'])->result_array();
      $dataKeluhanPetugasNonMedis = $this->YankesModel->KeluhanPetugasNonMedis($data1['ID'])->result_array();
      $dataKeluhanLayanan = $this->YankesModel->KeluhanLayanan($data1['ID'])->result_array();
      $dataKeluhanFasilitas = $this->YankesModel->KeluhanFasilitas($data1['ID'])->result_array();

      $admins = array();
      foreach($dataKeluhanAdmin as $data2){
         $admins[] = array (
            'pendaftaran_administrasi_id'=>  "".$data2['KELUHAN']."",
          );
      }

      $petmed = array();
      foreach($dataKeluhanPetugasMedis as $data3){
         $petmed[] = array (
            'petugas_medis_id'=>  "".$data3['KELUHAN']."",
          );
      }

      $petnonmed = array();
      foreach($dataKeluhanPetugasNonMedis as $data4){
        $desk4 = isset($data4['DESKRIPSI_KELUHAN']) ? $data4['DESKRIPSI_KELUHAN'] : '-';
         $petnonmed[] = array (
            'petugas_non_medis_id'=>  "".$data4['KELUHAN']."",
            'deskripsi'=>  "".$desk4."",
          );
      }

      $lay = array();
      foreach($dataKeluhanLayanan as $data5){
        $desk5 = isset($data5['DESKRIPSI_KELUHAN']) ? $data5['DESKRIPSI_KELUHAN'] : '-';
         $lay[] = array (
            'layanan_id'=>  "".$data5['KELUHAN']."",
            'deskripsi'=>  "".$desk5."",
          );
      }

      $fas = array();
      foreach($dataKeluhanFasilitas as $data6){
        $desk6 = isset($data6['DESKRIPSI_KELUHAN']) ? $data6['DESKRIPSI_KELUHAN'] : '-';
         $fas[] = array (
            'fasilitas_id'=>  "".$data6['KELUHAN']."",
            'deskripsi'=>  "".$desk6."",
          );
      }

      $postDataArray = array(
        'nama' => "".$data1['NAMA']."",
        'nomor_hp' => "".$data1['NO_HP']."",
        'email' => "".$data1['EMAIL']."",
        'pengisi_keluhan_id' => "".$data1['YANG_MENGALAMI_KELUHAN']."",
        'status_rekam_medis_id' => "".$data1['MEMILIKI_NORM']."",
        'no_rekam_medis' => "".$data1['NORM']."",
        'nama_pasien' => "".$data1['NAMA_PASIEN']."",
        'tanggal_lahir_pasien' => "".$data1['TGL_LAHIR_PASIEN']."",
        'tanggal_waktu_kejadian' => "".date('Y-m-d H:i', strtotime($data1['WAKTU_KEJADIAN']))."",
        'tempat_kejadian' => "".$data1['TEMPAT_KEJADIAN']."",
        'status_pembiayaan_pasien_id' => "".$data1['STATUS_PEMBIAYAAN']."",
        'keluhan_lain' => "".$data1['DESKRIPSI_KELUHAN_DAN_LAINNYA']."",
        'nilai_keluhan' => "".$data1['SKALA_TERGANGGU']."",
        'keluhan_pendaftaran_administrasi' => $admins,
        'keluhan_petugas_medis' => $petmed,
        'keluhan_petugas_non_medis' => $petnonmed,
        'keluhan_layanan' => $lay,
        'keluhan_fasilitas' => $fas,
      );
      $payload = json_encode($postDataArray);
      // echo '<pre>' . $payload . '</pre>';
      $url = 'http://*************/fasyankes/rumahsakit/keluhanpasien'; //DEV

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $payload);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      curl_close($cURL);
      $res = json_decode($response);
      // echo '<pre>' . $response . '</pre>';
      // echo $httpcode;
      if($res->status == TRUE){

        $simpanKeluhan = array(
          'ID_FASYANKES' => $res->data->keluhan_id,
          'ID_KELUHAN_FK' => $data1['ID'],
          'STATUS'       => $res->status,
        );

        $this->db->insert('keluhan_pasien.tb_keluhan_fasyankes', $simpanKeluhan);    
      }
        $simpanLogKeluhan = array(
          'ID_KELUHAN'    => $data1['ID'],
          'MESSAGE'      => $response,
          'STATUS'       => $res->status,
        );
        $this->db->insert('keluhan_pasien.tb_log_keluhan', $simpanLogKeluhan);
    }
  }

  function postRespon(){
    $dataRespon = $this->YankesModel->Respon()->result_array();
    foreach($dataRespon as $data1){
      $token = $this->getToken();

      $postDataArray = array(
        'keluhan_id' => $data1['ID_FASYANKES'],
        'asal_keluhan_id' => "1",
        'deskripsi_asal_keluhan' => "-",
        'status_keluhan_id' => "".$data1['STATUS_RESPON']."",
      );
      $payload = json_encode($postDataArray);
      // echo '<pre>' . $payload . '</pre>';
      $url = 'http://*************/fasyankes/rumahsakit/responkeluhanpasien'; //DEV

      $cURL = curl_init();
      curl_setopt($cURL, CURLOPT_URL,$url);
      curl_setopt($cURL, CURLOPT_HEADER,false);
      curl_setopt($cURL, CURLOPT_RETURNTRANSFER,true);
      curl_setopt($cURL, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer ".$token." "
      ));
      curl_setopt($cURL, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($cURL, CURLOPT_CUSTOMREQUEST, 'POST');
      curl_setopt($cURL, CURLOPT_POSTFIELDS, $payload);
      curl_setopt($cURL, CURLOPT_CONNECTTIMEOUT,10);
      $response = curl_exec($cURL);
      curl_close($cURL);
      $res = json_decode($response);
      // echo '<pre>' . $response . '</pre>';
      // echo $httpcode;
      if($res->status == TRUE){

        $simpanRespon = array(
          'ID_FASYANKES' => $res->data->respon_id,
          'ID_KELUHAN_FK' => $data1['ID'],
          'ID_KELUHAN_FASYANKES_FK' => $data1['ID_FASYANKES'],
          'STATUS'       => $res->status,
        );

        $this->db->insert('keluhan_pasien.tb_respon_fasyankes', $simpanRespon);    
      }
        $simpanLogRespon = array(
          'ID_KELUHAN'    => $data1['ID'],
          'MESSAGE'      => $response,
          'STATUS'       => $res->status,
        );
        $this->db->insert('keluhan_pasien.tb_log_respon', $simpanLogRespon);
    }
  }

}
