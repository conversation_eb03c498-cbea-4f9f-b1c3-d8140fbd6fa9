//
// Charts
//
"use strict";var Charts=function(){var a,r=$('[data-toggle="chart"]'),e="light",t={base:"Open Sans"},o={gray:{100:"#f6f9fc",200:"#e9ecef",300:"#dee2e6",400:"#ced4da",500:"#adb5bd",600:"#8898aa",700:"#525f7f",800:"#32325d",900:"#212529"},theme:{default:"#172b4d",primary:"#5e72e4",secondary:"#f4f5f7",info:"#11cdef",success:"#2dce89",danger:"#f5365c",warning:"#fb6340"},black:"#12263F",white:"#FFFFFF",transparent:"transparent"};function n(a,r){for(var e in r)"object"!=typeof r[e]?a[e]=r[e]:n(a[e],r[e])}function d(a){var r=a.data("add"),e=$(a.data("target")).data("chart");a.is(":checked")?(!function a(r,e){for(var t in e)Array.isArray(e[t])?e[t].forEach(function(a){r[t].push(a)}):a(r[t],e[t])}(e,r),e.update()):(!function a(r,e){for(var t in e)Array.isArray(e[t])?e[t].forEach(function(a){r[t].pop()}):a(r[t],e[t])}(e,r),e.update())}function i(a){var r=a.data("update"),e=$(a.data("target")).data("chart");n(e,r),function(a,r){if(void 0!==a.data("prefix")||void 0!==a.data("prefix")){var e=a.data("prefix")?a.data("prefix"):"",t=a.data("suffix")?a.data("suffix"):"";r.options.scales.yAxes[0].ticks.callback=function(a){if(!(a%10))return e+a+t},r.options.tooltips.callbacks.label=function(a,r){var o=r.datasets[a.datasetIndex].label||"",n=a.yLabel,d="";return r.datasets.length>1&&(d+='<span class="popover-body-label mr-auto">'+o+"</span>"),d+='<span class="popover-body-value">'+e+n+t+"</span>"}}}(a,e),e.update()}return window.Chart&&n(Chart,(a={defaults:{global:{responsive:!0,maintainAspectRatio:!1,defaultColor:"dark"==e?o.gray[700]:o.gray[600],defaultFontColor:"dark"==e?o.gray[700]:o.gray[600],defaultFontFamily:t.base,defaultFontSize:13,layout:{padding:0},legend:{display:!1,position:"bottom",labels:{usePointStyle:!0,padding:16}},elements:{point:{radius:0,backgroundColor:o.theme.primary},line:{tension:.4,borderWidth:4,borderColor:o.theme.primary,backgroundColor:o.transparent,borderCapStyle:"rounded"},rectangle:{backgroundColor:o.theme.warning},arc:{backgroundColor:o.theme.primary,borderColor:"dark"==e?o.gray[800]:o.white,borderWidth:4}},tooltips:{enabled:!0,mode:"index",intersect:!1}},doughnut:{cutoutPercentage:83,legendCallback:function(a){var r=a.data,e="";return r.labels.forEach(function(a,t){var o=r.datasets[0].backgroundColor[t];e+='<span class="chart-legend-item">',e+='<i class="chart-legend-indicator" style="background-color: '+o+'"></i>',e+=a,e+="</span>"}),e}}}},Chart.scaleService.updateScaleDefaults("linear",{gridLines:{borderDash:[2],borderDashOffset:[2],color:"dark"==e?o.gray[900]:o.gray[300],drawBorder:!1,drawTicks:!1,drawOnChartArea:!0,zeroLineWidth:0,zeroLineColor:"rgba(0,0,0,0)",zeroLineBorderDash:[2],zeroLineBorderDashOffset:[2]},ticks:{beginAtZero:!0,padding:10,callback:function(a){if(!(a%10))return a}}}),Chart.scaleService.updateScaleDefaults("category",{gridLines:{drawBorder:!1,drawOnChartArea:!1,drawTicks:!1},ticks:{padding:20},maxBarThickness:10}),a)),r.on({change:function(){var a=$(this);a.is("[data-add]")&&d(a)},click:function(){var a=$(this);a.is("[data-update]")&&i(a)}}),{colors:o,fonts:t,mode:e}}();