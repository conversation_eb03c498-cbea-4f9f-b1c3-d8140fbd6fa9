<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SatuSehatModel extends CI_Model {

	function __construct(){
		parent::__construct();
		//load our second db and put in $db2
		$this->dbpacs = $this->load->database('pacs', TRUE);
	}

	public function condition($nopen)
	{
		$query ="SELECT c.diagnosa_deskripsi DIAGNOSA_DESKRIPSI
		, c.id_condition IDCONDITION
		, c.jenis JENIS
		FROM ihs.tb_condition c
		WHERE c.nopen='$nopen'";
		$bind = $this->db->query($query);
		return $bind;
	}

	public function hapusIsiTable($param)
	{
		if($param == 1){
			$query ="DELETE 
			FROM ihs.tb_log_encounter
			ORDER BY id ASC
			LIMIT 1000";
		}elseif($param == 2){
			//....
		}
		$bind = $this->db->query($query);
		return $bind;
	}

	public function procedure()
	{
		$query = $this->db->query("CALL ihs.ProsedureIcd9Cm()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function observation($nopen)
	{
		$query = "SELECT a.id, a.NORM, a.nokun, a.nopen, a.tekanan_darah
		, a.per_tekanan_darah, a.pernapasan, a.nadi#, a.suhu 
		, if((a.suhu IS NULL OR a.suhu=0),NULL,a.suhu) suhu
		, if((a.bb IS NULL OR a.bb=0),NULL,a.bb) bb
		, if((a.tb IS NULL OR a.tb=0),NULL,a.tb) tb
		, a.created_at
		, a.ID_IHS
		FROM
		
		(SELECT kp.id_emr id, p.NORM, kp.nokun, p.NOMOR nopen
			, pf.tekanan_darah, pf.per_tekanan_darah
			, pf.pernapasan, pf.nadi, pf.suhu
			, pf.bb, pf.tb, kp.created_at, pas.ID_IHS
		
		FROM keperawatan.tb_keperawatan kp
		
			LEFT JOIN keperawatan.tb_pemeriksaan_fisik pf ON pf.id_emr = kp.id_emr
			#LEFT JOIN keperawatan.tb_cppt cp ON cp.nokun = kp.nokun
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = kp.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			LEFT JOIN ihs.tb_pasien pas ON pas.NORM = p.NORM
		
		WHERE p.NOMOR='$nopen' AND kp.flag='1'
		
		UNION
		
		SELECT cp.id, p.NORM, cp.nokun, p.NOMOR nopen
			, cv.tekanan_darah, cv.per_tekanan_darah
			, cv.pernapasan, cv.nadi, cv.suhu
			, cv.bb, cv.tb, cp.tanggal, pas.ID_IHS
		FROM keperawatan.tb_cppt_tanda_vital cv
			LEFT JOIN keperawatan.tb_cppt cp ON cp.id = cv.id_cppt
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = cp.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
		LEFT JOIN ihs.tb_pasien pas ON pas.NORM = p.NORM
		WHERE cp.`status`!=0 #AND cp.jenis IN (1,3) 
			AND p.NOMOR = '$nopen'
		
		UNION
						
		SELECT tv.id
			, tv.nomr
			, tv.nokun
			, p.NOMOR nopen
			, tv.td_sistolik
			, tv.td_diastolik
			, tv.pernapasan
			, tv.nadi
			, tv.suhu
			, tb.bb
			, tb.tb
			, tv.created_at
			  , pas.ID_IHS
			  
		FROM db_pasien.tb_tanda_vital tv
			LEFT JOIN db_pasien.tb_tb_bb tb ON tb.nokun = tv.nokun
			LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = tv.nokun
			LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
			  LEFT JOIN ihs.tb_pasien pas ON pas.NORM = p.NORM
		
		WHERE p.NOMOR='$nopen'
			AND tv.status = 1
			AND tb.status = 1
		) a   
		
		WHERE (a.tekanan_darah IS NOT NULL OR a.per_tekanan_darah IS NOT NULL OR a.pernapasan IS NOT NULL OR a.nadi IS NOT NULL OR a.suhu IS NOT NULL
				) AND (a.tekanan_darah>0 OR a.per_tekanan_darah>0 OR a.pernapasan>0 OR a.nadi>0 OR a.suhu>0)
		ORDER BY a.created_at DESC
		LIMIT 1
				#a.tekanan_darah!=0";

		$bind = $this->db->query($query);
		return $bind->row_array();
	}

	public function cekObservation()
	{
		$query = "SELECT enc.nopen NOPEN, enc.encounter IDENCOUNTER

		FROM ihs.tb_encounter enc
		LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN = enc.nopen
		LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = pk.NOPEN
		WHERE enc.nopen NOT IN (
			SELECT obs.nopen
			FROM ihs.tb_observation obs) AND pk.MASUK <= NOW() 
		AND pk.MASUK >= DATE_ADD(NOW(), INTERVAL - 12 MONTH)
		AND ihs.cariObservation(enc.nopen)!=0

		GROUP BY enc.nopen
		ORDER BY enc.nopen DESC
		LIMIT 20";

		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function cekComposition()
	{
		$query = "SELECT e.nopen, e.encounter FROM ihs.tb_encounter e
		LEFT JOIN ihs.tb_composition c ON e.encounter = c.id_encounter
		WHERE c.composition IS NULL";

		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function composition()
	{
		$query = $this->db->query("CALL ihs.Composition()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	// public function cekEncounter()
	// {
	// 	$query ="
	// 	SELECT enc.id_ihs_pasien ID_IHS_PASIEN, pd.NOMOR NOPEN, kip.NOMOR KTP_PASIEN
	// 	, pg.KTP KTP_DOKTER
	// 	, CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN
	// 	, master.getNamaLengkapPegawai (doks.NIP) DPJP
	// 	, DATE_FORMAT(tk.MASUK,'%Y-%m-%d %H:%i:%s') TGLREG
	// 	, DATE_FORMAT(tk.KELUAR,'%Y-%m-%d %H:%i:%s') TGLKELUAR
	// 	, r.DESKRIPSI UNITPELAYANAN
	// 	, r.ID ID_RUANGAN
	// 	, pd.STATUS
	// 	, enc.encounter ENCOUNTER
		
	// 	FROM pendaftaran.pendaftaran pd
	// 	LEFT JOIN master.diagnosa_masuk mdm ON pd.DIAGNOSA_MASUK=mdm.ID
	// 	LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pd.NOMOR
	// 	LEFT JOIN master.ruangan r ON tp.RUANGAN=r.ID AND r.JENIS=5
	// 	LEFT JOIN master.dokter doks ON doks.ID = tp.DOKTER
	// 	LEFT JOIN ihs.tb_pegawai pg ON pg.ID_DOKTER=doks.ID
	// 	LEFT JOIN master.pasien p ON p.NORM=pd.NORM
	// 	LEFT JOIN master.referensi rjk ON p.JENIS_KELAMIN=rjk.ID AND rjk.JENIS=2
	// 	LEFT JOIN master.kartu_identitas_pasien kip ON kip.NORM=p.NORM
	// 	LEFT JOIN pendaftaran.kunjungan tk ON tk.NOPEN=pd.NOMOR
	// 	LEFT JOIN master.ruangan jkr ON jkr.ID=tp.RUANGAN
	// 	LEFT JOIN ihs.tb_encounter enc ON enc.nopen = pd.NOMOR
	// 	LEFT JOIN ihs.tb_condition con ON con.nopen = enc.nopen
	// 	WHERE enc.nopen NOT IN (SELECT c.nopen FROM ihs.tb_condition c) AND enc.encounter IS NOT NULL
	// 	GROUP BY pd.NOMOR
	// 	LIMIT 50
	// 	";
	// 	$bind = $this->db->query($query);
	// 	return $bind->result_array();
	// }

	public function cekEncounter()
	{
		$query = $this->db->query("CALL ihs.Condition()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	// public function cekEncounter()
	// {
	// 	$query = "SELECT pen.NOMOR NOPEN, enc.encounter ENCOUNTER, master.getNamaLengkap(pen.NORM) NAMAPASIEN, enc.id_ihs_pasien ID_IHS_PASIEN
	// 	FROM ihs.tb_encounter enc
	// 	LEFT JOIN ihs.tb_condition con ON enc.encounter = con.id_encounter
	// 	LEFT JOIN pendaftaran.pendaftaran pen ON enc.nopen = pen.NOMOR
	// 	LEFT JOIN master.pasien pas ON pen.NORM = pas.NORM
	// 	WHERE enc.nopen NOT IN (
	// 		SELECT c.nopen
	// 		FROM ihs.tb_condition c)";

	// 	$bind = $this->db->query($query);
	// 	return $bind->result_array();
	// }

	public function cekProcedure()
	{
	// 	$query ="SELECT enc.nopen NOPEN, enc.encounter IDENCOUNTER, ps.ID_IHS IDPASIEN, p.NORM, CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN
	// 	FROM ihs.tb_encounter enc
	// 	LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = enc.nopen
	// 	LEFT JOIN ihs.tb_pasien ps ON ps.NORM=p.NORM
	// 	WHERE enc.nopen NOT IN (
	// 		SELECT pro.nopen
	// 		FROM ihs.tb_procedure pro) 
	// 		AND p.TANGGAL BETWEEN CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -7 DAY),' 00:00:00') 
	// AND CONCAT(DATE_ADD(DATE(NOW()), INTERVAL -7 DAY),' 23:59:59')";
		$query = "SELECT DISTINCT enc.nopen NOPEN, md.ID ID_PROSEDUR, enc.encounter IDENCOUNTER
		, p.NORM, CONCAT(master.getNamaLengkap(p.NORM)) NAMAPASIEN, enc.created_at
		, p.TANGGAL TGL_PENDAFTARAN
		, TRIM(md.KODE) KODE, mr.STR DESKRIPSI, md.TANGGAL
		, enc.id_ihs_pasien IDPASIEN
		, enc.id_ihs_practitioner IDPRACTITIONER
		FROM ihs.tb_encounter enc
		LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = enc.nopen
		LEFT JOIN medicalrecord.prosedur md ON md.NOPEN = enc.nopen
		LEFT JOIN master.mrconso mr ON mr.CODE=TRIM(md.KODE) AND md.`STATUS`=1 AND mr.SAB='ICD9CM_2005' AND TTY IN ('PX', 'PT')
		WHERE md.ID NOT IN (
			SELECT pro.prosedur
			FROM ihs.tb_procedure pro) AND md.ID IS NOT NULL
		LIMIT 10";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function cekMedicationRequest()
	{
		$query = "SELECT enc.encounter, enc.nopen
		
		FROM ihs.tb_encounter enc

		LEFT JOIN pendaftaran.pendaftaran pd ON pd.NOMOR=enc.nopen
		LEFT JOIN pendaftaran.kunjungan pk ON pk.NOPEN=pd.NOMOR AND pk.`STATUS` !=0
		LEFT JOIN layanan.order_resep ore ON ore.KUNJUNGAN = pk.NOMOR AND ore.`STATUS` !=0 AND ore.NOMOR IS NOT NULL
		LEFT JOIN layanan.order_detil_resep odr ON odr.ORDER_ID = ore.NOMOR
		
		
		WHERE pd.`STATUS` !=0
		AND
		enc.nopen NOT IN (
			SELECT md.nopen
			FROM ihs.tb_medication md WHERE md.jenis = 'REQUEST') 
		
		AND odr.FARMASI NOT IN (
			SELECT md.id_obat
			FROM ihs.tb_medication md WHERE md.jenis = 'REQUEST')
		
		GROUP BY pd.NOMOR
		ORDER BY pd.NOMOR DESC

		LIMIT 100";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function cekServiceRequestLab()
	{
		$query = "SELECT en.encounter ID_ENCOUNTER
		, df.NOMOR NOPEN
		, ps.ID_IHS ID_IHS_PATIENT
		, pas.NAMA NAMA_PASIEN
		, dok.ID_IHS ID_IHS_PRACTITIONER
		
		FROM pendaftaran.pendaftaran df
		
		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = df.NOMOR
		LEFT JOIN ihs.tb_encounter en ON en.nopen = kun.NOPEN
		LEFT JOIN ihs.tb_service_request_lab ser ON ser.nopen = en.nopen
		LEFT JOIN layanan.order_lab olab ON olab.KUNJUNGAN = kun.NOMOR
		LEFT JOIN master.pasien pas ON pas.NORM = df.NORM
		LEFT JOIN ihs.tb_pasien ps ON ps.NORM = pas.NORM
		LEFT JOIN master.dokter dokk ON dokk.ID = olab.DOKTER_ASAL
		LEFT JOIN ihs.tb_dokter dok ON dok.ID_DOKTER = dokk.ID
				
		WHERE en.nopen NOT IN (SELECT ser.nopen FROM ihs.tb_service_request_lab ser) AND en.encounter IS NOT NULL AND olab.NOMOR IS NOT NULL
		
		GROUP BY df.NOMOR
		
		LIMIT 5";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function medRequest($nopen)
	{
		$query ="
		SELECT
		master.getNamaLengkap(df.NORM) NAMA_PASIEN, CASE WHEN bar.NAMA IS NULL THEN bar2.NAMA ELSE bar.NAMA END NAMA_OBAT,
		bar.STATUS STATUS_OBAT,
		' ' TUJUAN_OBAT, CASE WHEN peng1.ID = peng2.ID THEN 'false' ELSE 'true' END OLEH,
		ores.TANGGAL TANGGAL_RESEP,
		master.getNamaLengkapPegawai(peng2.NIP) CREATED_BY,
		kun.MASUK DATETIME_TERIMA_FARMASI,
		refsign.DESKRIPSI ATURAN_PAKAI_X,
		refsig.DESKRIPSI ATURAN_PAKAI_HARI,
		refsigg.DESKRIPSI ATURAN_PAKAI_LOKASI,
		japer.variabel JALUR_PEMBERIAN,
		' ' DOSE_QUANTITY_VALUE,
		' ' DOSE_QUANTITY_UNIT,
		far.JUMLAH TOTAL_JUMLAH_OBAT,
		med.id_encounter ID_ENCOUNTER
		FROM layanan.farmasi far
		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOMOR = far.KUNJUNGAN
		LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = kun.NOPEN
		LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
		LEFT JOIN layanan.order_resep ores ON ores.NOMOR = orde.ORDER_ID
		LEFT JOIN inventory.barang bar ON bar.ID = far.FARMASI
		LEFT JOIN inventory.satuan sat ON sat.ID = bar.SATUAN
		LEFT JOIN pendaftaran.tujuan_pasien tuj ON tuj.NOPEN = df.NOMOR
		LEFT JOIN master.dokter dok ON dok.ID = tuj.DOKTER
		LEFT JOIN aplikasi.pengguna peng1 ON peng1.NIP = dok.NIP
		LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = ores.OLEH
		LEFT JOIN inventory.barang bar2 ON bar2.ID = far.FARMASI
		LEFT JOIN master.referensi refsig ON refsig.ID = orde.FREKUENSI_SIGNA AND refsig.JENIS = 109
		LEFT JOIN master.referensi refsigg ON refsigg.ID = orde.KETERANGAN_SIGNA AND refsigg.JENIS = 110
		LEFT JOIN master.referensi refsign ON refsign.ID = orde.JUMLAH_SIGNA AND refsign.JENIS = 107
		LEFT JOIN db_master.variabel japer ON japer.id_variabel = orde.JALUR_PEMBERIAN
		LEFT JOIN ihs.tb_medication med ON med.nopen = kun.NOPEN
		WHERE df.NOMOR = '$nopen'
		GROUP BY bar.ID
		";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function cariPasienYangGagalGetIdIhsPasien()
	{
		$query ="
		SELECT *
		FROM ihs.tb_pasien pas
		WHERE pas.FLAG_MPI=4
		AND pas.ID_IHS IS NULL
		AND pas.JENIS_CEK=0
		AND LENGTH(pas.NORM) BETWEEN 1 AND 6
		";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function serviceRequest($nopen)
	{
		$query = $this->db->query("CALL ihs.LabServiceRequest($nopen)");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function EncounterDataLama()
	{
		$query = $this->db->query("CALL ihs.Encounter_()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function Encounter()
	{
		$query = $this->db->query("CALL ihs.Encounter()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function cariNikPasien()
	{
		$query = $this->db->query("CALL ihs.RequestIHS2()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function cariNikPractitioner()
	{
		$query = $this->db->query("CALL ihs.RequestPractitioner()");
		$res = $query->result_array();
		$query->next_result(); 
		$query->free_result(); 
		return $res;
	}

	public function observationDiagnosticReport($nopen)
	{
		$query = $this->db->query("CALL ihs.LabObservationDiagnosticReport($nopen)");
		$res = $query->result_array();
		$query->next_result();
		$query->free_result();
		return $res;
	}

	public function serviceRequest_old($nopen)
	{
		$query ="
		SELECT
		kun.NOPEN NOPEN,
		t.NAMA TINDAKAN,
		enc.encounter ID_ENCOUNTER,
		kun.NOMOR,
		hl.LIS_HASIL,
		hl.LIS_NAMA_TEST,
		hl.HIS_KODE_TEST,
		IF(hl.LIS_FLAG = '', 'NORMAL', hl.LIS_FLAG) LIS_FLAG,
		p.deskripsi SPECIMEN_DESK,
		maplo.LOINC

		FROM lis.hasil_log hl

		LEFT JOIN pendaftaran.kunjungan kun ON kun.NOMOR = hl.HIS_NO_LAB
		LEFT JOIN layanan.order_lab ol ON ol.NOMOR = kun.REF
		LEFT JOIN master.tindakan t ON t.ID = hl.HIS_KODE_TEST
		LEFT JOIN ihs.tb_encounter enc ON enc.nopen = kun.NOPEN
		LEFT JOIN master.referensi_tindakan_penunjang tp ON tp.tindakan = t.ID
		LEFT JOIN master.referensi_tindakan_penunjang p ON p.id = tp.parent
		LEFT JOIN ihs.tb_mapping_loinc maplo ON hl.HIS_KODE_TEST = maplo.TEST_ID

		WHERE kun.NOPEN = '$nopen'
		AND t.STATUS = 1 AND maplo.TEST_ID IS NOT NULL
		";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function medicationDokter()
	{
		$query = $this->db->query("CALL ihs.medicationDokter()");
		$res = $query->result_array();
		$query->next_result();
		$query->free_result();
		return $res;
	}

// 	public function medicationDokter($nopen)
// 	{
// 		$query ="SELECT bar.ID ID_OBAT
// 		, 'active' KET_STATUS
// 		, IF(mo.code IS NULL, '-', mo.code) CODE_SEDIAAN_SATU_SEHAT
// 		, IF(mo.nama_satusehat IS NULL, '-', mo.nama_satusehat) SEDIAAN_SATUSEHAT
// 		, CASE
// 			WHEN orde.RACIKAN = 0 THEN 'NC'
// 			WHEN orde.RACIKAN = 1 THEN 'SD'
// 			ELSE orde.RACIKAN
// 			END KODE_RACIKAN
// 		, CASE
// 			WHEN orde.RACIKAN = 0 THEN 'Non-compound'
// 			WHEN orde.RACIKAN = 1 THEN 'Gives of such doses'
// 			ELSE orde.RACIKAN
// 			END KODE_RACIKAN_KET
// 		, kun.NOMOR NOKUN
// 		, (SELECT encsub.encounter
// FROM ihs.tb_encounter encsub
// WHERE encsub.nopen = df.NOMOR
// LIMIT 1) ID_ENCOUNTER
// 		, master.getNamaLengkap(df.NORM) NAMA_PASIEN
// 		, ore.TANGGAL TANGGAL_RESEP
// 		, master.getNamaLengkapPegawai(peng2.NIP) CREATED_BY
// 		, IF(mapja.code != '' AND mapja.code IS NOT NULL, mapja.code, '-') KODE_JALUR_PEMBERIAN
// 		, IF(mapja.nama != '' AND mapja.nama IS NOT NULL, mapja.nama, '-') NAMA_JALUR_PEMBERIAN
// 		, bar.NAMA NAMA_OBAT
// 		, bar.KFA_CODE KFA_CODE1
// 		, bar.KFA_CODE2 KFA_CODE2
// 		, bar.KFA_CODE3 KFA_CODE3
// 		, orde.JUMLAH JUMLAH_OBAT
// 		, FLOOR(orde.JUMLAH) JUMLAH_OBAT_INT
// 		, ROUND(orde.JUMLAH, 0) JUMLAH_OBAT_INT2
// 		, refsign.DESKRIPSI ATURAN_PAKAI_X
// 		, refsig.DESKRIPSI ATURAN_PAKAI_HARI
// 		, refsigg.DESKRIPSI ATURAN_PAKAI_LOKASI
// 		, refe.DESKRIPSI ATURAN_PAKAI_TEXT
// 		, df.NORM
// 		, orde.FARMASI ID_FARMASI
// 		, mo.id
// 		, enc.nopen
// FROM ihs.tb_encounter enc

// LEFT JOIN pendaftaran.kunjungan kun ON kun.NOPEN = enc.nopen
// LEFT JOIN master.ruangan r ON r.ID = kun.RUANGAN
// LEFT JOIN layanan.order_resep ore ON ore.NOMOR=kun.REF
// LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR=enc.nopen
// LEFT JOIN layanan.order_detil_resep orde ON orde.ORDER_ID = ore.NOMOR
// LEFT JOIN inventory.barang bar ON bar.ID = orde.FARMASI
// LEFT JOIN inventory.satuan sat ON sat.ID = bar.SATUAN
// LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = ore.OLEH
// LEFT JOIN master.dokter dok ON dok.NIP = peng2.NIP
// LEFT JOIN master.referensi refsig ON refsig.ID = orde.FREKUENSI_SIGNA AND refsig.JENIS = 109
// LEFT JOIN master.referensi refsigg ON refsigg.ID = orde.KETERANGAN_SIGNA AND refsigg.JENIS = 110
// LEFT JOIN master.referensi refsign ON refsign.ID = orde.JUMLAH_SIGNA AND refsign.JENIS = 107
// LEFT JOIN master.referensi bensed ON bensed.ID = bar.BENTUK_SEDIAAN AND bensed.JENIS=118
// LEFT JOIN ihs.mapping_bentuk_obat mo ON mo.id = bensed.ID
// LEFT JOIN db_master.variabel japer ON japer.id_variabel = orde.JALUR_PEMBERIAN
// LEFT JOIN ihs.mapping_jalur_pemberian mapja ON mapja.id_variabel = orde.JALUR_PEMBERIAN
// LEFT JOIN master.referensi refe ON refe.ID = orde.ATURAN_PAKAI AND refe.JENIS=41

// WHERE ore.NOMOR IS NOT NULL AND kun.REF IS NOT NULL 
// AND ore.`STATUS`!=0 
// AND bar.KATEGORI NOT IN ('10210','10102','10103')
// AND (
// SELECT mosub.id
// FROM ihs.mapping_bentuk_obat mosub
// WHERE mosub.id=bensed.ID
// ) IN (3,42)

// AND NOT EXISTS (
// SELECT 1
// FROM ihs.tb_medication md
// WHERE md.jenis = 'REQUEST' AND md.nopen=enc.nopen AND md.id_obat=orde.FARMASI
// )

// #AND enc.encounter='c4ea5ddc-17f7-4e50-a215-66045b66c1d0'
// #AND enc.created_at BETWEEN '2023-07-01 00:00:00' AND NOW()

// ORDER BY enc.nopen DESC
// LIMIT 100";
// 		$bind = $this->db->query($query);
// 		return $bind->result_array();
// 	}

	public function finishEncounter()
	{
		$query = $this->db->query("CALL ihs.PutEncounter()");
		$res = $query->result_array();
		$query->next_result();
		$query->free_result();
		return $res;
	}

	public function cekMedicationDispense()
	{
		$query ="SELECT m.id_medication IDMEDICATION, m.nopen NOPEN
		, m.id_encounter IDENCOUNTER, m.nokun NOKUN
		, pas.ID_IHS ID_IHS_PASIEN
		FROM ihs.tb_medication m
		LEFT JOIN pendaftaran.pendaftaran p ON p.NOMOR = m.nopen
		LEFT JOIN ihs.tb_pasien pas ON pas.NORM = p.NORM
		WHERE m.jenis = 'REQUEST' AND m.nopen NOT in (
			SELECT m2.nopen
			FROM ihs.tb_medication m2
			WHERE m2.jenis = 'DISPENSE')
		GROUP BY m.nopen
		LIMIT 5";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function dataPasien()
	{
		$query = "SELECT pas.NIK NIK, mps.NAMA NAMA_PASIEN, mps.ALAMAT ALAMAT
, master.getTempatLahir(mps.TEMPAT_LAHIR) TEMPAT_LAHIR
, DATE(mps.TANGGAL_LAHIR) TANGGAL_LAHIR
, IF(mps.JENIS_KELAMIN=1,'male', 'female') JENIS_KELAMIN
, pas.NORM
, wilkot.DESKRIPSI KOTA
, wil.ID KODE_PROVINSI
, wilkot.ID KODE_KOTA
, wilkec.ID KODE_KECAMATAN
, wilkel.ID KODE_KELURAHAN
, mps.RT
, mps.RW
FROM ihs.tb_pasien pas
LEFT JOIN master.pasien mps ON mps.NORM = pas.NORM
LEFT JOIN master.wilayah wil ON SUBSTR(mps.WILAYAH,1,2) = wil.ID AND wil.JENIS=1
LEFT JOIN master.wilayah wilkot ON SUBSTR(mps.WILAYAH,1,4) = wilkot.ID AND wilkot.JENIS=2
LEFT JOIN master.wilayah wilkec ON SUBSTR(mps.WILAYAH,1,6) = wilkec.ID AND wilkec.JENIS=3
LEFT JOIN master.wilayah wilkel ON SUBSTR(mps.WILAYAH,1,10) = wilkel.ID AND wilkel.JENIS=4
WHERE pas.ID_IHS IS NULL AND pas.NIK IS NOT NULL
AND trim(replace(replace(replace(REPLACE(pas.NIK,' ',''),'/',''),'.',''),',','')) IS NOT NULL
	AND length(trim(replace(replace(replace
		(replace(pas.NIK,' ',''),'/',''),'.',''),',','')))=16
	#AND ihs.ID_IHS IS NULL
	AND trim(replace(replace(replace(REPLACE(pas.NIK
	,' ',''),'/',''),'.',''),',','')) REGEXP '^[0-9]+$'
	AND pas.NIK NOT LIKE '%00000%'
	AND pas.NIK NOT LIKE '%11111%'
	AND pas.NIK NOT LIKE '%22222%'
	AND pas.NIK NOT LIKE '%33333%'
	AND pas.NIK NOT LIKE '%44444%'
	AND pas.NIK NOT LIKE '%55555%'
	AND pas.NIK NOT LIKE '%66666%'
	AND pas.NIK NOT LIKE '%77777%'
	AND pas.NIK NOT LIKE '%88888%'
	AND pas.NIK NOT LIKE '%99999%'
	AND pas.NIK NOT LIKE '0%'
	AND pas.NIK NOT LIKE '200300%'
	
	AND LENGTH(pas.NORM) BETWEEN 1 AND 6
	AND mps.NAMA NOT LIKE 'Test %'
	AND master.getTempatLahir(mps.TEMPAT_LAHIR)!=''
	#AND pas.FLAG_MPI IN (4)
	AND pas.FLAG_MPI NOT IN (1,2,3,4)
	ORDER BY pas.NORM DESC
	LIMIT 50";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

	public function medicationDispense()
	{
		$query = $this->db->query("CALL ihs.medicationFarmasi()");
		$res = $query->result_array();
		$query->next_result();
		$query->free_result();
		return $res;
	}

	// public function medicationDispense($nopen)
	// {
	// 	$query = "SELECT
	// 					IF(mo.nama_satusehat IS NULL, '-', mo.nama_satusehat) SEDIAAN_SATUSEHAT, 
	// 					IF(mo.code IS NULL, '-', mo.code) CODE_SEDIAAN_SATU_SEHAT,	
	// 					orde.ORDER_ID,
	// 					bar.NAMA NAMA_OBAT,
	// 					CASE
	// 					WHEN bar.STATUS = 1 THEN 'Aktif'
	// 					ELSE 'Tidak Aktif'
	// 					END BENTUK_OBAT,
	// 					sat.DESKRIPSI SATUAN_OBAT,
	// 					sat.NAMA NAMA_SATUAN_OBAT,
	// 					CASE
	// 					WHEN orde.RACIKAN = 0 THEN 'Non-Racikan'
	// 					WHEN orde.RACIKAN = 1 THEN 'Racikan'
	// 					ELSE orde.RACIKAN
	// 					END JENIS_RACIKAN,
	// 					CASE
	// 					WHEN far.STATUS = 2 THEN 'completed'
	// 					ELSE 'Not completed'
	// 					END STATUS,
	// 					'outpatient' KATEGORI,
	// 					'' ID_MEDICATION,
	// 					ktp.NOMOR KTP_PASIEN,
	// 					master.getNamaLengkap(df.NORM) NAMA_PASIEN,
	// 					'' ID_ENC,
	// 					ru.DESKRIPSI RUANGAN,
	// 					far.JUMLAH JUMLAH_OBAT,
	// 					'' ATURAN_PAKAI_SQU,
	// 					far.TANGGAL CREATED_AT,
	// 					japer.variabel JALUR_PEMBERIAN,
	// 					'' DOSE_RANGE_LOW_VALUE,
	// 					'' DOSE_RANGE_HIGH_VALUE,
	// 					'' DOSE_QUANTITY_VALUE,
	// 					'' DOSE_QUANTITY_UNIT,
	// 					IF(bar.KFA_CODE IS NULL, '-', IF(bar.KFA_CODE='', '-', IF(bar.KFA_CODE=' ', '-', bar.KFA_CODE))) KFA_CODE1,
	// 					IF(bar.KFA_CODE2 IS NULL, '-', IF(bar.KFA_CODE2='', '-', IF(bar.KFA_CODE2=' ', '-', bar.KFA_CODE2))) KFA_CODE2,
	// 					IF(bar.KFA_CODE3 IS NULL, '-', IF(bar.KFA_CODE3='', '-', IF(bar.KFA_CODE3=' ', '-', bar.KFA_CODE3))) KFA_CODE3,
	// 					IF(bar.DOSIS IS NOT NULL, bar.DOSIS, '0') DOSIS_TERKECIL,
	// 					bar.JUMLAH_KONVERSI JUMLAH_KONVERSI,
	// 					sat.ID ID_SATUAN,
	// 					satk.NAMA NAMA_KONVERSI,
	// 					satk.ID ID_KONVERSI,
	// 					far.ID IDFARMASI,
	// 					loc.id_ihs ID_IHS_RUANGAN,
	// 					loc.description NAMA_IHS_RUANGAN,
	// 					refe.DESKRIPSI ATURAN_PAKAI_TEXT
						
	// 					FROM layanan.farmasi far
						
	// 					LEFT JOIN pendaftaran.kunjungan kun ON kun.NOMOR = far.KUNJUNGAN
	// 					LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = kun.NOPEN
	// 					LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
	// 					LEFT JOIN layanan.order_resep ores ON ores.NOMOR = orde.ORDER_ID
	// 					LEFT JOIN inventory.barang bar ON bar.ID = far.FARMASI
	// 					LEFT JOIN inventory.satuan sat ON sat.ID = bar.SATUAN
	// 					LEFT JOIN inventory.satuan satk ON satk.ID = bar.SATUAN_KONVERSI
	// 					LEFT JOIN pendaftaran.tujuan_pasien tuj ON tuj.NOPEN = df.NOMOR
	// 					LEFT JOIN master.dokter dok ON dok.ID = tuj.DOKTER
	// 					LEFT JOIN aplikasi.pengguna peng1 ON peng1.NIP = dok.NIP
	// 					LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = ores.OLEH
	// 					LEFT JOIN inventory.barang bar2 ON bar2.ID = far.FARMASI
	// 					LEFT JOIN master.referensi refsig ON refsig.ID = orde.FREKUENSI_SIGNA AND refsig.JENIS = 109
	// 					LEFT JOIN master.referensi refsigg ON refsigg.ID = orde.KETERANGAN_SIGNA AND refsigg.JENIS = 110
	// 					LEFT JOIN master.referensi refsign ON refsign.ID = orde.JUMLAH_SIGNA AND refsign.JENIS = 107
	// 					LEFT JOIN master.referensi bensed ON bensed.ID = IF(bar.NAMA IS NULL, bar2.BENTUK_SEDIAAN, bar.BENTUK_SEDIAAN) AND bensed.JENIS=118
	// 					LEFT JOIN master.referensi refe ON refe.ID = orde.ATURAN_PAKAI AND refe.JENIS=41
	// 					LEFT JOIN ihs.mapping_bentuk_obat mo ON mo.id = bensed.ID
	// 					LEFT JOIN db_master.variabel japer ON japer.id_variabel = orde.JALUR_PEMBERIAN
	// 					LEFT JOIN master.kartu_identitas_pasien ktp ON ktp.NORM = df.NORM AND ktp.JENIS = 1
	// 					LEFT JOIN inventory.barang_ruangan baru ON baru.ID = far.STOK
	// 					LEFT JOIN master.ruangan ru ON ru.ID = kun.RUANGAN
	// 					LEFT JOIN ihs.tb_location loc ON loc.id_ruangan = ru.ID
						
	// 					WHERE df.NOMOR = '$nopen'
	// 					AND far.STATUS = 2
	// 					AND mo.code IS NOT NULL";

	// 		$bind = $this->db->query($query);
	// 		return $bind->result_array();
	// }

	// public function medicationDispense($nopen)
	// {
	// 	$query = "SELECT master.getNamaLengkap(df.NORM) NAMA_PASIEN,
	// 						bar.NAMA NAMA_OBAT, orde.ORDER_ID, kun.NOPEN,
	// 						CASE
	// 						WHEN bar.STATUS = 1 THEN 'Aktif'
	// 						ELSE 'Tidak Aktif'
	// 						END STATUS_OBAT,
	// 						sat.DESKRIPSI SATUAN_OBAT,
	// 						CASE
	// 						WHEN orde.RACIKAN = 0 THEN 'Non-Racikan'
	// 						WHEN orde.RACIKAN = 1 THEN 'Racikan'
	// 						ELSE orde.RACIKAN
	// 						END RACIKAN
							
	// 						FROM layanan.farmasi far
							
	// 						LEFT JOIN inventory.barang bar ON bar.ID = far.FARMASI
	// 						LEFT JOIN pendaftaran.kunjungan kun ON kun.NOMOR = far.KUNJUNGAN
	// 						LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = kun.NOPEN
	// 						LEFT JOIN inventory.satuan sat ON sat.ID = bar.SATUAN
	// 						LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
							
	// 						WHERE kun.NOPEN = '$nopen' ";

	// 	$bind = $this->db->query($query);
	// 	return $bind->result_array();
	// }

	// public function medicationDispense2($nopen)
	// {
	// 	$query = "SELECT
	// 						CASE
	// 						WHEN far.STATUS = 2 THEN 'completed'
	// 						ELSE 'Not completed'
	// 						END STATUS,
	// 						'outpatient' KATEGORI,
	// 						'' ID_MEDICATION,
	// 						ktp.NOMOR KTP_PASIEN,
	// 						master.getNamaLengkap(df.NORM) NAMA_PASIEN,
	// 						'' ID_ENC,
	// 						ru.DESKRIPSI RUANGAN,
	// 						far.JUMLAH TOTAL_JUMLAH_OBAT,
	// 						'' ATURAN_PAKAI_SQU,
	// 						far.TANGGAL CREATED_AT,
	// 						japer.variabel JALUR_PEMBERIAN,
	// 						'' DOSE_RANGE_LOW_VALUE,
	// 						'' DOSE_RANGE_HIGH_VALUE,
	// 						'' DOSE_QUANTITY_VALUE,
	// 						'' DOSE_QUANTITY_UNIT,
	// 						orde.ORDER_ID
							
	// 						FROM layanan.farmasi far
							
	// 						LEFT JOIN pendaftaran.kunjungan kun ON kun.NOMOR = far.KUNJUNGAN
	// 						LEFT JOIN pendaftaran.pendaftaran df ON df.NOMOR = kun.NOPEN
	// 						LEFT JOIN layanan.order_detil_resep orde ON orde.REF = far.ID
	// 						LEFT JOIN layanan.order_resep ores ON ores.NOMOR = orde.ORDER_ID
	// 						LEFT JOIN inventory.barang bar ON bar.ID = far.FARMASI
	// 						LEFT JOIN inventory.satuan sat ON sat.ID = bar.SATUAN
	// 						LEFT JOIN pendaftaran.tujuan_pasien tuj ON tuj.NOPEN = df.NOMOR
	// 						LEFT JOIN master.dokter dok ON dok.ID = tuj.DOKTER
	// 						LEFT JOIN aplikasi.pengguna peng1 ON peng1.NIP = dok.NIP
	// 						LEFT JOIN aplikasi.pengguna peng2 ON peng2.ID = ores.OLEH
	// 						LEFT JOIN inventory.barang bar2 ON bar2.ID = far.FARMASI
	// 						LEFT JOIN master.referensi refsig ON refsig.ID = orde.FREKUENSI_SIGNA AND refsig.JENIS = 109
	// 						LEFT JOIN master.referensi refsigg ON refsigg.ID = orde.KETERANGAN_SIGNA AND refsigg.JENIS = 110
	// 						LEFT JOIN master.referensi refsign ON refsign.ID = orde.JUMLAH_SIGNA AND refsign.JENIS = 107
	// 						LEFT JOIN db_master.variabel japer ON japer.id_variabel = orde.JALUR_PEMBERIAN
	// 						LEFT JOIN master.kartu_identitas_pasien ktp ON ktp.NORM = df.NORM AND ktp.JENIS = 1
	// 						LEFT JOIN inventory.barang_ruangan baru ON baru.ID = far.STOK
	// 						LEFT JOIN master.ruangan ru ON ru.ID = baru.RUANGAN
							
	// 						WHERE df.NOMOR = '$nopen'
	// 						AND far.STATUS = 2";

	// 		$bind = $this->db->query($query);
	// 		return $bind->result_array();
	// }

	function sendServiceRequestLab()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_lab_rajal dsrlr
				WHERE dsrlr.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendSpecimenLab()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_specimen_lab_rajal dslr
				WHERE dslr.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function cekPacs($id)
	{
		$accs = '%'.$id;
		$query = "SELECT st.pk, pp.pat_name, pp.pat_id, st.study_desc, st.study_datetime
					, st.study_iuid, st.mods_in_study, st.accession_no, f.filepath
					FROM pacsdb.study st
					LEFT JOIN pacsdb.patient pp ON st.patient_fk=pp.pk
					LEFT JOIN pacsdb.series sr ON sr.study_fk=st.pk
					LEFT JOIN pacsdb.instance i ON i.series_fk=sr.pk
					LEFT JOIN pacsdb.files f ON f.instance_fk = i.pk
					WHERE st.accession_no LIKE '$accs'
					ORDER BY st.study_datetime DESC LIMIT 1";
		$bind = $this->dbpacs->query($query);
		return $bind;
	}

	function getPacs()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_rad_rajal dsrr
				WHERE dsrr.STATUS = '1'
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendServiceRequestRad()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_rad_rajal dsrr
				WHERE dsrr.STATUS = '2' AND dsrr.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendDicom()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_rad_rajal dsrr
				WHERE dsrr.HTTPCODE = '201' AND dsrr.STATUS = '3'
				LIMIT 5";
		$bind = $this->db->query($query);
		return $bind;
	}

	function getImagingStudyRad()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_service_request_rad_rajal dsrr
				WHERE dsrr.HTTPCODE = '201' AND dsrr.STATUS = '4'
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendObservationRad()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_observation_radiologi dor
				WHERE dor.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

	function sendDiagnosticReportRad()
	{
		$query = "SELECT
					* 
				FROM
					data_ihs.data_diagnostic_report_radiologi drr
				WHERE drr.HTTPCODE IS NULL
				LIMIT 100";
		$bind = $this->db->query($query);
		return $bind;
	}

}
