<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Encrypt Library for CodeIgniter
 *
 * Provides AES-256-CBC encryption and decryption functionality
 * with HMAC signature verification to prevent padding oracle attacks
 *
 * @package    CodeIgniter
 * @subpackage Libraries
 * @category   Encryption
 * <AUTHOR> Name
 * @version    1.0
 */
class encrypt {

	/**
	 * CodeIgniter instance
	 * @var object
	 */
	protected $CI;

	/**
	 * Constructor
	 */
	public function __construct()
	{
		$this->CI =& get_instance();
		log_message('info', 'Encrypt Library Initialized');
	}

	/**
	 * Encrypt data using AES-256-CBC
	 *
	 * @param string $data The data to encrypt
	 * @param string $key The encryption key (hex format)
	 * @return string The encrypted data (base64 encoded)
	 * @throws Exception If key length is invalid
	 */
	public function encrypt($data, $key)
	{
		// make binary representation of $key
		$key = hex2bin($key);

		// check key length, must be 256 bit or 32 bytes
		if(mb_strlen($key, "8bit") !== 32) {
			throw new Exception("Needs a 256-bit key!");
		}

		// create initialization vector
		$iv_size = openssl_cipher_iv_length("aes-256-cbc");

		// Use random_bytes if available (PHP 7+), otherwise fallback to openssl
		if (function_exists('random_bytes')) {
			$iv = random_bytes($iv_size);
		} else {
			$iv = openssl_random_pseudo_bytes($iv_size);
		}

		// encrypt
		$encrypted = openssl_encrypt($data, "aes-256-cbc", $key, OPENSSL_RAW_DATA, $iv);

		// create signature, against padding oracle attacks
		$signature = mb_substr(hash_hmac("sha256", $encrypted, $key, true), 0, 10, "8bit");

		$encoded = chunk_split(base64_encode($signature.$iv.$encrypted));

		return $encoded;
	}

	/**
	 * Decrypt data using AES-256-CBC
	 *
	 * @param string $str The encrypted data to decrypt
	 * @param string $key The decryption key (hex format)
	 * @return string|bool The decrypted data or FALSE on failure
	 * @throws Exception If key length is invalid
	 */
	public function decrypt($str, $key)
	{
		// make binary representation of $key
		$key = hex2bin($key);

		// check key length, must be 256 bit or 32 bytes
		if(mb_strlen($key, "8bit") !== 32) {
			throw new Exception("Needs a 256-bit key!");
		}

		// calculate iv size
		$iv_size = openssl_cipher_iv_length("aes-256-cbc");

		// breakdown parts
		$str = str_replace("----BEGIN ENCRYPTED DATA----", "", $str);
		$str = str_replace("----END ENCRYPTED DATA----", "", $str);
		$decoded = base64_decode($str);

		$signature = mb_substr($decoded, 0, 10, "8bit");
		$iv = mb_substr($decoded, 10, $iv_size, "8bit");
		$encrypted = mb_substr($decoded, $iv_size+10, null, "8bit");

		// check signature, against padding oracle attack
		$calc_signature = mb_substr(hash_hmac("sha256", $encrypted, $key, true), 0, 10, "8bit");

		if(!$this->compare($signature, $calc_signature)) {
			return "SIGNATURE_NOT_MATCH"; // signature doesn't match
		}

		$decrypted = openssl_decrypt($encrypted, "aes-256-cbc", $key, OPENSSL_RAW_DATA, $iv);

		return $decrypted;
	}

	/**
	 * Secure string comparison to prevent timing attacks
	 *
	 * @param string $a First string
	 * @param string $b Second string
	 * @return bool TRUE if strings match, FALSE otherwise
	 */
	public function compare($a, $b)
	{
		// compare individually to prevent timing attacks

		// compare length
		if(strlen($a) !== strlen($b)) return false;

		// compare individual
		$result = 0;
		for($i = 0; $i < strlen($a); $i++) {
			$result |= ord($a[$i]) ^ ord($b[$i]);
		}

		return $result == 0;
	}
}
?>