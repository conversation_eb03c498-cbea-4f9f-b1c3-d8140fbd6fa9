<h1>Pengiriman Data <PERSON><PERSON></h1>

<div class="row">
	<div class="col-6" style="background-color: #FED8B1;">
		<h4>Log : </h4>
		<?php 
$data = '{
    "resourceType": "Observation",
    "status": "final",
    "category": [
        {
            "coding": [
                {
                    "system": "http://terminology.hl7.org/CodeSystem/observation-category",
                    "code": "vital-signs",
                    "display": "Vital Signs"
                }
            ]
        }
    ],
    "code": {
        "coding": [
            {
                "system": "http://loinc.org",
                "code": "8867-4",
                "display": "Heart rate"
            }
        ]
    },
    "performer": [
        {
            "reference": "Practitioner/**********"
        }
    ],
    "subject": {
        "reference": "Patient/P02185434517",
        "display": "IRSYAD FADLURRAHMAN"
    },
    "encounter": {
        "reference": "Encounter/4e9e6834-8eaa-4eb3-8076-1220cf60bb0b"
    },
    "effectiveDateTime": "2022-11-08T10:10:00+00:00",
    "issued": "2022-11-08T10:10:00+00:00",
    "valueQuantity": {
        "value": 70,
        "unit": "beats/minute",
        "system": "http://unitsofmeasure.org",
        "code": "/min"
    }
}';
				echo '<pre>' . $data . '</pre>';
			?>
	</div>
	<div class="col-6" style="background-color: #8FB31D;">
		<h4>Response : </h4>
		<?php 
$data = '{
    "category": [
        {
            "coding": [
                {
                    "code": "vital-signs",
                    "display": "Vital Signs",
                    "system": "http://terminology.hl7.org/CodeSystem/observation-category"
                }
            ]
        }
    ],
    "code": {
        "coding": [
            {
                "code": "8867-4",
                "display": "Heart rate",
                "system": "http://loinc.org"
            }
        ]
    },
    "effectiveDateTime": "2022-11-08T10:10:00+00:00",
    "encounter": {
        "reference": "Encounter/4e9e6834-8eaa-4eb3-8076-1220cf60bb0b"
    },
    "id": "d8c26f12-9a0f-4298-9a2c-1657885a63c2",
    "identifier": [],
    "issued": "2022-11-08T10:10:00+00:00",
    "meta": {
        "lastUpdated": "2024-01-18T06:15:57.774722+00:00",
        "versionId": "MTcwNTU1ODU1Nzc3NDcyMjAwMA"
    },
    "performer": [
        {
            "reference": "Practitioner/**********"
        }
    ],
    "resourceType": "Observation",
    "status": "final",
    "subject": {
        "display": "IRSYAD FADLURRAHMAN",
        "reference": "Patient/P02185434517"
    },
    "valueQuantity": {
        "code": "/min",
        "system": "http://unitsofmeasure.org",
        "unit": "beats/minute",
        "value": 70
    }
}';
				echo '<pre>' . $data . '</pre>';
			?>
	</div>
</div>