<?php
defined('BASEPATH') or exit('No direct script access allowed');

class IdrgModel extends CI_Model
{

    function __construct(){
		parent::__construct();
        $this->load->database();
	}


    public function insertToken($data)
    {
        $this->db->insert('log.token_satset', $data);
    }

    public function dataNewClaim($nopen)
	{
		$query = "SELECT * FROM medicarecord.bridging_klaim WHERE NOPEN = '$nopen' AND JENIS_BRIDGING=1";
		$bind = $this->db->query($query);
		return $bind->result_array();
	}

}