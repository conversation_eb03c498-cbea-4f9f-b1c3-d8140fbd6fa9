<div class="modal-header">
	<h5 class="modal-title">Cari Loinc dengan Nama Param : <span class="text-danger"><?=$nama;?></span></h5>
	<button type="button" class="close" data-dismiss="modal">&times;</button>
</div>
<form id="formCariLoinc" autocomplete="off">
	<div class="modal-body">
		<div class="container-fluid">
			
			<div class="row mt-2">
				<div class="col-md-12">
						<div class="table-responsive">
							<table id="tableMappingLoincAmbil" class="table table-striped table-bordered table-hover" width="100%">
							<thead>
								<tr>
									<th>KATEGORI</th>
									<th>NAMA PEMERIKSAAN</th>
									<th>PERMINTAAN</th>
									<th>SPESIMEN</th>
									<th>TIPE HASIL PEMERIKSAAN</th>
									<th>SATUAN</th>
									<th>METODE ANALISA</th>
									<th>CODE</th>
									<th>DISPLAY</th>
									<th>COMPONENT</th>
									<th>PROPERTY</th>
									<th>TIMING</th>
									<th>SYSTEM</th>
									<th>SCALE</th>
									<th>METHOD</th>
									<th>UNIT OF MEASURE</th>
									<th>ACTION</th>
								</tr>
							</thead>
							<tbody>
								
							</tbody>
							<tfoot>
								<tr>
									<th>KATEGORI</th>
									<th>NAMA PEMERIKSAAN</th>
									<th>PERMINTAAN</th>
									<th>SPESIMEN</th>
									<th>TIPE HASIL PEMERIKSAAN</th>
									<th>SATUAN</th>
									<th>METODE ANALISA</th>
									<th>CODE</th>
									<th>DISPLAY</th>
									<th>COMPONENT</th>
									<th>PROPERTY</th>
									<th>TIMING</th>
									<th>SYSTEM</th>
									<th>SCALE</th>
									<th>METHOD</th>
									<th>UNIT OF MEASURE</th>
									<th>ACTION</th>
								</tr>
							</tfoot>
						</table>
						</div>
				</div>
			</div>

		</div>
	</div>
	<div class="modal-footer">
		<a href="#" class="btn btn-secondary" data-toggle="modal" data-dismiss="modal"><i class="fa fa-times-circle"></i> Tutup
		</a>
	</div>
</form>

<script>
	$(document).ready(function(){
		// $('#tableMappingLoincAmbil').DataTable({
		// 	"ordering": false,
		// 	"lengthMenu": [[5, 10, 20, -1], [5, 10, 20, 'Todos']],
		// });

		$('#tableMappingLoincAmbil').DataTable({
		"responsive": true,
		"pageLength" : 5,
		"processing": true,
		"serverSide": true,
		"bLengthChange": true,
		"ordering": false,
		"order": [],
		"language": {
			"processing": 'Memuat Data...',
			"zeroRecords": "Data Tidak Ditemukan",
			"emptyTable": "Data Tidak Tersedia",
			"loadingRecords": "Harap Tunggu...",
			"paginate": {
				"next":       "Selanjutnya",
				"previous":   "Sebelumnya"
			},
			"info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
			"infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
			"search": "Cari:",
			"lengthMenu": "Tampilkan: _MENU_ Data",
			"infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
		},
		
		lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
		ajax: {
			url: '<?php echo base_url('IHS/getDataLoinc')?>',
			type: 'POST',
			data  : {
					id:'<?=$id?>',
				},
		},
	});

		$('#tableMappingLoincAmbil').on('click', '.ambilLoinc', function () {
			var id = $(this).data('id');
			var code = $(this).data('code');
			var display = $(this).data('display');
			$.ajax({
				type  : 'POST',
				url   : '<?php echo base_url() ?>IHS/ambilLoinc',
				data  : {
					id:id,
					code:code,
				},
				success : function(data){
					$('.codeTextLoinc_' + id).text(code);
					$('.displayTextLoinc_' + id).text(display);
					$('.statusLoinc_' + id).text('SUDAH MAPPING');
					$('#barisMappingLoinc_' + id).attr('class', 'table-success');
					alertify.success('Data Berhasil Mapping');
				}
			});
			
		});

	});
</script>